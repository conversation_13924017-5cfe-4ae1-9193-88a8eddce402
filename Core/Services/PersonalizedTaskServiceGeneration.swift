import Foundation

// MARK: - Personalized Task Service Generation Methods
// Extracted task generation methods to reduce main class size

@available(iOS 18.0, *)
extension PersonalizedTaskService {
    // MARK: - Task Generation Methods
    func generateCognitiveLoadBasedTasks(
        cognitiveLoad: CognitiveLoadLevel,
        userProfile: UserProfile,
        userContext: UserContext
    ) async -> [PersonalizedContent] {
        var tasks: [PersonalizedContent] = []
        
        switch cognitiveLoad {
        case .low:
            // User can handle complex tasks
            tasks.append(PersonalizedContent(
                type: .taskGuidance,
                title: "Complex Problem Solving",
                content: "Your cognitive load is low - perfect time for challenging tasks that require deep focus.",
                priority: .high,
                targetAudience: userProfile.neurodiversityProfile.types
            ))
            
        case .medium:
            // Moderate complexity tasks
            tasks.append(PersonalizedContent(
                type: .taskGuidance,
                title: "Structured Tasks",
                content: "Your cognitive load is moderate - focus on well-structured tasks with clear steps.",
                priority: .medium,
                targetAudience: userProfile.neurodiversityProfile.types
            ))
            
        case .high:
            // Simple, low-demand tasks
            tasks.append(PersonalizedContent(
                type: .taskGuidance,
                title: "Simple Maintenance Tasks",
                content: "Your cognitive load is high - stick to simple, routine tasks that don't require heavy thinking.",
                priority: .low,
                targetAudience: userProfile.neurodiversityProfile.types
            ))
        }
        
        return tasks
    }
    
    func generateExecutiveFunctionBasedTasks(
        insights: ExecutiveFunctionInsights,
        userProfile: UserProfile,
        userContext: UserContext
    ) async -> [PersonalizedContent] {
        var tasks: [PersonalizedContent] = []
        
        // Generate tasks based on executive function strengths and weaknesses
        if insights.planningSkills < 0.5 {
            tasks.append(PersonalizedContent(
                type: .taskGuidance,
                title: "Planning Support",
                content: "Break down your tasks into smaller, manageable steps. Use visual planning tools.",
                priority: .high,
                targetAudience: userProfile.neurodiversityProfile.types
            ))
        }
        
        if insights.workingMemory < 0.5 {
            tasks.append(PersonalizedContent(
                type: .taskGuidance,
                title: "Memory Support",
                content: "Write down important information and use external memory aids like notes and reminders.",
                priority: .medium,
                targetAudience: userProfile.neurodiversityProfile.types
            ))
        }
        
        if insights.cognitiveFlexibility < 0.5 {
            tasks.append(PersonalizedContent(
                type: .taskGuidance,
                title: "Routine Structure",
                content: "Stick to familiar routines and gradually introduce small changes when ready.",
                priority: .medium,
                targetAudience: userProfile.neurodiversityProfile.types
            ))
        }
        
        return tasks
    }
    
    func generateTimeContextBasedTasks(
        timeContext: TimeContext,
        userProfile: UserProfile
    ) async -> [PersonalizedContent] {
        let currentHour = Calendar.current.component(.hour, from: Date())
        
        switch currentHour {
        case 6...9:
            return await generateMorningTasks(userProfile: userProfile)
        case 10...12:
            return await generateMidMorningTasks(userProfile: userProfile)
        case 13...15:
            return await generateAfternoonTasks(userProfile: userProfile)
        case 16...18:
            return await generateLateAfternoonTasks(userProfile: userProfile)
        case 19...22:
            return await generateEveningTasks(userProfile: userProfile)
        default:
            return await generateNightTasks(userProfile: userProfile)
        }
    }
    
    // MARK: - Time-Specific Task Generation
    
    private func generateMorningTasks(userProfile: UserProfile) async -> [PersonalizedContent] {
        [
            PersonalizedContent(
                type: .taskGuidance,
                title: "Morning Energy Optimization",
                content: "Morning is ideal for high-focus tasks. Take advantage of your fresh mental energy.",
                priority: .high,
                targetAudience: userProfile.neurodiversityProfile.types
            )
        ]
    }
    
    private func generateMidMorningTasks(userProfile: UserProfile) async -> [PersonalizedContent] {
        [
            PersonalizedContent(
                type: .taskGuidance,
                title: "Peak Performance Window",
                content: "This is typically your peak performance time. Tackle your most important tasks now.",
                priority: .high,
                targetAudience: userProfile.neurodiversityProfile.types
            )
        ]
    }
    
    private func generateAfternoonTasks(userProfile: UserProfile) async -> [PersonalizedContent] {
        [
            PersonalizedContent(
                type: .taskGuidance,
                title: "Post-Lunch Focus",
                content: "Energy may dip after lunch. Consider lighter tasks or take a brief break first.",
                priority: .medium,
                targetAudience: userProfile.neurodiversityProfile.types
            )
        ]
    }
    
    private func generateLateAfternoonTasks(userProfile: UserProfile) async -> [PersonalizedContent] {
        [
            PersonalizedContent(
                type: .taskGuidance,
                title: "Afternoon Productivity",
                content: "Good time for collaborative tasks and meetings. Energy is stable but not peak.",
                priority: .medium,
                targetAudience: userProfile.neurodiversityProfile.types
            )
        ]
    }
    
    private func generateEveningTasks(userProfile: UserProfile) async -> [PersonalizedContent] {
        [
            PersonalizedContent(
                type: .taskGuidance,
                title: "Evening Wind-Down",
                content: "Evening is great for reflection, planning, and lighter tasks. Avoid overstimulation.",
                priority: .low,
                targetAudience: userProfile.neurodiversityProfile.types
            )
        ]
    }
    
    private func generateNightTasks(userProfile: UserProfile) async -> [PersonalizedContent] {
        [
            PersonalizedContent(
                type: .taskGuidance,
                title: "Night Rest Preparation",
                content: "Focus on calming activities and prepare for rest. Avoid stimulating tasks.",
                priority: .low,
                targetAudience: userProfile.neurodiversityProfile.types
            )
        ]
    }
    
    // MARK: - Task Adaptation Helpers
    
    func adaptTaskForCognitiveLoad(task: AITask, cognitiveLoad: CognitiveLoadLevel) async -> AITask {
        var adaptedTask = task
        
        switch cognitiveLoad {
        case .low:
            // Can handle more complexity
            adaptedTask.complexity = min(task.complexity + 1, 5)
            adaptedTask.estimatedDuration = task.estimatedDuration * 1.1
            
        case .medium:
            // Keep as is but add structure
            adaptedTask.requiresBreaks = task.estimatedDuration > 1_800 // 30 minutes
            
        case .high:
            // Simplify significantly
            adaptedTask.complexity = max(task.complexity - 2, 1)
            adaptedTask.estimatedDuration = task.estimatedDuration * 0.7
            adaptedTask.requiresBreaks = true
            adaptedTask.breakInterval = 900 // 15 minutes
        }
        
        return adaptedTask
    }
    
    func adaptTaskForNeurodiversity(task: AITask, profile: NeurodiversityProfile) async -> AITask {
        var adaptedTask = task
        
        // Adapt based on primary neurodiversity type
        switch profile.primaryType {
        case .adhd:
            adaptedTask = await adaptTaskForADHD(task: adaptedTask)
        case .autism:
            adaptedTask = await adaptTaskForAutism(task: adaptedTask)
        case .anxiety:
            adaptedTask = await adaptTaskForAnxiety(task: adaptedTask)
        case .depression:
            adaptedTask = await adaptTaskForDepression(task: adaptedTask)
        }
        
        return adaptedTask
    }
    
    func adaptTaskForPreferences(task: AITask, preferences: UserPreferences) async -> AITask {
        var adaptedTask = task
        
        // Adapt timing based on preferences
        if let preferredTimes = preferences.preferredWorkingHours {
            adaptedTask.suggestedStartTime = findOptimalTimeSlot(
                preferredTimes: preferredTimes,
                duration: task.estimatedDuration
            )
        }
        
        // Adapt environment based on preferences
        if preferences.requiresQuietEnvironment {
            adaptedTask.environmentRequirements.append("Quiet environment")
        }
        
        if preferences.prefersVisualAids {
            adaptedTask.visualAidsRecommended = true
        }
        
        return adaptedTask
    }
    
    // MARK: - Neurodiversity-Specific Adaptations
    
    private func adaptTaskForADHD(task: AITask) async -> AITask {
        var adaptedTask = task
        
        // Break into smaller chunks
        if task.estimatedDuration > 1_500 { // 25 minutes
            adaptedTask.requiresBreaks = true
            adaptedTask.breakInterval = 1_500
        }
        
        // Add more structure
        adaptedTask.requiresDetailedSteps = true
        adaptedTask.allowsMovement = true
        
        return adaptedTask
    }
    
    private func adaptTaskForAutism(task: AITask) async -> AITask {
        var adaptedTask = task
        
        // Add predictability
        adaptedTask.requiresDetailedSteps = true
        adaptedTask.estimatedDuration = task.estimatedDuration * 1.2 // Buffer time
        
        // Sensory considerations
        adaptedTask.environmentRequirements.append("Consistent, predictable environment")
        adaptedTask.sensoryConsiderations = true
        
        return adaptedTask
    }
    
    private func adaptTaskForAnxiety(task: AITask) async -> AITask {
        var adaptedTask = task
        
        // Reduce pressure
        adaptedTask.allowsFlexibleTiming = true
        adaptedTask.includesCalming = true
        
        // Add support
        adaptedTask.requiresEncouragement = true
        
        return adaptedTask
    }
    
    private func adaptTaskForDepression(task: AITask) async -> AITask {
        var adaptedTask = task
        
        // Make more manageable
        adaptedTask.complexity = max(task.complexity - 1, 1)
        adaptedTask.includesPositiveReinforcement = true
        
        return adaptedTask
    }
    
    // MARK: - Helper Methods
    
    private func findOptimalTimeSlot(preferredTimes: [Int], duration: TimeInterval) -> Date {
        let calendar = Calendar.current
        let now = Date()
        let currentHour = calendar.component(.hour, from: now)
        
        // Find next preferred time slot
        for hour in preferredTimes where hour >= currentHour {
            return calendar.date(bySettingHour: hour, minute: 0, second: 0, of: now) ?? now
        }
        
        // If no time today, use first preferred time tomorrow
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: now) ?? now
        let firstPreferredHour = preferredTimes.first ?? currentHour
        return calendar.date(bySettingHour: firstPreferredHour, minute: 0, second: 0, of: tomorrow) ?? now
    }
}
