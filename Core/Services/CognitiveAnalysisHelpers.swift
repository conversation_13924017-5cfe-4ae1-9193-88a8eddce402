import Foundation
import SwiftUI

// MARK: - Cognitive Analysis Helper Methods

@available(iOS 18.0, *)
extension CognitiveAnalysisService {
    // MARK: - Data Processing Helpers
    
    func processTaskPerformanceData(_ completions: [TaskCompletion]) -> TaskPerformanceMetrics {
        let totalTasks = completions.count
        let completedTasks = completions.filter { $0.status == .completed }.count
        let averageTime = completions.compactMap { $0.duration }.reduce(0, +) / Double(max(completions.count, 1))
        
        return TaskPerformanceMetrics(
            totalTasks: totalTasks,
            completedTasks: completedTasks,
            completionRate: Double(completedTasks) / Double(max(totalTasks, 1)),
            averageCompletionTime: averageTime,
            efficiency: calculateEfficiency(from: completions)
        )
    }
    
    func calculateEfficiency(from completions: [TaskCompletion]) -> Double {
        guard !completions.isEmpty else { return 0.0 }
        
        let successfulCompletions = completions.filter { $0.status == .completed }
        let totalTime = completions.compactMap { $0.duration }.reduce(0, +)
        let successfulTime = successfulCompletions.compactMap { $0.duration }.reduce(0, +)
        
        return totalTime > 0 ? successfulTime / totalTime : 0.0
    }
    
    func analyzeCognitiveLoadPatterns(_ entries: [CognitiveLoadEntry]) -> CognitiveLoadAnalysis {
        guard !entries.isEmpty else {
            return CognitiveLoadAnalysis(
                averageLoad: 0.0,
                peakLoad: 0.0,
                patterns: [],
                recommendations: []
            )
        }
        
        let loads = entries.map { $0.level.rawValue }
        let averageLoad = loads.reduce(0, +) / Double(loads.count)
        let peakLoad = loads.max() ?? 0.0
        
        return CognitiveLoadAnalysis(
            averageLoad: averageLoad,
            peakLoad: peakLoad,
            patterns: identifyLoadPatterns(entries),
            recommendations: generateLoadRecommendations(averageLoad: averageLoad, peakLoad: peakLoad)
        )
    }
    
    func identifyLoadPatterns(_ entries: [CognitiveLoadEntry]) -> [CognitiveLoadPattern] {
        var patterns: [CognitiveLoadPattern] = []
        
        // Identify high load periods
        let highLoadEntries = entries.filter { $0.level.rawValue > 0.7 }
        if !highLoadEntries.isEmpty {
            patterns.append(.highLoadPeriods(count: highLoadEntries.count))
        }
        
        // Identify time-based patterns
        let hourlyDistribution = Dictionary(grouping: entries) { entry in
            Calendar.current.component(.hour, from: entry.timestamp)
        }
        
        if let peakHour = hourlyDistribution.max(by: { $0.value.count < $1.value.count })?.key {
            patterns.append(.timeBasedPattern(peakHour: peakHour))
        }
        
        return patterns
    }
    
    func generateLoadRecommendations(averageLoad: Double, peakLoad: Double) -> [String] {
        var recommendations: [String] = []
        
        if averageLoad > 0.6 {
            recommendations.append("Consider taking more frequent breaks to manage cognitive load")
        }
        
        if peakLoad > 0.8 {
            recommendations.append("Break down complex tasks into smaller, manageable chunks")
        }
        
        if averageLoad < 0.3 {
            recommendations.append("You may be able to take on more challenging tasks")
        }
        
        return recommendations
    }
}
