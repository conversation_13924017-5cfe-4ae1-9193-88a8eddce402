import CloudKit
import Combine
import Foundation

// MARK: - User Service Protocol

@available(iOS 18.0, *)
public protocol UserServiceProtocol {
    func getCurrentProfile() async throws -> UserProfile
    func updateProfile(_ profile: UserProfile) async throws
    func createProfile(_ profile: UserProfile) async throws
    func deleteProfile() async throws
    func clearUserData() async throws
    func syncWithCloud() async throws
}

// MARK: - User Service Implementation

@available(iOS 18.0, *)
@MainActor
public class UserService: UserServiceProtocol {
    // MARK: - Dependencies
    private let coreDataService: CoreDataServiceProtocol
    private let cloudKitService: CloudKitServiceProtocol
    private let userDefaults: UserDefaults

    // MARK: - Current User Profile
    @Published private(set) var currentProfile: UserProfile?

    // MARK: - Keys
    private enum Keys {
        static let currentUserProfile = "neuronexa_current_user_profile"
        static let hasCreatedProfile = "neuronexa_has_created_profile"
    }

    // MARK: - Initialization

    init(
        coreDataService: CoreDataServiceProtocol = DependencyContainer.shared.coreDataService,
        cloudKitService: CloudKitServiceProtocol = DependencyContainer.shared.cloudKitService,
        userDefaults: UserDefaults = .standard
    ) {
        self.coreDataService = coreDataService
        self.cloudKitService = cloudKitService
        self.userDefaults = userDefaults

        Task {
            await loadCurrentProfile()
        }
    }

    // MARK: - Public Methods

    func getCurrentProfile() async throws -> UserProfile {
        if let profile = currentProfile {
            return profile
        }

        // Try to load from local storage
        if let profile = loadProfileFromUserDefaults() {
            currentProfile = profile
            return profile
        }

        // Try to load from CloudKit
        do {
            let profile = try await loadProfileFromCloud()
            currentProfile = profile
            saveProfileToUserDefaults(profile)
            return profile
        } catch {
            // Create default profile if none exists
            let defaultProfile = createDefaultProfile()
            try await createProfile(defaultProfile)
            return defaultProfile
        }
    }

    func updateProfile(_ profile: UserProfile) async throws {
        // Update local storage
        currentProfile = profile
        saveProfileToUserDefaults(profile)

        // Update CloudKit
        try await saveProfileToCloud(profile)

        // Update Core Data
        try await coreDataService.saveUserProfile(profile)
    }

    func createProfile(_ profile: UserProfile) async throws {
        // Save locally
        currentProfile = profile
        saveProfileToUserDefaults(profile)
        userDefaults.set(true, forKey: Keys.hasCreatedProfile)

        // Save to CloudKit
        try await saveProfileToCloud(profile)

        // Save to Core Data
        try await coreDataService.saveUserProfile(profile)
    }

    func deleteProfile() async throws {
        // Clear local storage
        currentProfile = nil
        userDefaults.removeObject(forKey: Keys.currentUserProfile)
        userDefaults.set(false, forKey: Keys.hasCreatedProfile)

        // Delete from CloudKit
        try await deleteProfileFromCloud()

        // Delete from Core Data
        try await coreDataService.deleteUserProfile()
    }

    func clearUserData() async throws {
        try await deleteProfile()

        // Clear any additional user-related data
        clearUserCaches()
    }

    func syncWithCloud() async throws {
        guard let profile = currentProfile else { return }

        // Sync profile with CloudKit
        try await saveProfileToCloud(profile)

        // Fetch any updates from CloudKit
        let cloudProfile = try await loadProfileFromCloud()
        if cloudProfile.lastActiveAt > profile.lastActiveAt {
            currentProfile = cloudProfile
            saveProfileToUserDefaults(cloudProfile)
        }
    }

    // MARK: - Profile State

    func hasCreatedProfile() -> Bool {
        userDefaults.bool(forKey: Keys.hasCreatedProfile)
    }

    func isProfileComplete() -> Bool {
        guard let profile = currentProfile else { return false }

        return !profile.name.isEmpty &&
            !profile.email.isEmpty &&
            !profile.neurodiversityTypes.isEmpty
    }

    // MARK: - Private Methods

    private func loadCurrentProfile() async {
        do {
            currentProfile = try await getCurrentProfile()
        } catch {
            print("Failed to load current profile: \(error)")
        }
    }

    private func createDefaultProfile() -> UserProfile {
        UserProfile(
            name: "User",
            email: "<EMAIL>"
        )
    }

    // MARK: - Local Storage

    private func loadProfileFromUserDefaults() -> UserProfile? {
        guard let data = userDefaults.data(forKey: Keys.currentUserProfile),
              let profile = try? JSONDecoder().decode(UserProfile.self, from: data) else {
            return nil
        }
        return profile
    }

    private func saveProfileToUserDefaults(_ profile: UserProfile) {
        guard let data = try? JSONEncoder().encode(profile) else { return }
        userDefaults.set(data, forKey: Keys.currentUserProfile)
    }

    // MARK: - CloudKit Operations

    private func loadProfileFromCloud() async throws -> UserProfile {
        // This would integrate with CloudKit to fetch user profile
        // For now, return a placeholder implementation
        throw UserServiceError.profileNotFound
    }

    private func saveProfileToCloud(_ profile: UserProfile) async throws {
        // This would integrate with CloudKit to save user profile
        // For now, this is a placeholder implementation
        print("Saving profile to CloudKit: \(profile.name)")
    }

    private func deleteProfileFromCloud() async throws {
        // This would integrate with CloudKit to delete user profile
        // For now, this is a placeholder implementation
        print("Deleting profile from CloudKit")
    }

    // MARK: - Cache Management

    private func clearUserCaches() {
        // Clear any user-specific caches
        // Clear temporary files
        // Reset any user-specific settings
    }
}

// MARK: - User Service Errors

enum UserServiceError: Error, LocalizedError {
    case profileNotFound
    case failedToCreateProfile
    case failedToUpdateProfile
    case failedToDeleteProfile
    case failedToSyncWithCloud
    case invalidProfileData

    var errorDescription: String? {
        switch self {
        case .profileNotFound:
            return "User profile not found"
        case .failedToCreateProfile:
            return "Failed to create user profile"
        case .failedToUpdateProfile:
            return "Failed to update user profile"
        case .failedToDeleteProfile:
            return "Failed to delete user profile"
        case .failedToSyncWithCloud:
            return "Failed to sync profile with cloud"
        case .invalidProfileData:
            return "Invalid profile data"
        }
    }
}

// MARK: - UserProfile Codable Extension

extension UserProfile: Codable {
    enum CodingKeys: String, CodingKey {
        case id, name, email, createdAt, lastActiveAt
        case neurodiversityTypes, cognitiveStrengths, supportNeeds, preferredTheme
        case healthKitAuthorized, mentalHealthDataEnabled
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        email = try container.decode(String.self, forKey: .email)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        lastActiveAt = try container.decode(Date.self, forKey: .lastActiveAt)

        neurodiversityTypes = try container.decodeIfPresent([NeurodiversityType].self, forKey: .neurodiversityTypes) ?? []
        cognitiveStrengths = try container.decodeIfPresent([CognitiveStrength].self, forKey: .cognitiveStrengths) ?? []
        supportNeeds = try container.decodeIfPresent([CognitiveSupport].self, forKey: .supportNeeds) ?? []
        preferredTheme = try container.decodeIfPresent(NeuroNexaTheme.self, forKey: .preferredTheme) ?? .adaptive

        healthKitAuthorized = try container.decodeIfPresent(Bool.self, forKey: .healthKitAuthorized) ?? false
        mentalHealthDataEnabled = try container.decodeIfPresent(Bool.self, forKey: .mentalHealthDataEnabled) ?? false

        // Set defaults for non-codable properties
        cognitiveProfile = .default
        sensoryPreferences = .default
        accessibilitySettings = .default
        cognitiveLoadSettings = .default
        sensorySettings = .default
        executiveFunctionSettings = .default
        executiveFunctionLevel = .medium
        cloudKitRecordID = nil
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(email, forKey: .email)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(lastActiveAt, forKey: .lastActiveAt)
        try container.encode(neurodiversityTypes, forKey: .neurodiversityTypes)
        try container.encode(cognitiveStrengths, forKey: .cognitiveStrengths)
        try container.encode(supportNeeds, forKey: .supportNeeds)
        try container.encode(preferredTheme, forKey: .preferredTheme)
        try container.encode(healthKitAuthorized, forKey: .healthKitAuthorized)
        try container.encode(mentalHealthDataEnabled, forKey: .mentalHealthDataEnabled)
    }
}
