import Foundation

// MARK: - Breathing Service Helper Methods
// Extracted helper methods to reduce main BreathingService file size

@available(iOS 18.0, *)
extension BreathingService {
    // MARK: - Calculation Helpers
    
    func calculateStressReduction(_ healthData: HealthKitBreathingData) -> Double {
        // Calculate stress reduction based on HRV improvement
        guard !healthData.hrvReadings.isEmpty else { return 0.0 }
        
        let hrvValues = healthData.hrvReadings.map { $0.value }
        let initialHRV = hrvValues.prefix(3).reduce(0, +) / Double(min(3, hrvValues.count))
        let finalHRV = hrvValues.suffix(3).reduce(0, +) / Double(min(3, hrvValues.count))
        
        return max(0, (finalHRV - initialHRV) / initialHRV * 100)
    }
    
    func calculateAnxietyImprovement(_ healthData: HealthKitBreathingData) -> Double {
        // Calculate anxiety improvement based on heart rate variability
        guard !healthData.heartRateReadings.isEmpty else { return 0.0 }
        
        let heartRates = healthData.heartRateReadings.map { $0.value }
        let initialHR = heartRates.prefix(5).reduce(0, +) / Double(min(5, heartRates.count))
        let finalHR = heartRates.suffix(5).reduce(0, +) / Double(min(5, heartRates.count))
        
        // Lower heart rate typically indicates reduced anxiety
        return max(0, (initialHR - finalHR) / initialHR * 100)
    }
    
    func calculateVariability(_ values: any Sequence<Double>) -> Double {
        let array = Array(values)
        guard array.count > 1 else { return 0.0 }
        
        let mean = array.reduce(0, +) / Double(array.count)
        let variance = array.map { pow($0 - mean, 2) }.reduce(0, +) / Double(array.count)
        return sqrt(variance)
    }
    
    // MARK: - Session Result Calculation
    
    func calculateSessionResult(session: BreathingSession, healthData: HealthKitBreathingData) async -> BreathingSessionResult {
        let endTime = Date()
        let duration = endTime.timeIntervalSince(session.startTime)
        
        // Calculate metrics
        let averageHeartRate = healthData.heartRateReadings.isEmpty ? 0.0 : 
            healthData.heartRateReadings.map { $0.value }.reduce(0, +) / Double(healthData.heartRateReadings.count)
        let stressReduction = calculateStressReduction(healthData)
        let anxietyImprovement = calculateAnxietyImprovement(healthData)
        
        // Calculate completion rate
        let targetCycles = session.targetCycles
        let completedCycles = currentCycle
        let completionRate = Double(completedCycles) / Double(targetCycles)
        
        return BreathingSessionResult(
            id: UUID(),
            sessionId: session.id,
            userId: session.userProfile.id,
            exerciseType: BreathingExerciseType.custom, // Default type
            startTime: session.startTime,
            endTime: endTime,
            duration: duration,
            completedCycles: completedCycles,
            targetCycles: targetCycles,
            completionRate: completionRate,
            averageHeartRate: averageHeartRate,
            heartRateVariability: calculateVariability(healthData.heartRateReadings.map { $0.value }),
            stressReductionScore: stressReduction,
            anxietyImprovementScore: anxietyImprovement,
            userRating: nil, // To be set by user later
            notes: nil,
            healthMetrics: healthData
        )
    }
    
    // MARK: - Data Persistence Helpers
    
    func saveSessionResult(_ result: BreathingSessionResult) async throws {
        try await coreDataService.saveBreathingSessionResult(result)
    }
    
    // MARK: - Exercise Personalization Helpers
    
    func getPersonalizedExercises(for userProfile: UserProfile) async -> [BreathingExercise] {
        let allExercises = await getAvailableExercises()
        
        // Filter based on user's neurodiversity profile and preferences
        return allExercises.filter { exercise in
            // Check if exercise is suitable for user's cognitive load preference
            switch userProfile.neurodiversityProfile.cognitiveLoadPreference {
            case .low:
                return exercise.difficulty <= .beginner
            case .medium:
                return exercise.difficulty <= .intermediate
            case .high:
                return true // All exercises suitable
            }
        }.sorted { exercise1, exercise2 in
            // Prioritize exercises based on user's specific needs
            let score1 = calculateExerciseScore(exercise1, for: userProfile)
            let score2 = calculateExerciseScore(exercise2, for: userProfile)
            return score1 > score2
        }
    }
    
    private func calculateExerciseScore(_ exercise: BreathingExercise, for userProfile: UserProfile) -> Double {
        var score = 0.0
        
        // Base score from exercise effectiveness
        score += Double(exercise.cycles) * 0.1
        
        // Adjust for user's sensory preferences
        if userProfile.neurodiversityProfile.sensoryPreferences.motionSensitivity == .low {
            score += 0.5 // Prefer more dynamic exercises
        } else {
            score += exercise.inhalePattern.inhale <= 4 ? 0.5 : 0.0 // Prefer gentler exercises
        }
        
        // Adjust for executive function level
        switch userProfile.neurodiversityProfile.executiveFunctionLevel {
        case .low:
            score += exercise.cycles <= 5 ? 0.3 : 0.0 // Prefer shorter exercises
        case .medium:
            score += exercise.cycles <= 8 ? 0.2 : 0.0
        case .high:
            score += 0.1 // All exercises suitable
        }
        
        return score
    }
    
    // MARK: - Phase Timing Helpers
    
    func waitForPhase(duration: Int) async {
        await withCheckedContinuation { continuation in
            phaseTimer = Timer.scheduledTimer(withTimeInterval: TimeInterval(duration), repeats: false) { _ in
                continuation.resume()
            }
        }
    }
    
    // MARK: - Breathing Sequence Helpers
    
    func executeBreathingPhases(_ pattern: BreathingPattern) async {
        let phases: [(BreathingPhase, Int)] = [
            (.inhale, pattern.inhale),
            (.hold, pattern.hold),
            (.exhale, pattern.exhale),
            (.pause, pattern.pause)
        ]
        
        for (phase, duration) in phases {
            await MainActor.run {
                currentPhase = phase
            }
            
            await waitForPhase(duration: duration)
        }
    }
    
    func continueBreathingFromCurrentState(_ session: BreathingSession) async {
        // Resume from current cycle and phase
        let remainingCycles = session.targetCycles - currentCycle
        
        for _ in 0..<remainingCycles {
            await executeBreathingPhases(session.pattern)
            
            await MainActor.run {
                currentCycle += 1
                sessionProgress = Double(currentCycle) / Double(session.targetCycles)
            }
        }
    }
}
