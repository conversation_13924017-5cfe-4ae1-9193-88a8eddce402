import Foundation

// MARK: - OpenAI Task Coach Prompt Generation
// Extracted prompt generation methods to reduce main class size

@available(iOS 18.0, *)
extension OpenAITaskCoach {
    // MARK: - Prompt Creation Methods
    
    func createPersonalizedContentPrompt(for context: UserContext) -> String {
        """
        Create personalized content for a neurodivergent user with the following context:
        
        User Profile:
        - Neurodiversity: \(context.userProfile.neurodiversityProfile.primaryType)
        - Cognitive Load: \(context.currentCognitiveLoadLevel)
        - Preferences: \(context.userProfile.preferences.description)
        
        Current Context:
        - Time of day: \(DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short))
        - Energy level: \(context.currentEnergyLevel)
        - Focus state: \(context.currentFocusState)
        
        Please provide supportive, neurodiversity-aware guidance that:
        1. Respects cognitive load limitations
        2. Offers practical, actionable advice
        3. Uses clear, simple language
        4. Includes specific accommodations for \(context.userProfile.neurodiversityProfile.primaryType)
        
        Response should be encouraging and empowering.
        """
    }
    
    func createBehaviorAnalysisPrompt(for data: UserBehaviorData) -> String {
        """
        Analyze the following user behavior data for neurodiversity-aware insights:
        
        Task Completion Data:
        - Completed tasks: \(data.completedTasks.count)
        - Average completion time: \(data.averageCompletionTime) minutes
        - Success rate: \(data.successRate)%
        
        Patterns Observed:
        - Most productive time: \(data.mostProductiveTime)
        - Common challenges: \(data.commonChallenges.joined(separator: ", "))
        - Preferred task types: \(data.preferredTaskTypes.joined(separator: ", "))
        
        Please provide:
        1. Key behavioral insights
        2. Strengths to leverage
        3. Areas for improvement
        4. Specific recommendations for neurodiversity support
        5. Suggested accommodations
        
        Focus on positive, strength-based analysis.
        """
    }
    
    func createUserNeedsPredictionPrompt(for context: UserContext) -> String {
        """
        Predict user needs based on current context and historical patterns:
        
        Current State:
        - Cognitive load: \(context.currentCognitiveLoadLevel)
        - Time: \(DateFormatter.localizedString(from: Date(), dateStyle: .short, timeStyle: .short))
        - Recent activities: \(context.recentActivities.map { $0.description }.joined(separator: ", "))
        
        User Profile:
        - Neurodiversity type: \(context.userProfile.neurodiversityProfile.primaryType)
        - Known triggers: \(context.userProfile.knownTriggers.joined(separator: ", "))
        - Coping strategies: \(context.userProfile.copingStrategies.joined(separator: ", "))
        
        Predict likely needs in the next 1-4 hours:
        1. Task support needs
        2. Break requirements
        3. Environmental adjustments
        4. Emotional support needs
        5. Cognitive accommodations
        
        Provide specific, actionable predictions.
        """
    }
}
