import Foundation

// MARK: - OpenAI Task Coach Helper Methods

@available(iOS 18.0, *)
extension OpenAITaskCoach {
    // MARK: - Parsing Helpers
    
    func parsePersonalizedContent(from response: String, context: UserContext) throws -> PersonalizedContent {
        PersonalizedContent(
            id: UUID(),
            title: "Personalized Guidance",
            content: response,
            type: .guidance,
            relevanceScore: 0.8,
            createdAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .hour, value: 24, to: Date()),
            metadata: [
                "cognitiveLoad": String(context.currentCognitiveLoad),
                "userProfile": context.userProfile.id.uuidString
            ]
        )
    }
    
    func parseBehaviorInsights(from response: String, data: UserBehaviorData) throws -> BehaviorInsights {
        BehaviorInsights(
            id: UUID(),
            userId: data.userId,
            analysisDate: Date(),
            patterns: [],
            recommendations: [response],
            confidence: 0.75,
            dataPoints: data.dataPoints.count,
            timeframe: data.timeframe
        )
    }
    
    func parseUserNeeds(from response: String, context: UserContext) throws -> [UserNeed] {
        var needs: [UserNeed] = []

        if context.currentCognitiveLoad > 0.7 {
            needs.append(UserNeed(
                id: UUID(),
                type: .cognitiveBreak,
                priority: .high,
                description: "High cognitive load detected - break recommended",
                suggestedAction: "Take a 5-10 minute break",
                confidence: 0.9
            ))
        }

        return needs
    }
    
    func parseBreakSuggestion(from response: String) throws -> BreakSuggestion {
        BreakSuggestion(
            id: UUID(),
            type: .cognitive,
            duration: 300,
            title: "Personalized Break Time",
            description: response,
            activities: [
                "Deep breathing exercises",
                "Gentle stretching",
                "Mindful observation",
                "Progressive muscle relaxation"
            ],
            urgency: .medium,
            createdAt: Date()
        )
    }
}
