import Foundation

// MARK: - OpenAI Intelligence Errors

@available(iOS 18.0, *)
enum OpenAIIntelligenceError: <PERSON><PERSON><PERSON>, LocalizedError {
    case serviceUnavailable
    case analysisFailure
    case contentGenerationFailure
    case predictionFailure

    var errorDescription: String? {
        switch self {
        case .serviceUnavailable:
            return "OpenAI service is not available"
        case .analysisFailure:
            return "Failed to analyze user behavior"
        case .contentGenerationFailure:
            return "Failed to generate personalized content"
        case .predictionFailure:
            return "Failed to predict user needs"
        }
    }
}

// MARK: - OpenAI Service Errors

enum OpenAIError: Error {
    case invalidURL
    case invalidResponse
    case networkError
    case apiKeyMissing
}
