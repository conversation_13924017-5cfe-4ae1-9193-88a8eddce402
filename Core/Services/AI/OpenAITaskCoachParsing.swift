import Foundation

// MARK: - OpenAI Task Coach Response Parsing
// Parsing methods for OpenAI responses

@available(iOS 18.0, *)
extension OpenAITaskCoach {
    // MARK: - OpenAI Response Parsing Methods
    
    func parsePersonalizedContent(from response: String, context: UserContext) throws -> PersonalizedContent {
        PersonalizedContent(
            id: UUID(),
            title: "Personalized Guidance",
            content: response,
            contentType: .taskGuidance,
            personalizedFor: context.userProfile.id,
            cognitiveAdaptations: [adaptUIForCognitiveLoad(context.currentCognitiveLoadLevel)],
            createdAt: Date(),
            relevanceScore: 0.9
        )
    }
    
    func parseBehaviorInsights(from response: String, data: UserBehaviorData) throws -> BehaviorInsights {
        BehaviorInsights(
            id: UUID(),
            userId: data.userId,
            analysisDate: Date(),
            patterns: [
                BehaviorPattern(
                    id: UUID(),
                    type: .taskCompletion,
                    frequency: .daily,
                    strength: 0.8,
                    description: "Consistent task completion pattern",
                    identifiedAt: Date()
                )
            ],
            recommendations: ["Take regular breaks", "Use visual task tracking"],
            cognitiveLoadTrends: [],
            productivityMetrics: ProductivityMetrics(
                tasksCompleted: data.taskCompletions.count,
                averageCompletionTime: data.averageSessionLength * 60,
                focusTime: 3_600,
                breakTime: 600,
                efficiencyScore: 0.8
            ),
            confidenceScore: 0.85
        )
    }
    
    func parseUserNeeds(from response: String, context: UserContext) throws -> [UserNeed] {
        var needs: [UserNeed] = []
        
        if context.currentCognitiveLoad > 0.7 {
            needs.append(UserNeed(
                id: UUID(),
                type: .cognitiveBreak,
                priority: .high,
                description: "High cognitive load detected - break recommended",
                suggestedAction: "Take a 5-10 minute break",
                confidence: 0.9
            ))
        }
        
        return needs
    }
    
    func parseBreakSuggestion(from response: String) throws -> BreakSuggestion {
        BreakSuggestion(
            id: UUID(),
            type: .cognitive,
            duration: 300,
            title: "Personalized Break Time",
            description: response,
            activities: [
                "Deep breathing exercises",
                "Gentle stretching",
                "Mindful observation",
                "Progressive muscle relaxation"
            ],
            urgency: .medium,
            createdAt: Date()
        )
    }
    
    // MARK: - Prompt Creation Methods
    
    func createPersonalizedContentPrompt(for context: UserContext) -> String {
        """
        Create personalized content for a neurodivergent user with the following profile:
        
        Neurodiversity Types: \(context.userProfile.cognitiveProfile.neurodiversityType.map { $0.rawValue }.joined(separator: ", "))
        Current Cognitive Load: \(String(format: "%.1f", context.currentCognitiveLoad * 100))%
        Energy Level: \(context.energyLevel)/10
        Focus Duration: \(context.focusDuration) minutes
        
        Current Context:
        - Time: \(DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short))
        - Recent tasks: \(context.recentTaskCompletions.count) completed
        - Environment: \(context.environment)
        
        Generate supportive, encouraging content that:
        1. Acknowledges their neurodivergent strengths
        2. Provides practical, actionable guidance
        3. Uses clear, direct language
        4. Avoids overwhelming information
        5. Includes specific next steps
        
        Focus on empowerment and accommodation rather than deficit-based language.
        """
    }
    
    func createBehaviorAnalysisPrompt(for data: UserBehaviorData) -> String {
        """
        Analyze the following user behavior data with a neurodiversity-first approach:
        
        User ID: \(data.userId)
        Task completions: \(data.taskCompletions.count)
        Average session length: \(data.averageSessionLength) minutes
        Cognitive load patterns: \(data.cognitiveLoadHistory.map { String(format: "%.1f", $0) }.joined(separator: ", "))
        
        Provide insights that:
        1. Identify positive patterns and strengths
        2. Suggest gentle optimizations
        3. Respect neurodivergent working styles
        4. Avoid pathologizing language
        5. Focus on accommodation rather than correction
        
        Return analysis in JSON format with patterns, recommendations, and confidence scores.
        """
    }
    
    func createUserNeedsPredictionPrompt(for context: UserContext) -> String {
        """
        Predict user needs based on current context with neurodiversity-first principles:
        
        Current cognitive load: \(String(format: "%.1f", context.currentCognitiveLoad * 100))%
        Recent task completions: \(context.recentTaskCompletions.count)
        Time of day: \(DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short))
        Neurodiversity profile: \(context.userProfile.cognitiveProfile.neurodiversityType.map { $0.rawValue }.joined(separator: ", "))
        
        Predict needs that might include:
        - Cognitive breaks
        - Task support
        - Environmental adjustments
        - Sensory accommodations
        - Executive function support
        
        Return predictions with priority levels and confidence scores.
        """
    }
    
    func createBreakSuggestionPrompt(timeSinceLastBreak: TimeInterval) -> String {
        let minutesSinceBreak = Int(timeSinceLastBreak / 60)
        
        return """
        Generate a personalized break suggestion for someone who has been working for \(minutesSinceBreak) minutes.
        
        Consider neurodiversity-friendly break activities that:
        1. Reduce cognitive load
        2. Provide sensory regulation
        3. Support executive function reset
        4. Are achievable in 5-10 minutes
        5. Can be done in various environments
        
        Include specific activities and gentle encouragement.
        """
    }
    
    // MARK: - Advanced Parsing Helpers
    
    func parseJSONResponse<T: Codable>(_ response: String, as type: T.Type) throws -> T {
        guard let data = response.data(using: .utf8) else {
            throw OpenAITaskCoachError.invalidResponse
        }
        
        do {
            return try JSONDecoder().decode(type, from: data)
        } catch {
            throw OpenAITaskCoachError.parsingFailed(error)
        }
    }
    
    func extractCodeBlock(from response: String, language: String = "json") -> String? {
        let pattern = "```\(language)\\s*\\n([\\s\\S]*?)\\n```"
        let regex = try? NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(response.startIndex..<response.endIndex, in: response)
        
        if let match = regex?.firstMatch(in: response, options: [], range: range) {
            let codeRange = Range(match.range(at: 1), in: response)
            return codeRange.map { String(response[$0]) }
        }
        
        return nil
    }
    
    func sanitizeResponse(_ response: String) -> String {
        response
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// MARK: - Error Types

enum OpenAITaskCoachError: Error, LocalizedError {
    case invalidResponse
    case parsingFailed(Error)
    case networkError(Error)
    case rateLimitExceeded
    case insufficientTokens
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse:
            return "Invalid response from OpenAI service"
        case .parsingFailed(let error):
            return "Failed to parse response: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .rateLimitExceeded:
            return "OpenAI rate limit exceeded"
        case .insufficientTokens:
            return "Insufficient tokens for request"
        }
    }
}
