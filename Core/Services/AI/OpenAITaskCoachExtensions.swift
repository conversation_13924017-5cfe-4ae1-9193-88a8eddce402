import Combine
import Foundation

// MARK: - OpenAI Task Coach Extensions
// Additional helper methods and extensions for OpenAITaskCoach

@available(iOS 18.0, *)
extension OpenAITaskCoach {
    // MARK: - Content Generation Helpers
    
    /// Generate personalized task suggestions based on user context
    func generateTaskSuggestions(for context: UserContext) async throws -> [TaskSuggestion] {
        let prompt = buildTaskSuggestionPrompt(context: context)
        
        let response = try await openAIService.generateCompletion(
            prompt: prompt,
            maxTokens: 500,
            temperature: 0.7
        )
        
        return parseTaskSuggestions(from: response)
    }
    
    /// Generate cognitive adaptation recommendations
    func generateCognitiveAdaptations(for level: CognitiveLoadLevel) async throws -> [CognitiveAdaptation] {
        let prompt = buildCognitiveAdaptationPrompt(level: level)
        
        let response = try await openAIService.generateCompletion(
            prompt: prompt,
            maxTokens: 300,
            temperature: 0.6
        )
        
        return parseCognitiveAdaptations(from: response)
    }
    
    // MARK: - Prompt Building Helpers
    
    private func buildTaskSuggestionPrompt(context: UserContext) -> String {
        var prompt = "Generate personalized task suggestions for a neurodivergent user with the following context:\n\n"
        
        prompt += "User Profile:\n"
        prompt += "- Neurodiversity Types: \(context.neurodiversityTypes.map { $0.rawValue }.joined(separator: ", "))\n"
        prompt += "- Cognitive Load Level: \(context.cognitiveLoadLevel.rawValue)\n"
        prompt += "- Energy Level: \(context.energyLevel)\n"
        prompt += "- Focus Duration: \(context.focusDuration) minutes\n\n"
        
        prompt += "Current Context:\n"
        prompt += "- Time of Day: \(context.timeOfDay)\n"
        prompt += "- Environment: \(context.environment)\n"
        prompt += "- Recent Activities: \(context.recentActivities.joined(separator: ", "))\n\n"
        
        prompt += "Please provide 3-5 specific, actionable task suggestions that:\n"
        prompt += "1. Match the user's current cognitive capacity\n"
        prompt += "2. Consider their neurodiversity profile\n"
        prompt += "3. Are appropriate for the current context\n"
        prompt += "4. Include estimated duration and difficulty\n"
        prompt += "5. Provide clear, step-by-step instructions\n\n"
        
        prompt += "Format as JSON array with fields: title, description, estimatedDuration, difficulty, steps, cognitiveSupport"
        
        return prompt
    }
    
    private func buildCognitiveAdaptationPrompt(level: CognitiveLoadLevel) -> String {
        var prompt = "Generate cognitive adaptation strategies for a user experiencing \(level.rawValue) cognitive load.\n\n"
        
        prompt += "Consider the following adaptation categories:\n"
        prompt += "1. UI/UX modifications (colors, fonts, layout)\n"
        prompt += "2. Task breakdown strategies\n"
        prompt += "3. Timing and pacing adjustments\n"
        prompt += "4. Sensory accommodations\n"
        prompt += "5. Communication style adaptations\n\n"
        
        prompt += "Provide specific, implementable recommendations that can be applied immediately.\n"
        prompt += "Format as JSON array with fields: category, recommendation, implementation, priority"
        
        return prompt
    }
    
    // MARK: - Response Parsing Helpers
    
    private func parseTaskSuggestions(from response: String) -> [TaskSuggestion] {
        guard let data = response.data(using: .utf8) else { return [] }
        
        do {
            let suggestions = try JSONDecoder().decode([TaskSuggestion].self, from: data)
            return suggestions
        } catch {
            // Fallback parsing if JSON fails
            return parseTaskSuggestionsFromText(response)
        }
    }
    
    private func parseCognitiveAdaptations(from response: String) -> [CognitiveAdaptation] {
        guard let data = response.data(using: .utf8) else { return [] }
        
        do {
            let adaptations = try JSONDecoder().decode([CognitiveAdaptation].self, from: data)
            return adaptations
        } catch {
            // Fallback parsing if JSON fails
            return parseCognitiveAdaptationsFromText(response)
        }
    }
    
    private func parseTaskSuggestionsFromText(_ text: String) -> [TaskSuggestion] {
        // Fallback text parsing implementation
        let lines = text.components(separatedBy: .newlines)
        var suggestions: [TaskSuggestion] = []
        
        for line in lines {
            if line.contains("Task:") || line.contains("Suggestion:") {
                let suggestion = TaskSuggestion(
                    id: UUID(),
                    title: extractTitle(from: line),
                    description: extractDescription(from: line),
                    estimatedDuration: 15, // Default
                    difficulty: .medium,
                    steps: [],
                    cognitiveSupport: []
                )
                suggestions.append(suggestion)
            }
        }
        
        return suggestions
    }
    
    private func parseCognitiveAdaptationsFromText(_ text: String) -> [CognitiveAdaptation] {
        // Fallback text parsing implementation
        let lines = text.components(separatedBy: .newlines)
        var adaptations: [CognitiveAdaptation] = []
        
        for line in lines {
            if line.contains("Adaptation:") || line.contains("Recommendation:") {
                let adaptation = CognitiveAdaptation(
                    id: UUID(),
                    category: .uiModification,
                    recommendation: line,
                    implementation: "Apply immediately",
                    priority: .medium
                )
                adaptations.append(adaptation)
            }
        }
        
        return adaptations
    }
    
    // MARK: - Text Extraction Helpers
    
    private func extractTitle(from text: String) -> String {
        let components = text.components(separatedBy: ":")
        return components.count > 1 ? components[1].trimmingCharacters(in: .whitespaces) : text
    }
    
    private func extractDescription(from text: String) -> String {
        // Extract description from various text formats
        if text.contains("-") {
            let parts = text.components(separatedBy: "-")
            return parts.last?.trimmingCharacters(in: .whitespaces) ?? text
        }
        return text
    }
}

// MARK: - Supporting Types

struct TaskSuggestion: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let estimatedDuration: Int
    let difficulty: TaskDifficulty
    let steps: [String]
    let cognitiveSupport: [String]
}

struct CognitiveAdaptation: Codable, Identifiable {
    let id: UUID
    let category: AdaptationCategory
    let recommendation: String
    let implementation: String
    let priority: AdaptationPriority
}

enum TaskDifficulty: String, Codable {
    case easy, medium, hard
}

enum AdaptationCategory: String, Codable {
    case uiModification, taskBreakdown, timing, sensory, communication
}

enum AdaptationPriority: String, Codable {
    case low, medium, high, critical
}
