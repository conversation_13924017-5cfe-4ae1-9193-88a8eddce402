import Foundation

// MARK: - OpenAI Service

class OpenAIService {
    private let apiKey: String
    private let baseURL = "https://api.openai.com/v1"

    init() {
        self.apiKey = AppConfiguration.openAIAPIKey
    }

    func initialize() async throws {
        // Validate API key and connection
        _ = try await generateCompletion(prompt: "Test connection", maxTokens: 10)
    }

    func generateCompletion(prompt: String, maxTokens: Int = 1_000) async throws -> String {
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw OpenAIError.invalidURL
        }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "user", "content": prompt]
            ],
            "max_tokens": maxTokens,
            "temperature": 0.7
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw OpenAIError.networkError
        }

        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let choices = json["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: Any],
              let content = message["content"] as? String else {
            throw OpenAIError.invalidResponse
        }

        return content.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    func isAvailable() async -> Bool {
        do {
            _ = try await generateCompletion(prompt: "Test", maxTokens: 1)
            return true
        } catch {
            return false
        }
    }
}
