import Combine
import Foundation

// MARK: - Personalized Task Service Extensions
// Helper methods and extensions for PersonalizedTaskService

@available(iOS 18.0, *)
extension PersonalizedTaskService {
    // MARK: - Task Adaptation Methods
    
    func adaptTaskComplexity(_ task: AITask, for cognitiveLoad: CognitiveLoadLevel) -> AITask {
        var adaptedTask = task
        
        switch cognitiveLoad {
        case .low:
            // User can handle more complex tasks
            adaptedTask.complexity = min(task.complexity + 1, 5)
            adaptedTask.estimatedDuration = task.estimatedDuration * 1.2
            
        case .medium:
            // Keep task as is, but add more structure
            adaptedTask.subtasks = breakdownIntoSubtasks(task)
            
        case .high:
            // Simplify the task
            adaptedTask.complexity = max(task.complexity - 1, 1)
            adaptedTask.estimatedDuration = task.estimatedDuration * 0.8
            adaptedTask.instructions = simplifyInstructions(task.instructions)
        }
        
        return adaptedTask
    }
    
    func adaptTaskTiming(_ task: AITask, for userContext: UserContext) -> AITask {
        var adaptedTask = task
        
        // Adapt based on user's optimal times
        let optimalTimes = userContext.userProfile.cognitiveProfile.optimalWorkingHours
        let currentHour = Calendar.current.component(.hour, from: Date())
        
        if !optimalTimes.contains(currentHour) {
            // Suggest better timing
            adaptedTask.suggestedStartTime = findNextOptimalTime(from: Date(), optimalTimes: optimalTimes)
            adaptedTask.priority = max(adaptedTask.priority - 1, 1) // Lower priority for non-optimal times
        }
        
        // Adapt duration based on focus capacity
        let focusCapacity = userContext.focusDuration
        if task.estimatedDuration > focusCapacity * 60 {
            adaptedTask.estimatedDuration = TimeInterval(focusCapacity * 60)
            adaptedTask.requiresBreaks = true
            adaptedTask.breakInterval = TimeInterval(focusCapacity * 60 / 2)
        }
        
        return adaptedTask
    }
    
    func adaptTaskForNeurodiversity(_ task: AITask, neurodiversityTypes: [NeurodiversityType]) -> AITask {
        var adaptedTask = task
        
        for type in neurodiversityTypes {
            switch type {
            case .adhd:
                adaptedTask = adaptForADHD(adaptedTask)
            case .autism:
                adaptedTask = adaptForAutism(adaptedTask)
            case .dyslexia:
                adaptedTask = adaptForDyslexia(adaptedTask)
            case .anxiety:
                adaptedTask = adaptForAnxiety(adaptedTask)
            }
        }
        
        return adaptedTask
    }
    
    // MARK: - Neurodiversity-Specific Adaptations
    
    private func adaptForADHD(_ task: AITask) -> AITask {
        var adaptedTask = task
        
        // Break into smaller chunks
        if task.estimatedDuration > 25 * 60 { // 25 minutes
            adaptedTask.subtasks = breakdownIntoSubtasks(task, maxDuration: 25 * 60)
        }
        
        // Add more structure and clear steps
        adaptedTask.instructions = addStructureToInstructions(task.instructions)
        
        // Add reminders and checkpoints
        adaptedTask.checkpoints = createCheckpoints(for: task)
        
        // Suggest fidget-friendly environment
        adaptedTask.environmentSuggestions.append("Consider using a fidget tool or standing desk")
        
        return adaptedTask
    }
    
    private func adaptForAutism(_ task: AITask) -> AITask {
        var adaptedTask = task
        
        // Provide very detailed instructions
        adaptedTask.instructions = expandInstructions(task.instructions)
        
        // Add predictability
        adaptedTask.estimatedDuration = task.estimatedDuration * 1.1 // Add buffer time
        
        // Suggest consistent environment
        adaptedTask.environmentSuggestions.append("Work in a quiet, consistent environment")
        adaptedTask.environmentSuggestions.append("Use noise-canceling headphones if needed")
        
        // Add visual aids
        adaptedTask.visualAids = createVisualAids(for: task)
        
        return adaptedTask
    }
    
    private func adaptForDyslexia(_ task: AITask) -> AITask {
        var adaptedTask = task
        
        // Simplify text and use bullet points
        adaptedTask.instructions = formatForDyslexia(task.instructions)
        
        // Add audio options
        adaptedTask.audioSupport = true
        
        // Use visual organization
        adaptedTask.visualOrganization = true
        
        // Suggest tools
        adaptedTask.toolSuggestions.append("Consider using text-to-speech software")
        adaptedTask.toolSuggestions.append("Use dyslexia-friendly fonts")
        
        return adaptedTask
    }
    
    private func adaptForAnxiety(_ task: AITask) -> AITask {
        var adaptedTask = task
        
        // Add reassurance and support
        adaptedTask.supportNotes.append("Take breaks as needed")
        adaptedTask.supportNotes.append("Remember: progress over perfection")
        
        // Reduce pressure
        adaptedTask.priority = max(adaptedTask.priority - 1, 1)
        
        // Add coping strategies
        adaptedTask.copingStrategies = [
            "Take 3 deep breaths before starting",
            "Break the task into smaller steps",
            "Remind yourself of past successes"
        ]
        
        // Suggest calming environment
        adaptedTask.environmentSuggestions.append("Work in a calm, comfortable space")
        
        return adaptedTask
    }
    
    // MARK: - Helper Methods
    
    private func breakdownIntoSubtasks(_ task: AITask, maxDuration: TimeInterval = 30 * 60) -> [AITask] {
        let numberOfSubtasks = max(Int(ceil(task.estimatedDuration / maxDuration)), 2)
        var subtasks: [AITask] = []
        
        for index in 0..<numberOfSubtasks {
            let subtask = AITask(
                id: UUID(),
                title: "\(task.title) - Part \(index + 1)",
                description: "Subtask \(index + 1) of \(numberOfSubtasks)",
                estimatedDuration: task.estimatedDuration / TimeInterval(numberOfSubtasks),
                priority: task.priority,
                complexity: task.complexity,
                category: task.category
            )
            subtasks.append(subtask)
        }
        
        return subtasks
    }
    
    private func simplifyInstructions(_ instructions: String) -> String {
        // Break into shorter sentences and bullet points
        let sentences = instructions.components(separatedBy: ". ")
        let simplifiedSentences = sentences.map { sentence in
            if sentence.count > 50 {
                return "• \(sentence.prefix(50))..."
            } else {
                return "• \(sentence)"
            }
        }
        return simplifiedSentences.joined(separator: "\n")
    }
    
    private func addStructureToInstructions(_ instructions: String) -> String {
        let lines = instructions.components(separatedBy: "\n")
        var structuredInstructions = "STEPS TO COMPLETE:\n\n"
        
        for (index, line) in lines.enumerated() {
            structuredInstructions += "\(index + 1). \(line)\n"
        }
        
        structuredInstructions += "\nREMEMBER: Take breaks between steps if needed!"
        return structuredInstructions
    }
    
    private func expandInstructions(_ instructions: String) -> String {
        var expandedInstructions = "DETAILED INSTRUCTIONS:\n\n"
        expandedInstructions += instructions
        expandedInstructions += "\n\nEXPECTED OUTCOME:\n"
        expandedInstructions += "You will have completed this task successfully when all steps are finished."
        expandedInstructions += "\n\nIF YOU GET STUCK:\n"
        expandedInstructions += "• Take a break\n• Review the instructions\n• Ask for help if needed"
        
        return expandedInstructions
    }
    
    private func formatForDyslexia(_ instructions: String) -> String {
        let lines = instructions.components(separatedBy: "\n")
        var formattedInstructions = ""
        
        for line in lines {
            if line.count > 60 {
                // Break long lines into shorter ones
                let words = line.components(separatedBy: " ")
                var currentLine = ""
                
                for word in words {
                    if currentLine.count + word.count + 1 <= 60 {
                        currentLine += (currentLine.isEmpty ? "" : " ") + word
                    } else {
                        formattedInstructions += "• \(currentLine)\n"
                        currentLine = word
                    }
                }
                
                if !currentLine.isEmpty {
                    formattedInstructions += "• \(currentLine)\n"
                }
            } else {
                formattedInstructions += "• \(line)\n"
            }
        }
        
        return formattedInstructions
    }
    
    private func findNextOptimalTime(from date: Date, optimalTimes: [Int]) -> Date {
        let calendar = Calendar.current
        let currentHour = calendar.component(.hour, from: date)
        
        // Find next optimal hour today
        if let nextHour = optimalTimes.first(where: { $0 > currentHour }) {
            return calendar.date(bySettingHour: nextHour, minute: 0, second: 0, of: date) ?? date
        }
        
        // If no optimal time today, find first optimal time tomorrow
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: date) ?? date
        let firstOptimalHour = optimalTimes.min() ?? 9
        return calendar.date(bySettingHour: firstOptimalHour, minute: 0, second: 0, of: tomorrow) ?? date
    }
    
    private func createCheckpoints(for task: AITask) -> [String] {
        let duration = task.estimatedDuration
        let checkpointInterval: TimeInterval = 15 * 60 // Every 15 minutes
        let numberOfCheckpoints = Int(duration / checkpointInterval)
        
        var checkpoints: [String] = []
        for index in 1...numberOfCheckpoints {
            checkpoints.append("Checkpoint \(index): Check progress and take a brief break if needed")
        }
        
        checkpoints
    }
    
    private func createVisualAids(for task: AITask) -> [String] {
        [
            "Create a visual checklist",
            "Use color coding for different steps",
            "Draw a simple diagram if applicable",
            "Use timers for time awareness"
        ]
    }
}
