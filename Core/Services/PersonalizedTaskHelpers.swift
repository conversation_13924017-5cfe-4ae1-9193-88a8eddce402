import Foundation
import SwiftUI

// MARK: - Personalized Task Service Helper Methods

@available(iOS 18.0, *)
extension PersonalizedTaskService {
    // MARK: - Task Analysis Helpers
    
    func analyzeTaskComplexity(_ task: AITask) -> TaskComplexity {
        let factors = [
            task.estimatedDuration > 3_600 ? 1 : 0, // > 1 hour
            task.dependencies.count > 3 ? 1 : 0,
            task.requiredSkills.count > 5 ? 1 : 0,
            task.priority == .high ? 1 : 0
        ]
        
        let complexityScore = factors.reduce(0, +)
        
        switch complexityScore {
        case 0...1:
            return .simple
        case 2:
            return .moderate
        default:
            return .complex
        }
    }
    
    func calculateTaskPriority(_ task: AITask, for user: UserProfile) -> TaskPriority {
        var score = 0
        
        // Base priority
        score += task.priority.rawValue
        
        // User preferences
        if user.preferences.preferredTaskTypes.contains(task.type) {
            score += 2
        }
        
        // Deadline proximity
        if let deadline = task.deadline {
            let timeUntilDeadline = deadline.timeIntervalSinceNow
            if timeUntilDeadline < 86_400 { // 24 hours
                score += 3
            } else if timeUntilDeadline < 259_200 { // 3 days
                score += 1
            }
        }
        
        // Energy level match
        if task.requiredEnergyLevel <= user.currentEnergyLevel {
            score += 1
        }
        
        return TaskPriority(rawValue: min(score, 3)) ?? .medium
    }
    
    func generateTaskRecommendations(for user: UserProfile, tasks: [AITask]) -> [TaskRecommendation] {
        var recommendations: [TaskRecommendation] = []
        
        // Filter tasks by user capabilities
        let suitableTasks = tasks.filter { task in
            task.requiredEnergyLevel <= user.currentEnergyLevel &&
            task.estimatedDuration <= user.availableTime
        }
        
        // Sort by priority and user preferences
        let sortedTasks = suitableTasks.sorted { task1, task2 in
            let priority1 = calculateTaskPriority(task1, for: user)
            let priority2 = calculateTaskPriority(task2, for: user)
            return priority1.rawValue > priority2.rawValue
        }
        
        // Generate recommendations
        for task in sortedTasks.prefix(5) {
            let recommendation = TaskRecommendation(
                task: task,
                confidence: calculateRecommendationConfidence(task, for: user),
                reasoning: generateRecommendationReasoning(task, for: user)
            )
            recommendations.append(recommendation)
        }
        
        return recommendations
    }
    
    func calculateRecommendationConfidence(_ task: AITask, for user: UserProfile) -> Double {
        var confidence = 0.5 // Base confidence
        
        // User skill match
        let skillMatch = task.requiredSkills.filter { user.skills.contains($0) }.count
        confidence += Double(skillMatch) / Double(max(task.requiredSkills.count, 1)) * 0.3
        
        // Energy level match
        if task.requiredEnergyLevel <= user.currentEnergyLevel {
            confidence += 0.2
        }
        
        // Time availability
        if task.estimatedDuration <= user.availableTime {
            confidence += 0.2
        }
        
        return min(confidence, 1.0)
    }
    
    func generateRecommendationReasoning(_ task: AITask, for user: UserProfile) -> String {
        var reasons: [String] = []
        
        if user.preferences.preferredTaskTypes.contains(task.type) {
            reasons.append("matches your preferred task type")
        }
        
        if task.requiredEnergyLevel <= user.currentEnergyLevel {
            reasons.append("fits your current energy level")
        }
        
        if task.estimatedDuration <= user.availableTime {
            reasons.append("can be completed in your available time")
        }
        
        let skillMatch = task.requiredSkills.filter { user.skills.contains($0) }.count
        if skillMatch > 0 {
            reasons.append("utilizes \(skillMatch) of your existing skills")
        }
        
        return reasons.isEmpty ? "Good general fit for your profile" : reasons.joined(separator: ", ")
    }
}
