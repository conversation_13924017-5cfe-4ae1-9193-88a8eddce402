import Foundation
import LocalAuthentication

@available(iOS 18.0, *)
public class AuthenticationService: AuthenticationServiceProtocol {
    // MARK: - Properties

    private let userDefaults = UserDefaults.standard
    private var currentUser: UserProfile?

    // MARK: - AuthenticationServiceProtocol Implementation

    public func signIn() async throws -> UserProfile {
        // For now, return a mock user profile
        // In a real implementation, this would authenticate with Supabase
        let mockUser = UserProfile(
            name: "NeuroNexa User",
            email: "<EMAIL>"
        )

        currentUser = mockUser

        // Cache user data (simplified for now)
        userDefaults.set(mockUser.email, forKey: "current_user_email")

        return mockUser
    }

    public func signOut() async throws {
        currentUser = nil
        userDefaults.removeObject(forKey: "current_user")
        userDefaults.removeObject(forKey: "auth_token")

        // Clear any cached authentication data
        clearAuthenticationCache()
    }

    public func getCurrentUser() async -> UserProfile? {
        // Return cached user if available
        if let user = currentUser {
            return user
        }

        // Try to load from UserDefaults (simplified for now)
        if let email = userDefaults.string(forKey: "current_user_email") {
            let user = UserProfile(name: "NeuroNexa User", email: email)
            currentUser = user
            return user
        }

        return nil
    }

    public func authenticateWithBiometrics() async throws -> Bool {
        let context = LAContext()
        let reason = "Authenticate to access NeuroNexa"

        // Check if biometrics are available
        var error: NSError?
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            throw AuthenticationError.biometricsNotAvailable
        }

        do {
            let result = try await context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason)
            return result
        } catch {
            throw AuthenticationError.biometricsFailed
        }
    }

    // MARK: - Additional Authentication Methods

    func signIn(email: String, password: String) async throws -> UserProfile {
        // Validate input
        guard !email.isEmpty, !password.isEmpty else {
            throw AuthenticationError.invalidCredentials
        }

        // For now, return a mock user profile
        // In a real implementation, this would authenticate with Supabase
        let user = UserProfile(
            name: "NeuroNexa User",
            email: email
        )

        currentUser = user

        // Cache user data (simplified for now)
        userDefaults.set(user.email, forKey: "current_user_email")

        return user
    }

    func signUp(email: String, password: String, profile: UserProfile) async throws -> UserProfile {
        // Validate input
        guard !email.isEmpty, !password.isEmpty else {
            throw AuthenticationError.invalidCredentials
        }

        // For now, return the provided profile with updated email
        var newUser = profile
        newUser.email = email
        // UserProfile properties are set during initialization
        currentUser = newUser

        // Cache user data (simplified for now)
        userDefaults.set(newUser.email, forKey: "current_user_email")

        return newUser
    }

    func resetPassword(email: String) async throws {
        // Validate email
        guard !email.isEmpty else {
            throw AuthenticationError.invalidCredentials
        }

        // In a real implementation, this would send a password reset email via Supabase
        // For now, just simulate success
        print("Password reset email sent to: \(email)")
    }

    func refreshToken() async throws -> String {
        // In a real implementation, this would refresh the authentication token
        // For now, return a mock token
        let mockToken = "mock_refresh_token_\(Date().timeIntervalSince1970)"
        userDefaults.set(mockToken, forKey: "auth_token")
        return mockToken
    }

    func isAuthenticated() -> Bool {
        currentUser != nil || userDefaults.data(forKey: "current_user") != nil
    }

    // MARK: - Private Helper Methods

    private func clearAuthenticationCache() {
        // Clear all authentication-related cached data
        let keysToRemove = [
            "current_user",
            "auth_token",
            "refresh_token",
            "biometric_enabled",
            "last_login_date"
        ]

        for key in keysToRemove {
            userDefaults.removeObject(forKey: key)
        }
    }
}

@available(iOS 18.0, *)
enum AuthenticationError: Error, LocalizedError {
    case notImplemented
    case invalidCredentials
    case biometricsFailed
    case biometricsNotAvailable
    case userNotFound
    case emailAlreadyExists
    case networkError
    case tokenExpired
    case authenticationRequired

    var errorDescription: String? {
        switch self {
        case .notImplemented:
            return "Authentication method not implemented"
        case .invalidCredentials:
            return "Invalid email or password"
        case .biometricsFailed:
            return "Biometric authentication failed"
        case .biometricsNotAvailable:
            return "Biometric authentication is not available on this device"
        case .userNotFound:
            return "User not found"
        case .emailAlreadyExists:
            return "An account with this email already exists"
        case .networkError:
            return "Network error occurred"
        case .tokenExpired:
            return "Authentication token has expired"
        case .authenticationRequired:
            return "Authentication is required"
        }
    }
}
