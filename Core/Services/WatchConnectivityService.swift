import Foundation
import SwiftUI
import WatchConnectivity

// MARK: - Watch Connectivity Service Protocol

@available(iOS 18.0, *)
protocol WatchConnectivityServiceProtocol {
    func startSession()
    func sendMessage(_ message: [String: Any]) async throws
    func sendUserContext(_ context: WCApplicationContext) async throws
    func transferFile(_ file: WCSessionFile) async throws
    func isWatchAppInstalled() -> Bool
    func isWatchReachable() -> Bool
    func syncHealthData() async throws
    func syncTaskData() async throws
    func syncBreathingData() async throws
}

// MARK: - Watch Connectivity Service Implementation

@available(iOS 18.0, *)
@MainActor
class WatchConnectivityService: NSObject, WatchConnectivityServiceProtocol {
    // MARK: - Properties

    private var session: WCSession?
    private let userDefaults = UserDefaults.standard

    // Published properties for SwiftUI binding
    @Published var isWatchConnected: Bool = false
    @Published var isWatchAppInstalled: Bool = false
    @Published var lastSyncDate: Date?

    // MARK: - Initialization

    override init() {
        super.init()
        setupWatchConnectivity()
    }

    // MARK: - Service Methods

    func startSession() {
        guard WCSession.isSupported() else {
            print("WatchConnectivity not supported on this device")
            return
        }

        session = WCSession.default
        session?.delegate = self
        session?.activate()
    }

    func sendMessage(_ message: [String: Any]) async throws {
        guard let session = session, session.isReachable else {
            throw WatchConnectivityError.watchNotReachable
        }

        return try await withCheckedThrowingContinuation { continuation in
            session.sendMessage(message, replyHandler: { _ in
                continuation.resume()
            }, errorHandler: { error in
                continuation.resume(throwing: error)
            })
        }
    }

    func sendUserContext(_ context: WCApplicationContext) async throws {
        guard let session = session else {
            throw WatchConnectivityError.sessionNotAvailable
        }

        do {
            try session.updateApplicationContext(context)
        } catch {
            throw WatchConnectivityError.contextUpdateFailed(error)
        }
    }

    func transferFile(_ file: WCSessionFile) async throws {
        guard let session = session else {
            throw WatchConnectivityError.sessionNotAvailable
        }

        session.transferFile(file.url, metadata: file.metadata)
    }

    func isWatchAppInstalled() -> Bool {
        session?.isWatchAppInstalled ?? false
    }

    func isWatchReachable() -> Bool {
        session?.isReachable ?? false
    }

    func syncHealthData() async throws {
        let healthData = try await gatherHealthData()
        let message = [
            "type": "healthSync",
            "data": healthData
        ] as [String: Any]

        try await sendMessage(message)
        updateLastSyncDate()
    }

    func syncTaskData() async throws {
        let taskData = try await gatherTaskData()
        let message = [
            "type": "taskSync",
            "data": taskData
        ] as [String: Any]

        try await sendMessage(message)
        updateLastSyncDate()
    }

    func syncBreathingData() async throws {
        let breathingData = try await gatherBreathingData()
        let message = [
            "type": "breathingSync",
            "data": breathingData
        ] as [String: Any]

        try await sendMessage(message)
        updateLastSyncDate()
    }

    // MARK: - Private Helper Methods

    private func setupWatchConnectivity() {
        if WCSession.isSupported() {
            startSession()
        }
    }

    private func updateLastSyncDate() {
        lastSyncDate = Date()
        userDefaults.set(lastSyncDate, forKey: "last_watch_sync_date")
    }

    private func gatherHealthData() async throws -> [String: Any] {
        // Gather health-related data for watch sync
        [
            "heartRate": await getCurrentHeartRate(),
            "steps": await getTodaySteps(),
            "activeEnergy": await getTodayActiveEnergy(),
            "timestamp": Date().timeIntervalSince1970
        ]
    }

    private func gatherTaskData() async throws -> [String: Any] {
        // Gather task-related data for watch sync
        [
            "activeTasks": await getActiveTasks(),
            "completedToday": await getTasksCompletedToday(),
            "upcomingDeadlines": await getUpcomingDeadlines(),
            "timestamp": Date().timeIntervalSince1970
        ]
    }

    private func gatherBreathingData() async throws -> [String: Any] {
        // Gather breathing exercise data for watch sync
        [
            "recentSessions": await getRecentBreathingSessions(),
            "currentStreak": await getBreathingStreak(),
            "preferredExercises": await getPreferredBreathingExercises(),
            "timestamp": Date().timeIntervalSince1970
        ]
    }

    // MARK: - Data Gathering Helper Methods

    private func getCurrentHeartRate() async -> Double {
        // This would integrate with HealthKit to get current heart rate
        // For now, return a placeholder value
        72.0
    }

    private func getTodaySteps() async -> Int {
        // This would integrate with HealthKit to get today's step count
        // For now, return a placeholder value
        8_500
    }

    private func getTodayActiveEnergy() async -> Double {
        // This would integrate with HealthKit to get today's active energy
        // For now, return a placeholder value
        450.0
    }

    private func getActiveTasks() async -> [[String: Any]] {
        // This would integrate with task repository to get active tasks
        // For now, return placeholder data
        [
            [
                "id": UUID().uuidString,
                "title": "Complete project review",
                "priority": "high",
                "dueDate": Date().addingTimeInterval(3_600).timeIntervalSince1970
            ]
        ]
    }

    private func getTasksCompletedToday() async -> Int {
        // This would integrate with task repository to get today's completed tasks
        // For now, return a placeholder value
        3
    }

    private func getUpcomingDeadlines() async -> [[String: Any]] {
        // This would integrate with task repository to get upcoming deadlines
        // For now, return placeholder data
        [
            [
                "taskId": UUID().uuidString,
                "title": "Submit report",
                "deadline": Date().addingTimeInterval(86_400).timeIntervalSince1970
            ]
        ]
    }

    private func getRecentBreathingSessions() async -> [[String: Any]] {
        // This would integrate with breathing service to get recent sessions
        // For now, return placeholder data
        [
            [
                "id": UUID().uuidString,
                "duration": 300,
                "type": "box_breathing",
                "completedAt": Date().timeIntervalSince1970
            ]
        ]
    }

    private func getBreathingStreak() async -> Int {
        // This would calculate the current breathing exercise streak
        // For now, return a placeholder value
        7
    }

    private func getPreferredBreathingExercises() async -> [String] {
        // This would get user's preferred breathing exercises
        // For now, return placeholder data
        ["box_breathing", "4_7_8_breathing"]
    }
}

// MARK: - WCSessionDelegate

@available(iOS 18.0, *)
extension WatchConnectivityService: WCSessionDelegate {
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            self.isWatchConnected = (activationState == .activated)
            self.isWatchAppInstalled = session.isWatchAppInstalled
        }

        if let error = error {
            print("WCSession activation failed with error: \(error.localizedDescription)")
        } else {
            print("WCSession activated with state: \(activationState.rawValue)")
        }
    }

    func sessionDidBecomeInactive(_ session: WCSession) {
        print("WCSession became inactive")
        DispatchQueue.main.async {
            self.isWatchConnected = false
        }
    }

    func sessionDidDeactivate(_ session: WCSession) {
        print("WCSession deactivated")
        DispatchQueue.main.async {
            self.isWatchConnected = false
        }

        // Reactivate the session for iOS
        session.activate()
    }

    func sessionWatchStateDidChange(_ session: WCSession) {
        DispatchQueue.main.async {
            self.isWatchConnected = session.isReachable
            self.isWatchAppInstalled = session.isWatchAppInstalled
        }
    }

    func session(_ session: WCSession, didReceiveMessage message: [String: Any]) {
        handleReceivedMessage(message)
    }

    func session(_ session: WCSession, didReceiveMessage message: [String: Any], replyHandler: @escaping ([String: Any]) -> Void) {
        handleReceivedMessage(message)
        replyHandler(["status": "received"])
    }

    func session(_ session: WCSession, didReceiveApplicationContext applicationContext: [String: Any]) {
        handleReceivedApplicationContext(applicationContext)
    }

    func session(_ session: WCSession, didReceive file: WCSessionFile) {
        handleReceivedFile(file)
    }

    // MARK: - Message Handling

    private func handleReceivedMessage(_ message: [String: Any]) {
        guard let messageType = message["type"] as? String else {
            print("Received message without type")
            return
        }

        switch messageType {
        case "heartRateUpdate":
            handleHeartRateUpdate(message)
        case "taskCompletion":
            handleTaskCompletion(message)
        case "breathingSessionComplete":
            handleBreathingSessionComplete(message)
        case "emergencyAlert":
            handleEmergencyAlert(message)
        default:
            print("Unknown message type: \(messageType)")
        }
    }

    private func handleReceivedApplicationContext(_ context: [String: Any]) {
        // Handle application context updates from watch
        print("Received application context: \(context)")
    }

    private func handleReceivedFile(_ file: WCSessionFile) {
        // Handle file transfers from watch
        print("Received file: \(file.fileURL)")
    }

    private func handleHeartRateUpdate(_ message: [String: Any]) {
        guard let heartRate = message["heartRate"] as? Double else { return }

        // Update heart rate data in the app
        print("Heart rate update from watch: \(heartRate) BPM")

        // This would typically update the health service or repository
    }

    private func handleTaskCompletion(_ message: [String: Any]) {
        guard let taskId = message["taskId"] as? String else { return }

        // Handle task completion from watch
        print("Task completed on watch: \(taskId)")

        // This would typically update the task repository
    }

    private func handleBreathingSessionComplete(_ message: [String: Any]) {
        guard let sessionData = message["sessionData"] as? [String: Any] else { return }

        // Handle breathing session completion from watch
        print("Breathing session completed on watch: \(sessionData)")

        // This would typically update the breathing service
    }

    private func handleEmergencyAlert(_ message: [String: Any]) {
        guard let alertType = message["alertType"] as? String else { return }

        // Handle emergency alerts from watch
        print("Emergency alert from watch: \(alertType)")

        // This would typically trigger appropriate emergency response
    }
}

// MARK: - Watch Connectivity Errors

@available(iOS 18.0, *)
enum WatchConnectivityError: Error, LocalizedError {
    case sessionNotAvailable
    case watchNotReachable
    case watchAppNotInstalled
    case contextUpdateFailed(Error)
    case messageTransferFailed(Error)
    case fileTransferFailed(Error)

    var errorDescription: String? {
        switch self {
        case .sessionNotAvailable:
            return "Watch connectivity session is not available"
        case .watchNotReachable:
            return "Apple Watch is not reachable"
        case .watchAppNotInstalled:
            return "Watch app is not installed"
        case .contextUpdateFailed(let error):
            return "Failed to update application context: \(error.localizedDescription)"
        case .messageTransferFailed(let error):
            return "Failed to transfer message: \(error.localizedDescription)"
        case .fileTransferFailed(let error):
            return "Failed to transfer file: \(error.localizedDescription)"
        }
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct WCSessionFile {
    let url: URL
    let metadata: [String: Any]?

    init(url: URL, metadata: [String: Any]? = nil) {
        self.url = url
        self.metadata = metadata
    }
}
