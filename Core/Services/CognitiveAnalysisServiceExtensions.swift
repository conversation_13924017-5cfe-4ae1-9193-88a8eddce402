import Foundation
import SwiftUI

// MARK: - Cognitive Analysis Service Extensions
// Helper methods and extensions for CognitiveAnalysisService

@available(iOS 18.0, *)
extension CognitiveAnalysisService {
    // MARK: - Pattern Analysis Methods
    
    func analyzeProductivityPatterns(_ data: [TaskCompletion]) async -> ProductivityPatterns {
        let timeBasedPatterns = analyzeTimeBasedPatterns(data)
        let cognitiveLoadPatterns = analyzeCognitiveLoadPatterns(data)
        let taskTypePatterns = analyzeTaskTypePatterns(data)
        
        return ProductivityPatterns(
            id: UUID(),
            timeBasedPatterns: timeBasedPatterns,
            cognitiveLoadPatterns: cognitiveLoadPatterns,
            taskTypePatterns: taskTypePatterns,
            identifiedAt: Date(),
            confidence: calculatePatternConfidence(data)
        )
    }
    
    func analyzeTimeBasedPatterns(_ data: [TaskCompletion]) -> [TimeBasedPattern] {
        var patterns: [TimeBasedPattern] = []
        
        // Group by hour of day
        let hourlyGroups = Dictionary(grouping: data) { completion in
            Calendar.current.component(.hour, from: completion.completedAt)
        }
        
        for (hour, completions) in hourlyGroups {
            let averagePerformance = completions.map { $0.performanceScore }.reduce(0, +) / Double(completions.count)
            let averageCognitiveLoad = completions.map { $0.cognitiveLoadDuring }.reduce(0, +) / Double(completions.count)
            
            if averagePerformance > 0.7 { // High performance threshold
                patterns.append(TimeBasedPattern(
                    timeOfDay: hour,
                    performanceLevel: .high,
                    averageCognitiveLoad: averageCognitiveLoad,
                    taskCount: completions.count
                ))
            }
        }
        
        return patterns
    }
    
    func analyzeCognitiveLoadPatterns(_ data: [TaskCompletion]) -> [CognitiveLoadPattern] {
        var patterns: [CognitiveLoadPattern] = []
        
        // Group by cognitive load level
        let loadGroups = Dictionary(grouping: data) { completion in
            CognitiveLoadLevel.from(completion.cognitiveLoadDuring)
        }
        
        for (loadLevel, completions) in loadGroups {
            let averagePerformance = completions.map { $0.performanceScore }.reduce(0, +) / Double(completions.count)
            let averageDuration = completions.map { $0.duration }.reduce(0, +) / Double(completions.count)
            
            patterns.append(CognitiveLoadPattern(
                loadLevel: loadLevel,
                averagePerformance: averagePerformance,
                averageDuration: averageDuration,
                taskCount: completions.count,
                optimalTaskTypes: identifyOptimalTaskTypes(for: loadLevel, in: completions)
            ))
        }
        
        return patterns
    }
    
    func analyzeTaskTypePatterns(_ data: [TaskCompletion]) -> [TaskTypePattern] {
        var patterns: [TaskTypePattern] = []
        
        // Group by task type
        let typeGroups = Dictionary(grouping: data) { completion in
            completion.taskType
        }
        
        for (taskType, completions) in typeGroups {
            let averagePerformance = completions.map { $0.performanceScore }.reduce(0, +) / Double(completions.count)
            let averageCognitiveLoad = completions.map { $0.cognitiveLoadDuring }.reduce(0, +) / Double(completions.count)
            let preferredTimes = identifyPreferredTimes(for: completions)
            
            patterns.append(TaskTypePattern(
                taskType: taskType,
                averagePerformance: averagePerformance,
                averageCognitiveLoad: averageCognitiveLoad,
                preferredTimes: preferredTimes,
                completionRate: calculateCompletionRate(for: taskType, in: data)
            ))
        }
        
        return patterns
    }
    
    // MARK: - Helper Methods
    
    private func calculatePatternConfidence(_ data: [TaskCompletion]) -> Double {
        guard data.count >= 10 else { return 0.3 } // Low confidence with insufficient data
        
        let dataSpan = data.max { $0.completedAt < $1.completedAt }?.completedAt.timeIntervalSince(
            data.min { $0.completedAt < $1.completedAt }?.completedAt ?? Date()
        ) ?? 0
        
        let daysSpan = dataSpan / (24 * 60 * 60)
        
        if daysSpan >= 30 && data.count >= 50 {
            return 0.9 // High confidence
        } else if daysSpan >= 14 && data.count >= 25 {
            return 0.7 // Medium confidence
        } else {
            return 0.5 // Low-medium confidence
        }
    }
    
    private func identifyOptimalTaskTypes(for loadLevel: CognitiveLoadLevel, in completions: [TaskCompletion]) -> [String] {
        let highPerformanceTasks = completions.filter { $0.performanceScore > 0.8 }
        let taskTypes = Set(highPerformanceTasks.map { $0.taskType })
        return Array(taskTypes)
    }
    
    private func identifyPreferredTimes(for completions: [TaskCompletion]) -> [Int] {
        let hourlyPerformance = Dictionary(grouping: completions) { completion in
            Calendar.current.component(.hour, from: completion.completedAt)
        }.mapValues { completions in
            completions.map { $0.performanceScore }.reduce(0, +) / Double(completions.count)
        }
        
        return hourlyPerformance.filter { $0.value > 0.7 }.map { $0.key }.sorted()
    }
    
    private func calculateCompletionRate(for taskType: String, in data: [TaskCompletion]) -> Double {
        let totalTasks = data.filter { $0.taskType == taskType }.count
        let completedTasks = data.filter { $0.taskType == taskType && $0.wasCompleted }.count
        
        guard totalTasks > 0 else { return 0.0 }
        return Double(completedTasks) / Double(totalTasks)
    }
}

// MARK: - Supporting Types

struct ProductivityPatterns {
    let id: UUID
    let timeBasedPatterns: [TimeBasedPattern]
    let cognitiveLoadPatterns: [CognitiveLoadPattern]
    let taskTypePatterns: [TaskTypePattern]
    let identifiedAt: Date
    let confidence: Double
}

struct TimeBasedPattern {
    let timeOfDay: Int
    let performanceLevel: PerformanceLevel
    let averageCognitiveLoad: Double
    let taskCount: Int
}

struct CognitiveLoadPattern {
    let loadLevel: CognitiveLoadLevel
    let averagePerformance: Double
    let averageDuration: TimeInterval
    let taskCount: Int
    let optimalTaskTypes: [String]
}

struct TaskTypePattern {
    let taskType: String
    let averagePerformance: Double
    let averageCognitiveLoad: Double
    let preferredTimes: [Int]
    let completionRate: Double
}

enum PerformanceLevel {
    case low, medium, high
}

extension CognitiveLoadLevel {
    static func from(_ value: Double) -> CognitiveLoadLevel {
        switch value {
        case 0.0..<0.3: return .low
        case 0.3..<0.7: return .medium
        default: return .high
        }
    }
}
