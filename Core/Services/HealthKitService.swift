import Foundation
import HealthKit

// Claude: Adding imports for breathing session types
// Note: These types should be imported from their respective model files

// MARK: - HealthKit Service Protocol

@available(iOS 18.0, *)
public protocol HealthKitServiceProtocol {
    func requestAuthorization() async throws -> Bool
    func isHealthDataAvailable() -> Bool
    func trackBreathingSession(duration: TimeInterval) async throws
    func getHeartRateData() async throws -> [HKQuantitySample]
    func getMindfulnessData() async throws -> [HKCategorySample]
    
    // Claude: Adding missing methods for breathing session management
    func startBreathingSession(_ session: BreathingSession) async throws
    func pauseBreathingSession() async throws
    func resumeBreathingSession() async throws
    func endBreathingSession() async throws -> HealthKitBreathingData
    func getCurrentHeartRate() async throws -> Double?
    func getCurrentHRV() async throws -> HRVReading?
}

// MARK: - HealthKit Service Implementation

@available(iOS 18.0, *)
@MainActor
public class HealthKitService: HealthKitServiceProtocol, @unchecked Sendable {
    // MARK: - HealthKit Store

    private let healthStore = HKHealthStore()

    // MARK: - Health Data Types

    private let typesToRead: Set<HKObjectType> = {
        var types: Set<HKObjectType> = []
        if let heartRate = HKObjectType.quantityType(forIdentifier: .heartRate) {
            types.insert(heartRate)
        }
        if let mindfulSession = HKObjectType.categoryType(forIdentifier: .mindfulSession) {
            types.insert(mindfulSession)
        }
        if let respiratoryRate = HKObjectType.quantityType(forIdentifier: .respiratoryRate) {
            types.insert(respiratoryRate)
        }
        return types
    }()

    private let typesToWrite: Set<HKSampleType> = {
        var types: Set<HKSampleType> = []
        if let mindfulSession = HKObjectType.categoryType(forIdentifier: .mindfulSession) {
            types.insert(mindfulSession)
        }
        if let respiratoryRate = HKObjectType.quantityType(forIdentifier: .respiratoryRate) {
            types.insert(respiratoryRate)
        }
        return types
    }()

    // MARK: - Authorization

    func requestAuthorization() async throws -> Bool {
        guard isHealthDataAvailable() else {
            throw HealthKitServiceError.healthDataNotAvailable
        }

        return try await withCheckedThrowingContinuation { continuation in
            healthStore.requestAuthorization(toShare: typesToWrite, read: typesToRead) { success, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume(returning: success)
                }
            }
        }
    }

    func isHealthDataAvailable() -> Bool {
        HKHealthStore.isHealthDataAvailable()
    }

    // MARK: - Breathing Session Tracking

    func trackBreathingSession(duration: TimeInterval) async throws {
        guard let mindfulnessType = HKObjectType.categoryType(forIdentifier: .mindfulSession) else {
            throw HealthKitServiceError.invalidDataType
        }

        let startDate = Date().addingTimeInterval(-duration)
        let endDate = Date()

        let mindfulnessSample = HKCategorySample(
            type: mindfulnessType,
            value: HKCategoryValue.notApplicable.rawValue,
            start: startDate,
            end: endDate
        )

        try await healthStore.save(mindfulnessSample)
    }

    // MARK: - Data Retrieval

    func getHeartRateData() async throws -> [HKQuantitySample] {
        guard let heartRateType = HKObjectType.quantityType(forIdentifier: .heartRate) else {
            throw HealthKitServiceError.invalidDataType
        }

        let predicate = HKQuery.predicateForSamples(
            withStart: Calendar.current.date(byAdding: .day, value: -7, to: Date()),
            end: Date(),
            options: .strictStartDate
        )

        return try await withCheckedThrowingContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: heartRateType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: false)]
            ) { _, samples, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume(returning: samples as? [HKQuantitySample] ?? [])
                }
            }

            healthStore.execute(query)
        }
    }

    func getMindfulnessData() async throws -> [HKCategorySample] {
        guard let mindfulnessType = HKObjectType.categoryType(forIdentifier: .mindfulSession) else {
            throw HealthKitServiceError.invalidDataType
        }

        let predicate = HKQuery.predicateForSamples(
            withStart: Calendar.current.date(byAdding: .month, value: -1, to: Date()),
            end: Date(),
            options: .strictStartDate
        )

        return try await withCheckedThrowingContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: mindfulnessType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: false)]
            ) { _, samples, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume(returning: samples as? [HKCategorySample] ?? [])
                }
            }

            healthStore.execute(query)
        }
    }
    
    // MARK: - Breathing Session Management
    
    // Claude: Adding missing breathing session management methods
    public func startBreathingSession(_ session: BreathingSession) async throws {
        // Start monitoring health data for the session
        try await trackBreathingSession(duration: 0) // Initialize session
    }

    public func pauseBreathingSession() async throws {
        // Pause health data collection
        // Implementation placeholder
    }

    public func resumeBreathingSession() async throws {
        // Resume health data collection
        // Implementation placeholder
    }

    public func endBreathingSession() async throws -> HealthKitBreathingData {
        // End session and return collected data
        let heartRateData = try await getHeartRateData()
        let heartRateReadings = heartRateData.compactMap { sample in
            HeartRateReading(value: sample.quantity.doubleValue(for: HKUnit(from: "count/min")), timestamp: sample.startDate)
        }
        
        return HealthKitBreathingData(
            heartRateReadings: heartRateReadings,
            hrvReadings: [], // Placeholder - would need proper HRV data collection
            respiratoryRate: nil,
            oxygenSaturation: nil
        )
    }
    
    public func getCurrentHeartRate() async throws -> Double? {
        let heartRateData = try await getHeartRateData()
        return heartRateData.first?.quantity.doubleValue(for: HKUnit(from: "count/min"))
    }

    public func getCurrentHRV() async throws -> HRVReading? {
        // Placeholder implementation - would need proper HRV data collection
        nil
    }
}

// MARK: - HealthKit Service Errors

enum HealthKitServiceError: Error, LocalizedError {
    case healthDataNotAvailable
    case authorizationDenied
    case invalidDataType
    case failedToSave
    case failedToFetch

    var errorDescription: String? {
        switch self {
        case .healthDataNotAvailable:
            return "Health data not available on this device"
        case .authorizationDenied:
            return "HealthKit authorization denied"
        case .invalidDataType:
            return "Invalid HealthKit data type"
        case .failedToSave:
            return "Failed to save health data"
        case .failedToFetch:
            return "Failed to fetch health data"
        }
    }
}
