import Foundation

// MARK: - Settings Service Protocol

@available(iOS 18.0, *)
public protocol SettingsServiceProtocol {
    func loadSettings() async throws -> AppSettings
    func saveSettings(_ settings: AppSettings) async throws
    func clearAllData() async throws
}

// MARK: - Settings Service Implementation

@available(iOS 18.0, *)
@MainActor
public class SettingsService: SettingsServiceProtocol {
    // MARK: - Dependencies
    private let userDefaults: UserDefaults
    private let userService: UserServiceProtocol

    // MARK: - Keys
    private enum Keys {
        static let notificationSettings = "neuronexa_notification_settings"
        static let privacySettings = "neuronexa_privacy_settings"
        static let hasCompletedOnboarding = "neuronexa_onboarding_completed"
        static let appVersion = "neuronexa_app_version"
    }

    // MARK: - Initialization

    init(
        userDefaults: UserDefaults = .standard,
        userService: UserServiceProtocol = DependencyContainer.shared.userService
    ) {
        self.userDefaults = userDefaults
        self.userService = userService
    }

    // MARK: - Public Methods

    func loadSettings() async throws -> AppSettings {
        // Load user profile
        let userProfile = try await userService.getCurrentProfile()

        // Load notification settings
        let notificationSettings = loadNotificationSettings()

        // Load privacy settings
        let privacySettings = loadPrivacySettings()

        return AppSettings(
            userProfile: userProfile,
            notificationSettings: notificationSettings,
            privacySettings: privacySettings
        )
    }

    func saveSettings(_ settings: AppSettings) async throws {
        // Save user profile
        try await userService.updateProfile(settings.userProfile)

        // Save notification settings
        saveNotificationSettings(settings.notificationSettings)

        // Save privacy settings
        savePrivacySettings(settings.privacySettings)
    }

    func clearAllData() async throws {
        // Clear user defaults
        guard let domain = Bundle.main.bundleIdentifier else {
            throw SettingsError.invalidConfiguration
        }
        userDefaults.removePersistentDomain(forName: domain)
        userDefaults.synchronize()

        // Clear user service data
        try await userService.clearUserData()

        // Clear any cached data
        clearCaches()
    }

    // MARK: - Notification Settings

    private func loadNotificationSettings() -> NotificationSettings {
        guard let data = userDefaults.data(forKey: Keys.notificationSettings),
              let settings = try? JSONDecoder().decode(NotificationSettings.self, from: data) else {
            return .default
        }
        return settings
    }

    private func saveNotificationSettings(_ settings: NotificationSettings) {
        guard let data = try? JSONEncoder().encode(settings) else { return }
        userDefaults.set(data, forKey: Keys.notificationSettings)
    }

    // MARK: - Privacy Settings

    private func loadPrivacySettings() -> PrivacySettings {
        guard let data = userDefaults.data(forKey: Keys.privacySettings),
              let settings = try? JSONDecoder().decode(PrivacySettings.self, from: data) else {
            return .default
        }
        return settings
    }

    private func savePrivacySettings(_ settings: PrivacySettings) {
        guard let data = try? JSONEncoder().encode(settings) else { return }
        userDefaults.set(data, forKey: Keys.privacySettings)
    }

    // MARK: - App State

    func hasCompletedOnboarding() -> Bool {
        userDefaults.bool(forKey: Keys.hasCompletedOnboarding)
    }

    func setOnboardingCompleted(_ completed: Bool) {
        userDefaults.set(completed, forKey: Keys.hasCompletedOnboarding)
    }

    func getCurrentAppVersion() -> String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }

    func getStoredAppVersion() -> String? {
        userDefaults.string(forKey: Keys.appVersion)
    }

    func setStoredAppVersion(_ version: String) {
        userDefaults.set(version, forKey: Keys.appVersion)
    }

    func isFirstLaunchAfterUpdate() -> Bool {
        let currentVersion = getCurrentAppVersion()
        let storedVersion = getStoredAppVersion()

        if storedVersion == nil {
            // First launch ever
            setStoredAppVersion(currentVersion)
            return true
        }

        if storedVersion != currentVersion {
            // App was updated
            setStoredAppVersion(currentVersion)
            return true
        }

        return false
    }

    // MARK: - Private Methods

    private func clearCaches() {
        // Clear any in-memory caches
        // Clear temporary files
        let tempDirectory = FileManager.default.temporaryDirectory
        try? FileManager.default.removeItem(at: tempDirectory)

        // Clear URL cache
        URLCache.shared.removeAllCachedResponses()
    }
}

// MARK: - Default Settings

extension NotificationSettings {
    static let `default` = NotificationSettings(
        taskRemindersEnabled: true,
        breakNotificationsEnabled: true,
        routineRemindersEnabled: true,
        moodCheckInsEnabled: true,
        reminderStyle: .gentle
    )
}

extension PrivacySettings {
    static let `default` = PrivacySettings(
        healthDataSharingEnabled: false,
        analyticsEnabled: true,
        crashReportingEnabled: true
    )
}

// MARK: - Settings Error Types

enum SettingsError: Error, LocalizedError {
    case failedToLoadSettings
    case failedToSaveSettings
    case failedToClearData
    case invalidSettingsData
    case invalidConfiguration

    var errorDescription: String? {
        switch self {
        case .failedToLoadSettings:
            return "Failed to load app settings"
        case .failedToSaveSettings:
            return "Failed to save app settings"
        case .failedToClearData:
            return "Failed to clear app data"
        case .invalidSettingsData:
            return "Invalid settings data format"
        case .invalidConfiguration:
            return "Invalid app configuration"
        }
    }
}

// MARK: - Codable Conformance

extension NotificationSettings: Codable {}
extension PrivacySettings: Codable {}
extension ReminderStyle: Codable {}
