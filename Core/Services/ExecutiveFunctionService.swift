import Foundation

// MARK: - Executive Function Service Protocol

@available(iOS 18.0, *)
public protocol ExecutiveFunctionServiceProtocol {
    func analyzeTaskPatterns(_ completions: [TaskCompletion]) async -> ExecutiveFunctionInsights
    func generateTaskBreakdown(_ task: AITask, level: TaskBreakdownLevel) async throws -> [TaskStep]
    func suggestTaskSequencing(_ tasks: [AITask]) async throws -> [AITask]
    func provideExecutiveSupport(_ task: AITask, userProfile: UserProfile) async throws -> ExecutiveSupport
    func trackExecutiveFunction(_ completion: TaskCompletion) async throws
}

// MARK: - Executive Function Service Implementation

@available(iOS 18.0, *)
@MainActor
public class ExecutiveFunctionService: ExecutiveFunctionServiceProtocol {
    // MARK: - Properties

    private var taskCompletions: [TaskCompletion] = []
    private var executiveFunctionData: [ExecutiveFunctionMetric] = []

    // MARK: - Executive Function Analysis

    func analyzeTaskPatterns(_ completions: [TaskCompletion]) async -> ExecutiveFunctionInsights {
        // Analyze patterns in task completion data
        let averageCompletionTime = completions.map { $0.actualDuration }.reduce(0, +) / Double(completions.count)
        let completionRate = Double(completions.filter { $0.isCompleted }.count) / Double(completions.count)

        let strengths = identifyExecutiveStrengths(completions)
        let challenges = identifyExecutiveChallenges(completions)
        let recommendations = generateExecutiveRecommendations(completions)

        return ExecutiveFunctionInsights(
            averageCompletionTime: averageCompletionTime,
            completionRate: completionRate,
            strengths: strengths,
            challenges: challenges,
            recommendations: recommendations,
            analysisDate: Date()
        )
    }

    func generateTaskBreakdown(_ task: AITask, level: TaskBreakdownLevel) async throws -> [TaskStep] {
        // Generate task breakdown based on complexity level
        var steps: [TaskStep] = []

        switch level {
        case .minimal:
            steps = generateMinimalBreakdown(task)
        case .standard:
            steps = generateStandardBreakdown(task)
        case .detailed:
            steps = generateDetailedBreakdown(task)
        case .comprehensive:
            steps = generateComprehensiveBreakdown(task)
        }

        return steps
    }

    func suggestTaskSequencing(_ tasks: [AITask]) async throws -> [AITask] {
        // Sort tasks based on executive function principles
        tasks.sorted { task1, task2 in
            // Prioritize by cognitive load, then by priority
            if task1.cognitiveLoad != task2.cognitiveLoad {
                return task1.cognitiveLoad.rawValue < task2.cognitiveLoad.rawValue
            }
            return task1.priority.rawValue > task2.priority.rawValue
        }
    }

    func provideExecutiveSupport(_ task: AITask, userProfile: UserProfile) async throws -> ExecutiveSupport {
        // Generate executive function support based on user profile and task
        let strategies = generateExecutiveStrategies(for: task, userProfile: userProfile)
        let reminders = generateAdaptiveReminders(for: task, userProfile: userProfile)
        let scaffolding = generateCognitiveScaffolding(for: task, userProfile: userProfile)

        return ExecutiveSupport(
            strategies: strategies,
            reminders: reminders,
            scaffolding: scaffolding,
            estimatedDuration: task.estimatedDuration,
            breakSuggestions: generateBreakSuggestions(for: task)
        )
    }

    func trackExecutiveFunction(_ completion: TaskCompletion) async throws {
        taskCompletions.append(completion)

        // Analyze executive function metrics
        let metric = ExecutiveFunctionMetric(
            taskId: completion.taskId,
            planningTime: completion.planningTime ?? 0,
            executionTime: completion.actualDuration,
            distractionCount: completion.distractionCount ?? 0,
            completionQuality: completion.completionQuality ?? 0.5,
            timestamp: completion.completedAt ?? Date()
        )

        executiveFunctionData.append(metric)
    }

    // MARK: - Private Helper Methods

    private func identifyExecutiveStrengths(_ completions: [TaskCompletion]) -> [ExecutiveStrength] {
        var strengths: [ExecutiveStrength] = []

        let avgCompletionRate = Double(completions.filter { $0.isCompleted }.count) / Double(completions.count)
        if avgCompletionRate > 0.8 {
            strengths.append(.taskCompletion)
        }

        let avgPlanningTime = completions.compactMap { $0.planningTime }.reduce(0, +) / Double(completions.count)
        if avgPlanningTime > 0 && avgPlanningTime < 300 { // Less than 5 minutes
            strengths.append(.planning)
        }

        return strengths
    }

    private func identifyExecutiveChallenges(_ completions: [TaskCompletion]) -> [ExecutiveChallenge] {
        var challenges: [ExecutiveChallenge] = []

        let avgDistractionCount = completions.compactMap { $0.distractionCount }.reduce(0, +) / completions.count
        if avgDistractionCount > 3 {
            challenges.append(.distractibility)
        }

        let incompleteTasks = completions.filter { !$0.isCompleted }.count
        if Double(incompleteTasks) / Double(completions.count) > 0.3 {
            challenges.append(.taskInitiation)
        }

        return challenges
    }

    private func generateExecutiveRecommendations(_ completions: [TaskCompletion]) -> [ExecutiveRecommendation] {
        var recommendations: [ExecutiveRecommendation] = []

        let challenges = identifyExecutiveChallenges(completions)

        if challenges.contains(.distractibility) {
            recommendations.append(ExecutiveRecommendation(
                type: .environmentalModification,
                title: "Reduce Distractions",
                description: "Consider using focus mode or finding a quieter workspace"
            ))
        }

        if challenges.contains(.taskInitiation) {
            recommendations.append(ExecutiveRecommendation(
                type: .strategicSupport,
                title: "Break Down Tasks",
                description: "Try breaking larger tasks into smaller, more manageable steps"
            ))
        }

        return recommendations
    }

    private func generateMinimalBreakdown(_ task: AITask) -> [TaskStep] {
        [
            TaskStep(title: "Complete: \(task.title)", description: task.description, estimatedDuration: task.estimatedDuration)
        ]
    }

    private func generateStandardBreakdown(_ task: AITask) -> [TaskStep] {
        [
            TaskStep(title: "Plan approach", description: "Review task requirements", estimatedDuration: task.estimatedDuration * 0.1),
            TaskStep(title: "Execute task", description: task.description, estimatedDuration: task.estimatedDuration * 0.8),
            TaskStep(title: "Review and finalize", description: "Check work and make final adjustments", estimatedDuration: task.estimatedDuration * 0.1)
        ]
    }

    private func generateDetailedBreakdown(_ task: AITask) -> [TaskStep] {
        [
            TaskStep(title: "Gather materials", description: "Collect all necessary resources", estimatedDuration: task.estimatedDuration * 0.1),
            TaskStep(title: "Plan approach", description: "Create detailed plan", estimatedDuration: task.estimatedDuration * 0.15),
            TaskStep(title: "Begin execution", description: "Start working on main task", estimatedDuration: task.estimatedDuration * 0.35),
            TaskStep(title: "Mid-point review", description: "Check progress and adjust", estimatedDuration: task.estimatedDuration * 0.1),
            TaskStep(title: "Complete execution", description: "Finish main work", estimatedDuration: task.estimatedDuration * 0.2),
            TaskStep(title: "Final review", description: "Review and polish", estimatedDuration: task.estimatedDuration * 0.1)
        ]
    }

    private func generateComprehensiveBreakdown(_ task: AITask) -> [TaskStep] {
        // Most detailed breakdown for users who need maximum support
        generateDetailedBreakdown(task) + [
            TaskStep(title: "Document learnings", description: "Note what worked well", estimatedDuration: 300),
            TaskStep(title: "Plan next steps", description: "Identify follow-up actions", estimatedDuration: 300)
        ]
    }

    private func generateExecutiveStrategies(for task: AITask, userProfile: UserProfile) -> [ExecutiveStrategy] {
        // Generate strategies based on user's executive function needs
        [
            ExecutiveStrategy(type: .timeManagement, description: "Use Pomodoro technique"),
            ExecutiveStrategy(type: .organization, description: "Create checklist for task steps")
        ]
    }

    private func generateAdaptiveReminders(for task: AITask, userProfile: UserProfile) -> [AdaptiveReminder] {
        [
            AdaptiveReminder(type: .timeCheck, message: "How are you progressing?", interval: 900), // 15 minutes
            AdaptiveReminder(type: .breakSuggestion, message: "Consider taking a short break", interval: 1_800) // 30 minutes
        ]
    }

    private func generateCognitiveScaffolding(for task: AITask, userProfile: UserProfile) -> [CognitiveScaffold] {
        [
            CognitiveScaffold(type: .planning, description: "Break task into smaller steps"),
            CognitiveScaffold(type: .monitoring, description: "Check progress regularly")
        ]
    }

    private func generateBreakSuggestions(for task: AITask) -> [BreakSuggestion] {
        [
            BreakSuggestion(type: .movement, duration: 300, description: "Take a 5-minute walk"),
            BreakSuggestion(type: .breathing, duration: 180, description: "Do breathing exercises")
        ]
    }
}

// MARK: - Executive Function Models

struct ExecutiveFunctionInsights {
    let averageCompletionTime: Double
    let completionRate: Double
    let strengths: [ExecutiveStrength]
    let challenges: [ExecutiveChallenge]
    let recommendations: [ExecutiveRecommendation]
    let analysisDate: Date
}

struct ExecutiveFunctionMetric {
    let taskId: UUID
    let planningTime: TimeInterval
    let executionTime: TimeInterval
    let distractionCount: Int
    let completionQuality: Double
    let timestamp: Date
}

struct ExecutiveSupport {
    let strategies: [ExecutiveStrategy]
    let reminders: [AdaptiveReminder]
    let scaffolding: [CognitiveScaffold]
    let estimatedDuration: TimeInterval
    let breakSuggestions: [BreakSuggestion]
}

struct TaskStep {
    let id = UUID()
    let title: String
    let description: String
    let estimatedDuration: TimeInterval
    var isCompleted = false
}

struct ExecutiveStrategy {
    let type: StrategyType
    let description: String
}

struct AdaptiveReminder {
    let type: ReminderType
    let message: String
    let interval: TimeInterval
}

struct CognitiveScaffold {
    let type: ScaffoldType
    let description: String
}

struct BreakSuggestion {
    let type: BreakType
    let duration: TimeInterval
    let description: String
}

struct ExecutiveRecommendation {
    let type: RecommendationType
    let title: String
    let description: String
}

enum ExecutiveStrength: String, CaseIterable {
    case planning
    case taskCompletion
    case timeManagement
    case organization
    case flexibility
}

enum ExecutiveChallenge: String, CaseIterable {
    case taskInitiation
    case distractibility
    case timeManagement
    case organization
    case workingMemory
}

enum StrategyType: String, CaseIterable {
    case timeManagement
    case organization
    case attention
    case memory
}

enum ReminderType: String, CaseIterable {
    case timeCheck
    case breakSuggestion
    case focusPrompt
    case progressCheck
}

enum ScaffoldType: String, CaseIterable {
    case planning
    case monitoring
    case evaluation
    case organization
}

enum BreakType: String, CaseIterable {
    case movement
    case breathing
    case mindfulness
    case hydration
}

enum RecommendationType: String, CaseIterable {
    case strategicSupport
    case environmentalModification
    case toolRecommendation
    case behavioralStrategy
}
