import CoreData
import Foundation

// MARK: - Core Data Service Protocol

@available(iOS 18.0, *)
public protocol CoreDataServiceProtocol {
    func saveUserProfile(_ profile: UserProfile) async throws
    func deleteUserProfile() async throws
    func saveContext() async throws
    
    // Claude: Adding missing methods for breathing service compatibility
    func getBreathingHistory(for timeframe: BehaviorTimeframe) async throws -> [BreathingSessionResult]
    func saveBreathingSessionResult(_ result: BreathingSessionResult) async throws
}

// MARK: - Core Data Service Implementation

@available(iOS 18.0, *)
@MainActor
public class CoreDataService: CoreDataServiceProtocol {
    // MARK: - Core Data Stack

    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "NeuroNexa")
        container.loadPersistentStores { _, error in
            if let error = error as NSError? {
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        }
        return container
    }()

    var context: NSManagedObjectContext {
        persistentContainer.viewContext
    }

    // MARK: - User Profile Operations

    func saveUserProfile(_ profile: UserProfile) async throws {
        // For now, this is a placeholder implementation
        // In a full implementation, this would save to Core Data
        print("Saving user profile to Core Data: \(profile.name)")
        try await saveContext()
    }

    func deleteUserProfile() async throws {
        // For now, this is a placeholder implementation
        // In a full implementation, this would delete from Core Data
        print("Deleting user profile from Core Data")
        try await saveContext()
    }

    func saveContext() async throws {
        if context.hasChanges {
            try context.save()
        }
    }
    
    // MARK: - Breathing Session Operations
    
    // Claude: Adding missing breathing session methods
    func getBreathingHistory(for timeframe: BehaviorTimeframe) async throws -> [BreathingSessionResult] {
        // For now, this is a placeholder implementation
        // In a full implementation, this would fetch from Core Data
        print("Fetching breathing history for timeframe: \(timeframe)")
        return [] // Return empty array for now
    }
    
    func saveBreathingSessionResult(_ result: BreathingSessionResult) async throws {
        // For now, this is a placeholder implementation
        // In a full implementation, this would save to Core Data
        print("Saving breathing session result: \(result.sessionId)")
        try await saveContext()
    }
}

// MARK: - Core Data Service Errors

enum CoreDataServiceError: Error, LocalizedError {
    case failedToSave
    case failedToDelete
    case failedToFetch
    case contextNotAvailable

    var errorDescription: String? {
        switch self {
        case .failedToSave:
            return "Failed to save to Core Data"
        case .failedToDelete:
            return "Failed to delete from Core Data"
        case .failedToFetch:
            return "Failed to fetch from Core Data"
        case .contextNotAvailable:
            return "Core Data context not available"
        }
    }
}
