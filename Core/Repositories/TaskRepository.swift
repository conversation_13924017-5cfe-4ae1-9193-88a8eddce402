import CloudKit
import CoreData
import Foundation

// MARK: - Task Repository Protocol

@available(iOS 18.0, *)
protocol TaskRepositoryProtocol {
    func getAllTasks() async throws -> [AITask]
    func getActiveTasks() async throws -> [AITask]
    func getCompletedTasks() async throws -> [AITask]
    func getTask(by id: UUID) async throws -> AITask?
    func saveTask(_ task: AITask) async throws
    func updateTask(_ task: AITask) async throws
    func deleteTask(_ task: AITask) async throws
    func getTasksByCategory(_ category: TaskCategory) async throws -> [AITask]
    func getTasksByPriority(_ priority: TaskPriority) async throws -> [AITask]
    func searchTasks(query: String) async throws -> [AITask]
    func syncWithCloud() async throws
}

// MARK: - Task Repository Implementation

@available(iOS 18.0, *)
@MainActor
class TaskRepository: TaskRepositoryProtocol {
    // MARK: - Properties

    nonisolated(unsafe) private let coreDataService: CoreDataServiceProtocol
    nonisolated(unsafe) private let cloudKitService: CloudKitServiceProtocol
    private let userDefaults = UserDefaults.standard

    // MARK: - Initialization

    init(
        coreDataService: CoreDataServiceProtocol,
        cloudKitService: CloudKitServiceProtocol
    ) {
        self.coreDataService = coreDataService
        self.cloudKitService = cloudKitService
    }

    convenience init() {
        let coreDataService = DependencyContainer.shared.coreDataService
        let cloudKitService = DependencyContainer.shared.cloudKitService
        self.init(coreDataService: coreDataService, cloudKitService: cloudKitService)
    }

    // MARK: - Repository Methods

    func getAllTasks() async throws -> [AITask] {
        let localTasks = try await coreDataService.getAllTasks()

        // Sync with cloud if needed
        try await syncWithCloud()

        return localTasks.sorted(by: { $0.id.uuidString > $1.id.uuidString })
    }

    func getActiveTasks() async throws -> [AITask] {
        let allTasks = try await getAllTasks()
        return allTasks.filter { !$0.isCompleted }
    }

    func getCompletedTasks() async throws -> [AITask] {
        let allTasks = try await getAllTasks()
        return allTasks.filter { $0.isCompleted }
    }

    func getTask(by id: UUID) async throws -> AITask? {
        // First try Core Data
        if let task = try await coreDataService.getTask(by: id) {
            return task
        }

        // If not found locally, try CloudKit
        if let cloudTask = try await cloudKitService.getTask(by: id) {
            // Save to Core Data for caching
            try await coreDataService.saveTask(cloudTask)
            return cloudTask
        }

        return nil
    }

    func saveTask(_ task: AITask) async throws {
        // Save to Core Data
        try await coreDataService.saveTask(task)

        // Save to CloudKit
        try await cloudKitService.saveTask(task)

        // Update cache
        updateTaskCache(task)
    }

    func updateTask(_ task: AITask) async throws {
        // Update in Core Data
        try await coreDataService.updateTask(task)

        // Update in CloudKit
        try await cloudKitService.updateTask(task)

        // Update cache
        updateTaskCache(task)
    }

    func deleteTask(_ task: AITask) async throws {
        // Delete from Core Data
        try await coreDataService.deleteTask(task)

        // Delete from CloudKit
        try await cloudKitService.deleteTask(task)

        // Clear cache
        clearTaskCache(task.id)
    }

    func getTasksByCategory(_ category: TaskCategory) async throws -> [AITask] {
        let allTasks = try await getAllTasks()
        // Since AITask doesn't have a category property, return all tasks for now
        // NOTE: Add category property to AITask or implement categorization logic
        return allTasks
    }

    func getTasksByPriority(_ priority: TaskPriority) async throws -> [AITask] {
        let allTasks = try await getAllTasks()
        return allTasks.filter { $0.priority == priority }
    }

    func searchTasks(query: String) async throws -> [AITask] {
        let allTasks = try await getAllTasks()
        let lowercaseQuery = query.lowercased()

        return allTasks.filter { task in
            task.title.lowercased().contains(lowercaseQuery) ||
                task.description.lowercased().contains(lowercaseQuery)
            // Note: AITask doesn't have tags property, so we only search title and description
        }
    }

    func syncWithCloud() async throws {
        // Get CloudKit account status
        let accountStatus = try await cloudKitService.getAccountStatus()

        guard accountStatus == .available else {
            throw TaskRepositoryError.cloudKitNotAvailable
        }

        // Get all local tasks
        let localTasks = try await coreDataService.getAllTasks()

        // Get all cloud tasks
        let cloudTasks = try await cloudKitService.getAllTasks()

        // Sync logic: merge local and cloud data
        try await performSync(localTasks: localTasks, cloudTasks: cloudTasks)
    }

    // MARK: - Private Helper Methods

    private func updateTaskCache(_ task: AITask) {
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(task) {
            userDefaults.set(data, forKey: "cached_task_\(task.id.uuidString)")
        }
    }

    private func clearTaskCache(_ taskId: UUID) {
        userDefaults.removeObject(forKey: "cached_task_\(taskId.uuidString)")
    }

    private func performSync(localTasks: [AITask], cloudTasks: [AITask]) async throws {
        var localTasksDict = Dictionary(uniqueKeysWithValues: localTasks.map { ($0.id, $0) })
        let cloudTasksDict = Dictionary(uniqueKeysWithValues: cloudTasks.map { ($0.id, $0) })

        // Process cloud tasks
        for (taskId, cloudTask) in cloudTasksDict {
            if let localTask = localTasksDict[taskId] {
                // Task exists in both - since AITask doesn't have lastModified,
                // we'll prioritize cloud version for consistency
                // NOTE: Add lastModified property to AITask for proper conflict resolution
                try await coreDataService.updateTask(cloudTask)
                // Remove from local dict as it's been processed
                localTasksDict.removeValue(forKey: taskId)
            } else {
                // Task only exists in cloud - add to local
                try await coreDataService.saveTask(cloudTask)
            }
        }

        // Remaining local tasks don't exist in cloud - upload them
        for (_, localTask) in localTasksDict {
            try await cloudKitService.saveTask(localTask)
        }
    }
}

// MARK: - Repository Errors

@available(iOS 18.0, *)
enum TaskRepositoryError: Error, LocalizedError {
    case taskNotFound
    case cloudKitNotAvailable
    case syncFailed
    case dataCorrupted
    case networkError
    case invalidTaskData

    var errorDescription: String? {
        switch self {
        case .taskNotFound:
            return "Task not found"
        case .cloudKitNotAvailable:
            return "CloudKit is not available"
        case .syncFailed:
            return "Failed to sync with cloud"
        case .dataCorrupted:
            return "Task data is corrupted"
        case .networkError:
            return "Network error occurred"
        case .invalidTaskData:
            return "Invalid task data provided"
        }
    }
}

// MARK: - Core Data Extensions

@available(iOS 18.0, *)
extension CoreDataServiceProtocol {
    func getAllTasks() async throws -> [AITask] {
        // Implementation would fetch all tasks from Core Data
        // For now, return empty array as placeholder
        []
    }

    func getTask(by id: UUID) async throws -> AITask? {
        // Implementation would fetch specific task from Core Data
        // For now, return nil as placeholder
        nil
    }

    func saveTask(_ task: AITask) async throws {
        // Implementation would save task to Core Data
    }

    func updateTask(_ task: AITask) async throws {
        // Implementation would update task in Core Data
    }

    func deleteTask(_ task: AITask) async throws {
        // Implementation would delete task from Core Data
    }
}

// MARK: - CloudKit Extensions

@available(iOS 18.0, *)
extension CloudKitServiceProtocol {
    func getAllTasks() async throws -> [AITask] {
        // Implementation would fetch all tasks from CloudKit
        // For now, return empty array as placeholder
        []
    }

    func getTask(by id: UUID) async throws -> AITask? {
        // Implementation would fetch specific task from CloudKit
        // For now, return nil as placeholder
        nil
    }

    func saveTask(_ task: AITask) async throws {
        // Implementation would save task to CloudKit
    }

    func updateTask(_ task: AITask) async throws {
        // Implementation would update task in CloudKit
    }

    func deleteTask(_ task: AITask) async throws {
        // Implementation would delete task from CloudKit
    }
}
