import CloudKit
import CoreData
import Foundation

// MARK: - Repository Implementation

// MARK: - User Repository Implementation

@available(iOS 18.0, *)
@MainActor
class UserRepository: UserRepositoryProtocol {
    // MARK: - Properties

    nonisolated(unsafe) private let userProfileRepository: UserProfileRepositoryProtocol

    // MARK: - Initialization

    init(userProfileRepository: UserProfileRepositoryProtocol = DefaultUserProfileRepository()) {
        self.userProfileRepository = userProfileRepository
    }

    // MARK: - UserRepositoryProtocol Implementation

    func getCurrentUser() async throws -> UserProfile? {
        // For now, get the first user profile as current user
        let profiles = try await userProfileRepository.getAllUserProfiles()
        return profiles.first
    }

    func saveUser(_ user: UserProfile) async throws {
        try await userProfileRepository.saveUserProfile(user)
    }

    func updateUser(_ user: User<PERSON>rofile) async throws {
        try await userProfileRepository.updateUserProfile(user)
    }

    func deleteUser(_ user: UserProfile) async throws {
        try await userProfileRepository.deleteUserProfile(for: user.id)
    }

    func getUserById(_ id: UUID) async throws -> UserProfile? {
        try await userProfileRepository.getUserProfile(for: id)
    }

    func getUserByEmail(_ email: String) async throws -> UserProfile? {
        // Get all profiles and filter by email
        let profiles = try await userProfileRepository.getAllUserProfiles()
        return profiles.first { $0.email == email }
    }

    func createUser(_ user: UserProfile) async throws {
        try await userProfileRepository.saveUserProfile(user)
    }

    func updateUserPreferences(_ preferences: SimpleUserPreferencesForProfile, for userId: UUID) async throws {
        // Get the user profile and update preferences
        guard var userProfile = try await userProfileRepository.getUserProfile(for: userId) else {
            throw UserRepositoryError.userNotFound
        }

        userProfile.preferences = preferences
        try await userProfileRepository.updateUserProfile(userProfile)
    }

    func getUserPreferences(for userId: UUID) async throws -> SimpleUserPreferencesForProfile? {
        guard let userProfile = try await userProfileRepository.getUserProfile(for: userId) else {
            return nil
        }
        return userProfile.preferences
    }

    func updateCognitivePreferences(_ preferences: SimpleCognitivePreferencesForProfile, for userId: UUID) async throws {
        // Get the user profile and update cognitive preferences
        guard var userProfile = try await userProfileRepository.getUserProfile(for: userId) else {
            throw UserRepositoryError.userNotFound
        }

        userProfile.cognitivePreferences = preferences
        try await userProfileRepository.updateUserProfile(userProfile)
    }

    func getCognitivePreferences(for userId: UUID) async throws -> SimpleCognitivePreferencesForProfile? {
        guard let userProfile = try await userProfileRepository.getUserProfile(for: userId) else {
            return nil
        }
        return userProfile.cognitivePreferences
    }

    func syncUserData() async throws {
        try await userProfileRepository.syncWithCloud()
    }

    func clearUserData() async throws {
        let profiles = try await userProfileRepository.getAllUserProfiles()
        for profile in profiles {
            try await userProfileRepository.deleteUserProfile(for: profile.id)
        }
    }
}

// MARK: - User Repository Errors

enum UserRepositoryError: Error, LocalizedError {
    case userNotFound
    case invalidUserData
    case syncFailed

    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "User not found"
        case .invalidUserData:
            return "Invalid user data"
        case .syncFailed:
            return "Failed to sync user data"
        }
    }
}
