import CloudKit
import CoreData
import Foundation

// MARK: - User Profile Repository Protocol
// UserProfileRepositoryProtocol is defined in Core/Architecture/DependencyContainer.swift

// MARK: - User Profile Repository Implementation

@available(iOS 18.0, *)
@MainActor
class UserProfileRepository: UserProfileRepositoryProtocol {
    // MARK: - Properties

    private let coreDataService: CoreDataServiceProtocol
    private let cloudKitService: CloudKitServiceProtocol
    private let userDefaults = UserDefaults.standard

    // MARK: - Initialization

    init(
        coreDataService: CoreDataServiceProtocol,
        cloudKitService: CloudKitServiceProtocol
    ) {
        self.coreDataService = coreDataService
        self.cloudKitService = cloudKitService
    }

    convenience init() {
        let coreDataService = CoreDataService()
        let cloudKitService = CloudKitService()
        self.init(coreDataService: coreDataService, cloudKitService: cloudKitService)
    }

    // MARK: - Repository Methods

    func getUserProfile(for userId: UUID) async throws -> UserProfile? {
        // First try to get from Core Data
        if let profile = try await coreDataService.getUserProfile(for: userId) {
            return profile
        }

        // If not found locally, try CloudKit
        if let cloudProfile = try await cloudKitService.getUserProfile(for: userId) {
            // Save to Core Data for caching
            try await coreDataService.saveUserProfile(cloudProfile)
            return cloudProfile
        }

        return nil
    }

    func saveUserProfile(_ profile: UserProfile) async throws {
        // Save to Core Data
        try await coreDataService.saveUserProfile(profile)

        // Save to CloudKit
        try await cloudKitService.saveUserProfile(profile)

        // Update UserDefaults cache for quick access
        updateUserDefaultsCache(profile)
    }

    func updateUserProfile(_ profile: UserProfile) async throws {
        // Update in Core Data
        try await coreDataService.updateUserProfile(profile)

        // Update in CloudKit
        try await cloudKitService.updateUserProfile(profile)

        // Update UserDefaults cache
        updateUserDefaultsCache(profile)
    }

    func deleteUserProfile(for userId: UUID) async throws {
        // Delete from Core Data
        try await coreDataService.deleteUserProfile(for: userId)

        // Delete from CloudKit
        try await cloudKitService.deleteUserProfile(for: userId)

        // Clear UserDefaults cache
        clearUserDefaultsCache(for: userId)
    }

    func getAllUserProfiles() async throws -> [UserProfile] {
        // Get all profiles from Core Data
        let localProfiles = try await coreDataService.getAllUserProfiles()

        // Sync with CloudKit if needed
        try await syncWithCloud()

        return localProfiles
    }

    func syncWithCloud() async throws {
        // Get CloudKit account status
        let accountStatus = try await cloudKitService.getAccountStatus()

        guard accountStatus == .available else {
            throw UserProfileRepositoryError.cloudKitNotAvailable
        }

        // Get all local profiles
        let localProfiles = try await coreDataService.getAllUserProfiles()

        // Get all cloud profiles
        let cloudProfiles = try await cloudKitService.getAllUserProfiles()

        // Sync logic: merge local and cloud data
        try await performSync(localProfiles: localProfiles, cloudProfiles: cloudProfiles)
    }

    // MARK: - Private Helper Methods

    private func updateUserDefaultsCache(_ profile: UserProfile) {
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(profile) {
            userDefaults.set(data, forKey: "cached_user_profile_\(profile.id.uuidString)")
        }
    }

    private func clearUserDefaultsCache(for userId: UUID) {
        userDefaults.removeObject(forKey: "cached_user_profile_\(userId.uuidString)")
    }

    private func performSync(localProfiles: [UserProfile], cloudProfiles: [UserProfile]) async throws {
        var localProfilesDict = Dictionary(uniqueKeysWithValues: localProfiles.map { ($0.id, $0) })
        let cloudProfilesDict = Dictionary(uniqueKeysWithValues: cloudProfiles.map { ($0.id, $0) })

        // Process cloud profiles
        for (profileId, cloudProfile) in cloudProfilesDict {
            if let localProfile = localProfilesDict[profileId] {
                // Profile exists in both - check which is newer
                if cloudProfile.lastModified > localProfile.lastModified {
                    // Cloud is newer - update local
                    try await coreDataService.updateUserProfile(cloudProfile)
                } else if localProfile.lastModified > cloudProfile.lastModified {
                    // Local is newer - update cloud
                    try await cloudKitService.updateUserProfile(localProfile)
                }
                // Remove from local dict as it's been processed
                localProfilesDict.removeValue(forKey: profileId)
            } else {
                // Profile only exists in cloud - add to local
                try await coreDataService.saveUserProfile(cloudProfile)
            }
        }

        // Remaining local profiles don't exist in cloud - upload them
        for (_, localProfile) in localProfilesDict {
            try await cloudKitService.saveUserProfile(localProfile)
        }
    }
}

// MARK: - Repository Errors

@available(iOS 18.0, *)
enum UserProfileRepositoryError: Error, LocalizedError {
    case profileNotFound
    case cloudKitNotAvailable
    case syncFailed
    case dataCorrupted
    case networkError

    var errorDescription: String? {
        switch self {
        case .profileNotFound:
            return "User profile not found"
        case .cloudKitNotAvailable:
            return "CloudKit is not available"
        case .syncFailed:
            return "Failed to sync with cloud"
        case .dataCorrupted:
            return "User profile data is corrupted"
        case .networkError:
            return "Network error occurred"
        }
    }
}

// MARK: - Core Data Extensions

@available(iOS 18.0, *)
extension CoreDataServiceProtocol {
    func getAllUserProfiles() async throws -> [UserProfile] {
        // Implementation would fetch all user profiles from Core Data
        // For now, return empty array as placeholder
        []
    }
}

// MARK: - CloudKit Extensions

@available(iOS 18.0, *)
extension CloudKitServiceProtocol {
    func getAllUserProfiles() async throws -> [UserProfile] {
        // Implementation would fetch all user profiles from CloudKit
        // For now, return empty array as placeholder
        []
    }

    func getAccountStatus() async throws -> CKAccountStatus {
        // Implementation would check CloudKit account status
        // For now, return available as placeholder
        .available
    }
}
