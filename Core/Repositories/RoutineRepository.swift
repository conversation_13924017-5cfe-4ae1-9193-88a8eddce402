import CloudKit
import CoreData
import Foundation

// MARK: - Routine Model

@available(iOS 18.0, *)
struct Routine: Codable, Identifiable, Equatable, Sendable {
    let id: UUID
    var name: String
    var description: String
    var tasks: [AITask]
    var schedule: RoutineSchedule
    var isActive: Bool
    var createdAt: Date
    var lastModified: Date
    var completionCount: Int
    var averageCompletionTime: TimeInterval
    var tags: [String]
    var neurodiversitySupport: [NeurodiversityType]

    init(
        name: String,
        description: String,
        schedule: RoutineSchedule,
        id: UUID = UUID(),
        tasks: [AITask] = [],
        isActive: Bool = true,
        createdAt: Date = Date(),
        lastModified: Date = Date(),
        completionCount: Int = 0,
        averageCompletionTime: TimeInterval = 0,
        tags: [String] = [],
        neurodiversitySupport: [NeurodiversityType] = []
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.tasks = tasks
        self.schedule = schedule
        self.isActive = isActive
        self.createdAt = createdAt
        self.lastModified = lastModified
        self.completionCount = completionCount
        self.averageCompletionTime = averageCompletionTime
        self.tags = tags
        self.neurodiversitySupport = neurodiversitySupport
    }
}

// MARK: - Routine Schedule

@available(iOS 18.0, *)
struct RoutineSchedule: Codable, Equatable, Sendable {
    var frequency: RoutineFrequency
    var timeOfDay: TimeOfDay?
    var daysOfWeek: Set<Weekday>
    var startDate: Date
    var endDate: Date?
    var reminderEnabled: Bool
    var reminderTime: TimeInterval // seconds before routine

    init(
        frequency: RoutineFrequency,
        timeOfDay: TimeOfDay? = nil,
        daysOfWeek: Set<Weekday> = Set(Weekday.allCases),
        startDate: Date = Date(),
        endDate: Date? = nil,
        reminderEnabled: Bool = true,
        reminderTime: TimeInterval = 300 // 5 minutes
    ) {
        self.frequency = frequency
        self.timeOfDay = timeOfDay
        self.daysOfWeek = daysOfWeek
        self.startDate = startDate
        self.endDate = endDate
        self.reminderEnabled = reminderEnabled
        self.reminderTime = reminderTime
    }
}

// MARK: - Supporting Enums

@available(iOS 18.0, *)
enum RoutineFrequency: String, Codable, CaseIterable, Sendable {
    case daily
    case weekly
    case biweekly
    case monthly
    case custom
}

@available(iOS 18.0, *)
enum TimeOfDay: String, Codable, CaseIterable, Sendable {
    case morning
    case afternoon
    case evening
    case night
    case anytime
}

@available(iOS 18.0, *)
enum Weekday: String, Codable, CaseIterable, Sendable {
    case monday
    case tuesday
    case wednesday
    case thursday
    case friday
    case saturday
    case sunday
}

// MARK: - Routine Repository Protocol

@available(iOS 18.0, *)
protocol RoutineRepositoryProtocol {
    func getAllRoutines() async throws -> [Routine]
    func getActiveRoutines() async throws -> [Routine]
    func getRoutine(by id: UUID) async throws -> Routine?
    func saveRoutine(_ routine: Routine) async throws
    func updateRoutine(_ routine: Routine) async throws
    func deleteRoutine(_ routine: Routine) async throws
    func getRoutinesForToday() async throws -> [Routine]
    func getRoutinesByFrequency(_ frequency: RoutineFrequency) async throws -> [Routine]
    func searchRoutines(query: String) async throws -> [Routine]
    func syncWithCloud() async throws
}

// MARK: - Routine Repository Implementation

@available(iOS 18.0, *)
@MainActor
class RoutineRepository: RoutineRepositoryProtocol {
    // MARK: - Properties

    nonisolated(unsafe) private let coreDataService: CoreDataServiceProtocol
    nonisolated(unsafe) private let cloudKitService: CloudKitServiceProtocol
    private let userDefaults = UserDefaults.standard

    // MARK: - Initialization

    init(
        coreDataService: CoreDataServiceProtocol,
        cloudKitService: CloudKitServiceProtocol
    ) {
        self.coreDataService = coreDataService
        self.cloudKitService = cloudKitService
    }

    convenience init() {
        let coreDataService = CoreDataService()
        let cloudKitService = CloudKitService()
        self.init(coreDataService: coreDataService, cloudKitService: cloudKitService)
    }

    // MARK: - Repository Methods

    func getAllRoutines() async throws -> [Routine] {
        let localRoutines = try await coreDataService.getAllRoutines()

        // Sync with cloud if needed
        try await syncWithCloud()

        return localRoutines.sorted { $0.createdAt > $1.createdAt }
    }

    func getActiveRoutines() async throws -> [Routine] {
        let allRoutines = try await getAllRoutines()
        return allRoutines.filter { $0.isActive }
    }

    func getRoutine(by id: UUID) async throws -> Routine? {
        // First try Core Data
        if let routine = try await coreDataService.getRoutine(by: id) {
            return routine
        }

        // If not found locally, try CloudKit
        if let cloudRoutine = try await cloudKitService.getRoutine(by: id) {
            // Save to Core Data for caching
            try await coreDataService.saveRoutine(cloudRoutine)
            return cloudRoutine
        }

        return nil
    }

    func saveRoutine(_ routine: Routine) async throws {
        // Save to Core Data
        try await coreDataService.saveRoutine(routine)

        // Save to CloudKit
        try await cloudKitService.saveRoutine(routine)

        // Update cache
        updateRoutineCache(routine)
    }

    func updateRoutine(_ routine: Routine) async throws {
        var updatedRoutine = routine
        updatedRoutine.lastModified = Date()

        // Update in Core Data
        try await coreDataService.updateRoutine(updatedRoutine)

        // Update in CloudKit
        try await cloudKitService.updateRoutine(updatedRoutine)

        // Update cache
        updateRoutineCache(updatedRoutine)
    }

    func deleteRoutine(_ routine: Routine) async throws {
        // Delete from Core Data
        try await coreDataService.deleteRoutine(routine)

        // Delete from CloudKit
        try await cloudKitService.deleteRoutine(routine)

        // Clear cache
        clearRoutineCache(routine.id)
    }

    func getRoutinesForToday() async throws -> [Routine] {
        let activeRoutines = try await getActiveRoutines()
        let today = Calendar.current.component(.weekday, from: Date())
        let currentWeekday = Weekday.allCases[today - 1] // Calendar weekday is 1-based

        return activeRoutines.filter { routine in
            routine.schedule.daysOfWeek.contains(currentWeekday) &&
                isRoutineScheduledForToday(routine)
        }
    }

    func getRoutinesByFrequency(_ frequency: RoutineFrequency) async throws -> [Routine] {
        let allRoutines = try await getAllRoutines()
        return allRoutines.filter { $0.schedule.frequency == frequency }
    }

    func searchRoutines(query: String) async throws -> [Routine] {
        let allRoutines = try await getAllRoutines()
        let lowercaseQuery = query.lowercased()

        return allRoutines.filter { routine in
            routine.name.lowercased().contains(lowercaseQuery) ||
                routine.description.lowercased().contains(lowercaseQuery) ||
                routine.tags.contains { $0.lowercased().contains(lowercaseQuery) }
        }
    }

    func syncWithCloud() async throws {
        // Get CloudKit account status
        let accountStatus = try await cloudKitService.getAccountStatus()

        guard accountStatus == .available else {
            throw RoutineRepositoryError.cloudKitNotAvailable
        }

        // Get all local routines
        let localRoutines = try await coreDataService.getAllRoutines()

        // Get all cloud routines
        let cloudRoutines = try await cloudKitService.getAllRoutines()

        // Sync logic: merge local and cloud data
        try await performSync(localRoutines: localRoutines, cloudRoutines: cloudRoutines)
    }

    // MARK: - Private Helper Methods

    private func updateRoutineCache(_ routine: Routine) {
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(routine) {
            userDefaults.set(data, forKey: "cached_routine_\(routine.id.uuidString)")
        }
    }

    private func clearRoutineCache(_ routineId: UUID) {
        userDefaults.removeObject(forKey: "cached_routine_\(routineId.uuidString)")
    }

    private func isRoutineScheduledForToday(_ routine: Routine) -> Bool {
        let calendar = Calendar.current
        let today = Date()

        // Check if routine is within date range
        if today < routine.schedule.startDate {
            return false
        }

        if let endDate = routine.schedule.endDate, today > endDate {
            return false
        }

        // Check frequency-specific logic
        switch routine.schedule.frequency {
        case .daily:
            return true
        case .weekly:
            let daysSinceStart = calendar.dateComponents([.day], from: routine.schedule.startDate, to: today).day ?? 0
            return daysSinceStart % 7 == 0
        case .biweekly:
            let daysSinceStart = calendar.dateComponents([.day], from: routine.schedule.startDate, to: today).day ?? 0
            return daysSinceStart % 14 == 0
        case .monthly:
            let monthsSinceStart = calendar.dateComponents([.month], from: routine.schedule.startDate, to: today).month ?? 0
            return monthsSinceStart >= 1 && calendar.component(.day, from: today) == calendar.component(.day, from: routine.schedule.startDate)
        case .custom:
            return true // Custom logic would be implemented based on specific requirements
        }
    }

    private func performSync(localRoutines: [Routine], cloudRoutines: [Routine]) async throws {
        var localRoutinesDict = Dictionary(uniqueKeysWithValues: localRoutines.map { ($0.id, $0) })
        let cloudRoutinesDict = Dictionary(uniqueKeysWithValues: cloudRoutines.map { ($0.id, $0) })

        // Process cloud routines
        for (routineId, cloudRoutine) in cloudRoutinesDict {
            if let localRoutine = localRoutinesDict[routineId] {
                // Routine exists in both - check which is newer
                if cloudRoutine.lastModified > localRoutine.lastModified {
                    // Cloud is newer - update local
                    try await coreDataService.updateRoutine(cloudRoutine)
                } else if localRoutine.lastModified > cloudRoutine.lastModified {
                    // Local is newer - update cloud
                    try await cloudKitService.updateRoutine(localRoutine)
                }
                // Remove from local dict as it's been processed
                localRoutinesDict.removeValue(forKey: routineId)
            } else {
                // Routine only exists in cloud - add to local
                try await coreDataService.saveRoutine(cloudRoutine)
            }
        }

        // Remaining local routines don't exist in cloud - upload them
        for (_, localRoutine) in localRoutinesDict {
            try await cloudKitService.saveRoutine(localRoutine)
        }
    }
}

// MARK: - Repository Errors

@available(iOS 18.0, *)
enum RoutineRepositoryError: Error, LocalizedError {
    case routineNotFound
    case cloudKitNotAvailable
    case syncFailed
    case dataCorrupted
    case networkError
    case invalidRoutineData

    var errorDescription: String? {
        switch self {
        case .routineNotFound:
            return "Routine not found"
        case .cloudKitNotAvailable:
            return "CloudKit is not available"
        case .syncFailed:
            return "Failed to sync with cloud"
        case .dataCorrupted:
            return "Routine data is corrupted"
        case .networkError:
            return "Network error occurred"
        case .invalidRoutineData:
            return "Invalid routine data provided"
        }
    }
}
