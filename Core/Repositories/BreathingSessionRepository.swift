import CloudKit
import CoreData
import Foundation

// MARK: - Breathing Session Repository Protocol

@available(iOS 18.0, *)
protocol BreathingSessionRepositoryProtocol {
    func getAllSessions() async throws -> [BreathingSession]
    func getRecentSessions(limit: Int) async throws -> [BreathingSession]
    func getSession(by id: UUID) async throws -> BreathingSession?
    func saveSession(_ session: BreathingSession) async throws
    func updateSession(_ session: BreathingSession) async throws
    func deleteSession(_ session: BreathingSession) async throws
    func getSessionsByDateRange(from startDate: Date, to endDate: Date) async throws -> [BreathingSession]
    func getSessionsByExerciseType(_ exerciseType: BreathingExerciseType) async throws -> [BreathingSession]
    func getUserStats(for userId: UUID) async throws -> BreathingStats
    func syncWithCloud() async throws
}

// MARK: - Breathing Session Repository Implementation

@available(iOS 18.0, *)
@MainActor
class BreathingSessionRepository: BreathingSessionRepositoryProtocol {
    // MARK: - Properties

    private let coreDataService: CoreDataServiceProtocol
    private let cloudKitService: CloudKitServiceProtocol
    private let userDefaults = UserDefaults.standard

    // MARK: - Initialization

    init(
        coreDataService: CoreDataServiceProtocol,
        cloudKitService: CloudKitServiceProtocol
    ) {
        self.coreDataService = coreDataService
        self.cloudKitService = cloudKitService
    }

    convenience init() {
        let coreDataService = CoreDataService()
        let cloudKitService = CloudKitService()
        self.init(coreDataService: coreDataService, cloudKitService: cloudKitService)
    }

    // MARK: - Repository Methods

    func getAllSessions() async throws -> [BreathingSession] {
        let localSessions = try await coreDataService.getAllBreathingSessions()

        // Sync with cloud if needed
        try await syncWithCloud()

        return localSessions.sorted { $0.startTime > $1.startTime }
    }

    func getRecentSessions(limit: Int = 10) async throws -> [BreathingSession] {
        let allSessions = try await getAllSessions()
        return Array(allSessions.prefix(limit))
    }

    func getSession(by id: UUID) async throws -> BreathingSession? {
        // First try Core Data
        if let session = try await coreDataService.getBreathingSession(by: id) {
            return session
        }

        // If not found locally, try CloudKit
        if let cloudSession = try await cloudKitService.getBreathingSession(by: id) {
            // Save to Core Data for caching
            try await coreDataService.saveBreathingSession(cloudSession)
            return cloudSession
        }

        return nil
    }

    func saveSession(_ session: BreathingSession) async throws {
        // Save to Core Data
        try await coreDataService.saveBreathingSession(session)

        // Save to CloudKit
        try await cloudKitService.saveBreathingSession(session)

        // Update cache
        updateSessionCache(session)

        // Update user stats
        try await updateUserStatsAfterSession(session)
    }

    func updateSession(_ session: BreathingSession) async throws {
        // Update in Core Data
        try await coreDataService.updateBreathingSession(session)

        // Update in CloudKit
        try await cloudKitService.updateBreathingSession(session)

        // Update cache
        updateSessionCache(session)
    }

    func deleteSession(_ session: BreathingSession) async throws {
        // Delete from Core Data
        try await coreDataService.deleteBreathingSession(session)

        // Delete from CloudKit
        try await cloudKitService.deleteBreathingSession(session)

        // Clear cache
        clearSessionCache(session.id)
    }

    func getSessionsByDateRange(from startDate: Date, to endDate: Date) async throws -> [BreathingSession] {
        let allSessions = try await getAllSessions()
        return allSessions.filter { session in
            session.startTime >= startDate && session.startTime <= endDate
        }
    }

    func getSessionsByExerciseType(_ exerciseType: BreathingExerciseType) async throws -> [BreathingSession] {
        let allSessions = try await getAllSessions()
        return allSessions.filter { $0.exerciseType == exerciseType }
    }

    func getUserStats(for userId: UUID) async throws -> BreathingStats {
        let userSessions = try await getUserSessions(for: userId)
        return calculateBreathingStats(from: userSessions)
    }

    func syncWithCloud() async throws {
        // Get CloudKit account status
        let accountStatus = try await cloudKitService.getAccountStatus()

        guard accountStatus == .available else {
            throw BreathingSessionRepositoryError.cloudKitNotAvailable
        }

        // Get all local sessions
        let localSessions = try await coreDataService.getAllBreathingSessions()

        // Get all cloud sessions
        let cloudSessions = try await cloudKitService.getAllBreathingSessions()

        // Sync logic: merge local and cloud data
        try await performSync(localSessions: localSessions, cloudSessions: cloudSessions)
    }

    // MARK: - Private Helper Methods

    private func updateSessionCache(_ session: BreathingSession) {
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(session) {
            userDefaults.set(data, forKey: "cached_breathing_session_\(session.id.uuidString)")
        }
    }

    private func clearSessionCache(_ sessionId: UUID) {
        userDefaults.removeObject(forKey: "cached_breathing_session_\(sessionId.uuidString)")
    }

    private func getUserSessions(for userId: UUID) async throws -> [BreathingSession] {
        let allSessions = try await getAllSessions()
        return allSessions.filter { $0.userId == userId }
    }

    private func calculateBreathingStats(from sessions: [BreathingSession]) -> BreathingStats {
        let totalSessions = sessions.count
        let totalDuration = sessions.reduce(0) { $0 + $1.duration }
        let averageDuration = totalSessions > 0 ? totalDuration / Double(totalSessions) : 0

        // Calculate streak
        let currentStreak = calculateCurrentStreak(from: sessions)
        let longestStreak = calculateLongestStreak(from: sessions)

        // Calculate exercise type distribution
        let exerciseTypeDistribution = calculateExerciseTypeDistribution(from: sessions)

        // Calculate weekly progress
        let weeklyProgress = calculateWeeklyProgress(from: sessions)

        return BreathingStats(
            id: UUID(),
            userId: sessions.first?.userId ?? UUID(),
            totalSessions: totalSessions,
            totalDuration: totalDuration,
            averageDuration: averageDuration,
            currentStreak: currentStreak,
            longestStreak: longestStreak,
            exerciseTypeDistribution: exerciseTypeDistribution,
            weeklyProgress: weeklyProgress,
            lastSessionDate: sessions.first?.startTime,
            calculatedAt: Date()
        )
    }

    private func calculateCurrentStreak(from sessions: [BreathingSession]) -> Int {
        guard !sessions.isEmpty else { return 0 }

        let sortedSessions = sessions.sorted { $0.startTime > $1.startTime }
        let calendar = Calendar.current
        var streak = 0
        var currentDate = calendar.startOfDay(for: Date())

        for session in sortedSessions {
            let sessionDate = calendar.startOfDay(for: session.startTime)

            if calendar.isDate(sessionDate, inSameDayAs: currentDate) {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else if sessionDate < currentDate {
                break
            }
        }

        return streak
    }

    private func calculateLongestStreak(from sessions: [BreathingSession]) -> Int {
        guard !sessions.isEmpty else { return 0 }

        let sortedSessions = sessions.sorted { $0.startTime < $1.startTime }
        let calendar = Calendar.current
        var longestStreak = 0
        var currentStreak = 0
        var lastDate: Date?

        for session in sortedSessions {
            let sessionDate = calendar.startOfDay(for: session.startTime)

            if let last = lastDate {
                let daysBetween = calendar.dateComponents([.day], from: last, to: sessionDate).day ?? 0

                if daysBetween == 1 {
                    currentStreak += 1
                } else if daysBetween > 1 {
                    longestStreak = max(longestStreak, currentStreak)
                    currentStreak = 1
                }
            } else {
                currentStreak = 1
            }

            lastDate = sessionDate
        }

        return max(longestStreak, currentStreak)
    }

    private func calculateExerciseTypeDistribution(from sessions: [BreathingSession]) -> [BreathingExerciseType: Int] {
        var distribution: [BreathingExerciseType: Int] = [:]

        for session in sessions {
            distribution[session.exerciseType, default: 0] += 1
        }

        return distribution
    }

    private func calculateWeeklyProgress(from sessions: [BreathingSession]) -> [WeeklyBreathingProgress] {
        let calendar = Calendar.current
        let now = Date()
        var weeklyProgress: [WeeklyBreathingProgress] = []

        // Calculate progress for the last 12 weeks
        for weekOffset in 0..<12 {
            guard let weekStart = calendar.date(byAdding: .weekOfYear, value: -weekOffset, to: now),
                  let weekEnd = calendar.date(byAdding: .day, value: 6, to: weekStart) else {
                continue
            }

            let weekSessions = sessions.filter { session in
                session.startTime >= weekStart && session.startTime <= weekEnd
            }

            let progress = WeeklyBreathingProgress(
                weekStartDate: weekStart,
                sessionCount: weekSessions.count,
                totalDuration: weekSessions.reduce(0) { $0 + $1.duration },
                averageHeartRate: calculateAverageHeartRate(from: weekSessions),
                stressReduction: calculateStressReduction(from: weekSessions)
            )

            weeklyProgress.append(progress)
        }

        return weeklyProgress.reversed() // Most recent first
    }

    private func calculateAverageHeartRate(from sessions: [BreathingSession]) -> Double {
        let heartRates = sessions.compactMap { $0.averageHeartRate }
        guard !heartRates.isEmpty else { return 0 }
        return heartRates.reduce(0, +) / Double(heartRates.count)
    }

    private func calculateStressReduction(from sessions: [BreathingSession]) -> Double {
        let stressReductions = sessions.compactMap { $0.stressReduction }
        guard !stressReductions.isEmpty else { return 0 }
        return stressReductions.reduce(0, +) / Double(stressReductions.count)
    }

    private func updateUserStatsAfterSession(_ session: BreathingSession) async throws {
        // Update user's breathing statistics after completing a session
        let currentStats = try await getUserStats(for: session.userId)

        // Cache updated stats
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(currentStats) {
            userDefaults.set(data, forKey: "breathing_stats_\(session.userId.uuidString)")
        }
    }

    private func performSync(localSessions: [BreathingSession], cloudSessions: [BreathingSession]) async throws {
        var localSessionsDict = Dictionary(uniqueKeysWithValues: localSessions.map { ($0.id, $0) })
        let cloudSessionsDict = Dictionary(uniqueKeysWithValues: cloudSessions.map { ($0.id, $0) })

        // Process cloud sessions
        for (sessionId, cloudSession) in cloudSessionsDict {
            if let localSession = localSessionsDict[sessionId] {
                // Session exists in both - check which is newer
                if cloudSession.endTime > localSession.endTime {
                    // Cloud is newer - update local
                    try await coreDataService.updateBreathingSession(cloudSession)
                } else if localSession.endTime > cloudSession.endTime {
                    // Local is newer - update cloud
                    try await cloudKitService.updateBreathingSession(localSession)
                }
                // Remove from local dict as it's been processed
                localSessionsDict.removeValue(forKey: sessionId)
            } else {
                // Session only exists in cloud - add to local
                try await coreDataService.saveBreathingSession(cloudSession)
            }
        }

        // Remaining local sessions don't exist in cloud - upload them
        for (_, localSession) in localSessionsDict {
            try await cloudKitService.saveBreathingSession(localSession)
        }
    }
}

// MARK: - Repository Errors

@available(iOS 18.0, *)
enum BreathingSessionRepositoryError: Error, LocalizedError {
    case sessionNotFound
    case cloudKitNotAvailable
    case syncFailed
    case dataCorrupted
    case networkError
    case invalidSessionData

    var errorDescription: String? {
        switch self {
        case .sessionNotFound:
            return "Breathing session not found"
        case .cloudKitNotAvailable:
            return "CloudKit is not available"
        case .syncFailed:
            return "Failed to sync with cloud"
        case .dataCorrupted:
            return "Breathing session data is corrupted"
        case .networkError:
            return "Network error occurred"
        case .invalidSessionData:
            return "Invalid breathing session data provided"
        }
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct BreathingStats: Codable {
    let id: UUID
    let userId: UUID
    let totalSessions: Int
    let totalDuration: TimeInterval
    let averageDuration: TimeInterval
    let currentStreak: Int
    let longestStreak: Int
    let exerciseTypeDistribution: [BreathingExerciseType: Int]
    let weeklyProgress: [WeeklyBreathingProgress]
    let lastSessionDate: Date?
    let calculatedAt: Date
}

@available(iOS 18.0, *)
struct WeeklyBreathingProgress: Codable {
    let weekStartDate: Date
    let sessionCount: Int
    let totalDuration: TimeInterval
    let averageHeartRate: Double
    let stressReduction: Double
}
