import Foundation

// MARK: - User Context Models

@available(iOS 18.0, *)
struct UserContext: Codable {
    let userId: UUID
    let currentState: UserState
    let cognitiveLoad: CognitiveLoadLevel
    let recentTasks: [AITask]
    let preferences: UserPreferences
    let environmentalFactors: EnvironmentalContext
    let timeContext: TimeContext
    let neurodiversityProfile: NeurodiversityProfile

    init(
        userId: UUID,
        preferences: UserPreferences,
        neurodiversityProfile: NeurodiversityProfile,
        currentState: UserState = .neutral,
        cognitiveLoad: CognitiveLoadLevel = .moderate,
        recentTasks: [AITask] = [],
        environmentalFactors: EnvironmentalContext = .default,
        timeContext: TimeContext = TimeContext()
    ) {
        self.userId = userId
        self.currentState = currentState
        self.cognitiveLoad = cognitiveLoad
        self.recentTasks = recentTasks
        self.preferences = preferences
        self.environmentalFactors = environmentalFactors
        self.timeContext = timeContext
        self.neurodiversityProfile = neurodiversityProfile
    }
}

@available(iOS 18.0, *)
enum UserState: String, CaseIterable, Codable {
    case energetic
    case focused
    case neutral
    case tired
    case overwhelmed
    case anxious
    case calm
}

@available(iOS 18.0, *)
struct EnvironmentalContext: Codable {
    let location: LocationType
    let noiseLevel: NoiseLevel
    let lightingCondition: LightingCondition
    let socialContext: SocialContext
    let timeOfDay: TimeOfDay

    static let `default` = EnvironmentalContext(
        location: .home,
        noiseLevel: .quiet,
        lightingCondition: .natural,
        socialContext: .alone,
        timeOfDay: .morning
    )
}

@available(iOS 18.0, *)
enum LocationType: String, CaseIterable, Codable {
    case home
    case work
    case school
    case `public`
    case transport
    case outdoor
}

@available(iOS 18.0, *)
enum NoiseLevel: String, CaseIterable, Codable {
    case silent
    case quiet
    case moderate
    case loud
    case overwhelming
}

@available(iOS 18.0, *)
enum LightingCondition: String, CaseIterable, Codable {
    case dim
    case natural
    case bright
    case artificial
    case harsh
}

@available(iOS 18.0, *)
enum SocialContext: String, CaseIterable, Codable {
    case alone
    case withFamily
    case withFriends
    case withColleagues
    case inGroup
    case inCrowd
}

@available(iOS 18.0, *)
enum TimeOfDay: String, CaseIterable, Codable {
    case earlyMorning
    case morning
    case midday
    case afternoon
    case evening
    case night
    case lateNight
}

@available(iOS 18.0, *)
struct TimeContext: Codable {
    let currentTime: Date
    let timeOfDay: TimeOfDay
    let dayOfWeek: DayOfWeek
    let isWorkDay: Bool
    let availableTime: TimeInterval

    init(
        currentTime: Date = Date(),
        timeOfDay: TimeOfDay? = nil,
        dayOfWeek: DayOfWeek? = nil,
        isWorkDay: Bool? = nil,
        availableTime: TimeInterval = 3_600
    ) {
        self.currentTime = currentTime
        self.timeOfDay = timeOfDay ?? TimeContext.determineTimeOfDay(from: currentTime)
        self.dayOfWeek = dayOfWeek ?? TimeContext.determineDayOfWeek(from: currentTime)
        self.isWorkDay = isWorkDay ?? TimeContext.determineIsWorkDay(from: currentTime)
        self.availableTime = availableTime
    }

    private static func determineTimeOfDay(from date: Date) -> TimeOfDay {
        let hour = Calendar.current.component(.hour, from: date)
        switch hour {
        case 5..<7: return .earlyMorning
        case 7..<12: return .morning
        case 12..<14: return .midday
        case 14..<18: return .afternoon
        case 18..<22: return .evening
        case 22..<24, 0..<2: return .night
        default: return .lateNight
        }
    }

    private static func determineDayOfWeek(from date: Date) -> DayOfWeek {
        let weekday = Calendar.current.component(.weekday, from: date)
        return DayOfWeek.allCases[weekday - 1]
    }

    private static func determineIsWorkDay(from date: Date) -> Bool {
        let weekday = Calendar.current.component(.weekday, from: date)
        return weekday >= 2 && weekday <= 6 // Monday to Friday
    }
}

@available(iOS 18.0, *)
enum DayOfWeek: String, CaseIterable, Codable {
    case sunday
    case monday
    case tuesday
    case wednesday
    case thursday
    case friday
    case saturday
}
