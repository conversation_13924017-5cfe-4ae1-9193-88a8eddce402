import Foundation

// MARK: - User Need Prediction Models

@available(iOS 18.0, *)
struct UserNeed: Identifiable, Codable {
    let id = UUID()
    let type: UserNeedType
    let description: String
    let urgency: NeedUrgency
    let confidence: Double
    let suggestedActions: [SuggestedAction]
    let timeframe: NeedTimeframe

    init(
        type: UserNeedType,
        description: String,
        urgency: NeedUrgency,
        confidence: Double = 0.8,
        suggestedActions: [SuggestedAction] = [],
        timeframe: NeedTimeframe = .immediate
    ) {
        self.type = type
        self.description = description
        self.urgency = urgency
        self.confidence = confidence
        self.suggestedActions = suggestedActions
        self.timeframe = timeframe
    }
}

@available(iOS 18.0, *)
enum UserNeedType: String, CaseIterable, Codable {
    case cognitiveBreak
    case taskSupport
    case environmentalAdjustment
    case motivationalSupport
    case skillDevelopment
    case adaptationTuning
}

@available(iOS 18.0, *)
enum NeedUrgency: String, CaseIterable, Codable {
    case low
    case medium
    case high
    case critical
}

@available(iOS 18.0, *)
enum NeedTimeframe: String, CaseIterable, Codable {
    case immediate
    case withinHour
    case today
    case thisWeek
    case longTerm
}

@available(iOS 18.0, *)
struct SuggestedAction: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let type: ActionType
    let estimatedDuration: TimeInterval
    let difficulty: ActionDifficulty

    init(
        title: String,
        description: String,
        type: ActionType,
        estimatedDuration: TimeInterval = 300,
        difficulty: ActionDifficulty = .easy
    ) {
        self.title = title
        self.description = description
        self.type = type
        self.estimatedDuration = estimatedDuration
        self.difficulty = difficulty
    }
}

@available(iOS 18.0, *)
enum ActionType: String, CaseIterable, Codable {
    case takeBreak
    case adjustSettings
    case startTask
    case seekSupport
    case changeEnvironment
    case practiceSkill
}

@available(iOS 18.0, *)
enum ActionDifficulty: String, CaseIterable, Codable {
    case easy
    case moderate
    case challenging
}
