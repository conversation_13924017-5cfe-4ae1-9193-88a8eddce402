import Foundation
import SwiftUI

// MARK: - Core Neurodiversity Types and Cognitive Support Enums

/// Types of neurodiversity for personalized support
public enum NeurodiversityType: String, CaseIterable, Codable, Sendable {
    case adhd = "ADHD"
    case autism = "Autism Spectrum"
    case executiveDysfunction = "Executive Dysfunction"
    case dyslexia = "Dyslexia"
    case dyspraxia = "Dyspraxia"
    case sensoryProcessingDifferences = "Sensory Processing Differences"
    case anxiety = "Anxiety"
    case depression = "Depression"
    case ocd = "OCD"
    case tourettes = "Tourette's"

    var displayName: String { rawValue }

    var supportStrategies: [CognitiveSupport] {
        switch self {
        case .adhd:
            return [.focusAssistance, .taskBreakdown, .timeManagement, .dopamineSupport]
        case .autism:
            return [.routineSupport, .sensoryAdaptation, .predictableInterfaces, .socialSupport]
        case .executiveDysfunction:
            return [.taskBreakdown, .memorySupport, .organizationTools, .initiationSupport]
        case .dyslexia:
            return [.readingSupport, .visualProcessing, .audioSupport]
        case .dyspraxia:
            return [.motorSupport, .sequencingSupport, .visualCues]
        case .sensoryProcessingDifferences:
            return [.sensoryAdaptation, .environmentalControl, .stimulationRegulation]
        case .anxiety:
            return [.calmingTechniques, .gradualExposure, .safetyFeatures]
        case .depression:
            return [.motivationSupport, .energyManagement, .positiveReinforcement]
        case .ocd:
            return [.flexibilitySupport, .uncertaintyTolerance, .compulsionManagement]
        case .tourettes:
            return [.ticManagement, .stressReduction, .focusSupport]
        }
    }
}

/// Cognitive strengths for strength-based approach
public enum CognitiveStrength: String, CaseIterable, Codable, Sendable {
    case hyperfocus
    case creativity
    case patternRecognition = "Pattern Recognition"
    case detailOriented = "Detail-Oriented"
    case systemicThinking = "Systemic Thinking"
    case problemSolving = "Problem Solving"
    case innovation
    case persistence
    case empathy
    case authenticity

    var description: String {
        switch self {
        case .hyperfocus:
            return "Ability to focus intensely on tasks of interest"
        case .creativity:
            return "Unique and innovative thinking patterns"
        case .patternRecognition:
            return "Exceptional ability to identify patterns and connections"
        case .detailOriented:
            return "Strong attention to detail and accuracy"
        case .systemicThinking:
            return "Understanding complex systems and relationships"
        case .problemSolving:
            return "Creative approaches to solving challenges"
        case .innovation:
            return "Thinking outside conventional boundaries"
        case .persistence:
            return "Determination and resilience in pursuing goals"
        case .empathy:
            return "Deep understanding and connection with others"
        case .authenticity:
            return "Genuine and honest self-expression"
        }
    }
}

/// Cognitive support strategies
public enum CognitiveSupport: String, CaseIterable, Codable, Sendable {
    case focusAssistance = "Focus Assistance"
    case taskBreakdown = "Task Breakdown"
    case timeManagement = "Time Management"
    case memorySupport = "Memory Support"
    case organizationTools = "Organization Tools"
    case initiationSupport = "Initiation Support"
    case routineSupport = "Routine Support"
    case sensoryAdaptation = "Sensory Adaptation"
    case predictableInterfaces = "Predictable Interfaces"
    case socialSupport = "Social Support"
    case readingSupport = "Reading Support"
    case visualProcessing = "Visual Processing"
    case audioSupport = "Audio Support"
    case motorSupport = "Motor Support"
    case sequencingSupport = "Sequencing Support"
    case visualCues = "Visual Cues"
    case environmentalControl = "Environmental Control"
    case stimulationRegulation = "Stimulation Regulation"
    case calmingTechniques = "Calming Techniques"
    case gradualExposure = "Gradual Exposure"
    case safetyFeatures = "Safety Features"
    case motivationSupport = "Motivation Support"
    case energyManagement = "Energy Management"
    case positiveReinforcement = "Positive Reinforcement"
    case flexibilitySupport = "Flexibility Support"
    case uncertaintyTolerance = "Uncertainty Tolerance"
    case compulsionManagement = "Compulsion Management"
    case ticManagement = "Tic Management"
    case dopamineSupport = "Dopamine Support"
    case stressReduction = "Stress Reduction"
    case focusSupport = "Focus Support"
}

/// Learning style preferences
public enum LearningStyle: String, CaseIterable, Codable, Sendable {
    case visual
    case auditory
    case kinesthetic
    case readingWriting = "Reading/Writing"
    case multimodal

    var description: String {
        switch self {
        case .visual: return "Learning through visual aids and imagery"
        case .auditory: return "Learning through listening and verbal instruction"
        case .kinesthetic: return "Learning through hands-on experience and movement"
        case .readingWriting: return "Learning through text-based materials"
        case .multimodal: return "Learning through multiple sensory channels"
        }
    }
}

/// Attention span categories
public enum AttentionSpan: String, CaseIterable, Codable, Sendable {
    case short
    case medium
    case long
    case variable

    var durationMinutes: ClosedRange<Int> {
        switch self {
        case .short: return 5...15
        case .medium: return 15...45
        case .long: return 45...120
        case .variable: return 5...120
        }
    }
}

/// Processing speed levels
public enum ProcessingSpeed: String, CaseIterable, Codable, Sendable {
    case slow
    case medium
    case fast
    case variable

    var multiplier: Double {
        switch self {
        case .slow: return 1.5
        case .medium: return 1.0
        case .fast: return 0.7
        case .variable: return 1.0
        }
    }
}

/// Working memory capacity
public enum WorkingMemoryCapacity: String, CaseIterable, Codable, Sendable {
    case low
    case medium
    case high

    var maxSimultaneousTasks: Int {
        switch self {
        case .low: return 2
        case .medium: return 4
        case .high: return 7
        }
    }

    var chunkSize: Int {
        switch self {
        case .low: return 3
        case .medium: return 5
        case .high: return 9
        }
    }
}

/// Executive function levels
public enum ExecutiveFunctionLevel: String, CaseIterable, Codable, Sendable {
    case low
    case medium
    case high

    var supportLevel: ExecutiveFunctionSupport {
        switch self {
        case .low: return .extensive
        case .medium: return .standard
        case .high: return .minimal
        }
    }
}

/// Executive function support levels
public enum ExecutiveFunctionSupport: String, CaseIterable, Codable, Sendable {
    case minimal
    case standard
    case extensive

    var features: [String] {
        switch self {
        case .minimal: return ["Basic reminders", "Simple organization"]
        case .standard: return ["Task breakdown", "Progress tracking", "Flexible scheduling"]
        case .extensive: return ["Detailed guidance", "Step-by-step instructions", "Multiple reminders",
                                 "Adaptive scheduling", "Cognitive load monitoring"]
        }
    }
}
