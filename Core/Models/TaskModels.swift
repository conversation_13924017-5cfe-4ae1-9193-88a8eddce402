import CloudKit
import Foundation
import SwiftUI

// MARK: - Required Type Definitions

@available(iOS 18.0, *)
public struct ExecutiveFunctionChallenge: Codable, Identifiable, Equatable, Sendable {
    public let id = UUID()
    public var domain: ExecutiveFunctionDomain
    public var description: String
    public var severity: ChallengeSeverity
    public var impact: String

    public init(domain: ExecutiveFunctionDomain, description: String, severity: ChallengeSeverity, impact: String) {
        self.domain = domain
        self.description = description
        self.severity = severity
        self.impact = impact
    }
}

@available(iOS 18.0, *)
public enum ExecutiveFunctionDomain: String, Codable, CaseIterable, Sendable {
    case workingMemory
    case inhibitoryControl
    case cognitiveFlexibility
    case planning
    case taskInitiation
    case timeManagement
    case persistence
    case metacognition
}

@available(iOS 18.0, *)
public enum ChallengeSeverity: String, Codable, CaseIterable, Sendable {
    case mild
    case moderate
    case severe
}

// MARK: - Task Models

/// AI-generated task with neurodiversity optimizations
@available(iOS 18.0, *)
public struct AITask: Identifiable, Codable, Sendable, Equatable {
    public let id: UUID
    public var title: String
    public var description: String
    public var priority: TaskPriority
    public var estimatedDuration: TimeInterval
    public var cognitiveLoad: CognitiveLoadLevel
    public var executiveFunctionSupport: ExecutiveFunctionSupport
    public var isCompleted: Bool
    public var progress: Double
    public var aiGeneratedSteps: [TaskStep]
    public var personalizedHints: [String]
    public var adaptiveReminders: [TaskReminder]
    public var breakdownLevel: TaskBreakdownLevel
    public var focusRequirement: FocusRequirement
    public var sensoryConsiderations: [SensoryConsideration]
    public var cloudKitRecordName: String?
    public var dueDate: Date?
    public var createdAt: Date

    init(title: String, description: String, priority: TaskPriority, estimatedDuration: TimeInterval, cognitiveLoad: CognitiveLoadLevel, id: UUID = UUID()) {
        self.id = id; self.title = title; self.description = description; self.priority = priority; self.estimatedDuration = estimatedDuration; self.cognitiveLoad = cognitiveLoad; self.executiveFunctionSupport = .standard; self.isCompleted = false; self.progress = 0.0; self.aiGeneratedSteps = []; self.personalizedHints = []; self.adaptiveReminders = []; self.breakdownLevel = .standard; self.focusRequirement = .medium; self.sensoryConsiderations = []; self.cloudKitRecordName = nil; self.dueDate = nil; self.createdAt = Date()
    }

    // MARK: - Equatable
    public static func == (lhs: AITask, rhs: AITask) -> Bool {
        lhs.id == rhs.id
    }
}

/// Task step for executive function support
public struct TaskStep: Identifiable, Codable, Sendable, Equatable {
    public let id: UUID
    public var title: String
    public var description: String
    public var isCompleted: Bool
    public var estimatedDuration: TimeInterval
    public var order: Int

    public init(title: String, description: String, estimatedDuration: TimeInterval, order: Int, id: UUID = UUID()) {
        self.id = id; self.title = title; self.description = description; self.isCompleted = false; self.estimatedDuration = estimatedDuration; self.order = order
    }

    public static func == (lhs: TaskStep, rhs: TaskStep) -> Bool {
        lhs.id == rhs.id
    }
}

/// Task reminder with neurodiversity considerations
@available(iOS 18.0, *)
public struct TaskReminder: Identifiable, Codable, Sendable, Equatable {
    public let id: UUID
    public var taskId: UUID
    public var reminderTime: Date
    public var reminderType: ReminderType
    public var message: String
    public var isGentle: Bool
    public var sensoryMode: SensoryMode

    public init(taskId: UUID, reminderTime: Date, reminderType: ReminderType, message: String, isGentle: Bool, sensoryMode: SensoryMode, id: UUID = UUID()) {
        self.id = id; self.taskId = taskId; self.reminderTime = reminderTime; self.reminderType = reminderType; self.message = message; self.isGentle = isGentle; self.sensoryMode = sensoryMode
    }

    public static func == (lhs: TaskReminder, rhs: TaskReminder) -> Bool {
        lhs.id == rhs.id
    }
}

/// Task completion tracking with neurodiversity insights
@available(iOS 18.0, *)
public struct TaskCompletion: Identifiable, Codable, Sendable {
    public let id: UUID
    public let taskId: UUID
    public let userId: UUID
    public let completedAt: Date
    public let actualDuration: TimeInterval
    public let estimatedDuration: TimeInterval
    public let cognitiveLoadExperienced: CognitiveLoadLevel
    public let difficultyRating: Int // 1-5 scale
    public let satisfactionRating: Int // 1-5 scale
    public let executiveFunctionChallenges: [ExecutiveFunctionChallenge]
    public let adaptationsUsed: [String]
    public let notes: String?
    public let contextualFactors: [String]
    public let completionMethod: CompletionMethod

    // Additional properties for cognitive analysis compatibility
    public var completionTime: TimeInterval { actualDuration }
    public var cognitiveLoadDuring: Double {
        switch cognitiveLoadExperienced {
        case .low: return 0.3
        case .medium: return 0.6
        case .high: return 0.9
        case .overload: return 1.0
        }
    }
    public let interruptions: Int

    public var durationAccuracy: Double {
        guard estimatedDuration > 0 else { return 0.0 }
        let ratio = actualDuration / estimatedDuration
        return 1.0 - abs(ratio - 1.0)
    }

    public var wasOverestimated: Bool {
        actualDuration < estimatedDuration
    }

    public var wasUnderestimated: Bool {
        actualDuration > estimatedDuration
    }

    public init(
        taskId: UUID,
        userId: UUID,
        actualDuration: TimeInterval,
        estimatedDuration: TimeInterval,
        cognitiveLoadExperienced: CognitiveLoadLevel,
        difficultyRating: Int,
        satisfactionRating: Int,
        completionMethod: CompletionMethod = .manual,
        interruptions: Int = 0,
        id: UUID = UUID(),
        completedAt: Date = Date(),
        executiveFunctionChallenges: [ExecutiveFunctionChallenge] = [],
        adaptationsUsed: [String] = [],
        notes: String? = nil,
        contextualFactors: [String] = []
    ) {
        self.id = id
        self.taskId = taskId
        self.userId = userId
        self.completedAt = completedAt
        self.actualDuration = actualDuration
        self.estimatedDuration = estimatedDuration
        self.cognitiveLoadExperienced = cognitiveLoadExperienced
        self.difficultyRating = difficultyRating
        self.satisfactionRating = satisfactionRating
        self.executiveFunctionChallenges = executiveFunctionChallenges
        self.adaptationsUsed = adaptationsUsed
        self.notes = notes
        self.contextualFactors = contextualFactors
        self.completionMethod = completionMethod
        self.interruptions = interruptions
    }
}

@available(iOS 18.0, *)
struct TaskTiming: Codable {
    let suggestedStartTime: Date
    let estimatedDuration: TimeInterval
    let optimalTimeSlots: [String]
    let reasoning: String
    let confidence: Double
}

// Note: Routine, RoutineSchedule, and RoutineFrequency moved to Core/Repositories/RoutineRepository.swift to avoid conflicts

// TaskRepositoryProtocol is defined in Core/Repositories/TaskRepository.swift
