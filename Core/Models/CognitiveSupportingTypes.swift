import Foundation
import SwiftUI

// MARK: - Supporting Enums and Types

@available(iOS 18.0, *)
enum FocusEnhancementTechnique: String, Codable, CaseIterable {
    case timeBlocking
    case pomodoroTechnique
    case deepWork
    case mindfulness
    case environmentalControl
    case musicTherapy
    case visualCues
    case breathingExercises
}

@available(iOS 18.0, *)
enum CognitiveSupport: String, Codable, CaseIterable {
    case visualCues
    case auditoryReminders
    case taskBreakdown
    case progressTracking
    case externalMemoryAids
    case structuredEnvironment
    case socialSupport
    case technologyAssistance
}

@available(iOS 18.0, *)
enum AdaptationSpeed: String, Codable, CaseIterable {
    case slow
    case moderate
    case fast
    case immediate
}

@available(iOS 18.0, *)
struct FeedbackPreferences: Codable, Equatable {
    var frequency: FeedbackFrequency
    var type: FeedbackType
    var delivery: FeedbackDelivery
    var sensitivity: FeedbackSensitivity

    init(
        frequency: FeedbackFrequency = .moderate,
        type: FeedbackType = .constructive,
        delivery: FeedbackDelivery = .visual,
        sensitivity: FeedbackSensitivity = .medium
    ) {
        self.frequency = frequency
        self.type = type
        self.delivery = delivery
        self.sensitivity = sensitivity
    }
}

@available(iOS 18.0, *)
enum FeedbackFrequency: String, Codable, CaseIterable {
    case minimal
    case moderate
    case frequent
    case continuous
}

@available(iOS 18.0, *)
enum FeedbackType: String, Codable, CaseIterable {
    case constructive
    case encouraging
    case analytical
    case minimal
}

@available(iOS 18.0, *)
enum FeedbackDelivery: String, Codable, CaseIterable {
    case visual
    case auditory
    case haptic
    case multimodal
}

@available(iOS 18.0, *)
enum FeedbackSensitivity: String, Codable, CaseIterable {
    case low
    case medium
    case high
}

@available(iOS 18.0, *)
enum BreakType: String, Codable, CaseIterable {
    case movement
    case breathing
    case mindfulness
    case social
    case creative
    case nature
    case rest
}
