import Foundation

@available(iOS 18.0, *)
struct User: Codable, Identifiable {
    let id: UUID
    let email: String
    let name: String
    let neurodiversityProfile: NeurodiversityProfile
    let preferences: UserPreferences
    let createdAt: Date
    let updatedAt: Date

    init(email: String, name: String) {
        self.id = UUID()
        self.email = email
        self.name = name
        self.neurodiversityProfile = NeurodiversityProfile()
        self.preferences = UserPreferences()
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

@available(iOS 18.0, *)
struct NeurodiversityProfile: Codable {
    var hasADHD: Bool = false
    var hasAutism: Bool = false
    var hasDyslexia: Bool = false
    var cognitiveLoadPreference: CognitiveLoadLevel = .medium
    var sensoryPreferences = SensoryPreferences.default
}

enum ColorScheme: String, Codable, CaseIterable {
    case light, dark, system
}

@available(iOS 18.0, *)
struct UserPreferences: Codable {
    var notificationsEnabled: Bool = true
    var healthKitEnabled: Bool = false
    var aiCoachingEnabled: Bool = true
    var language: String = "en"
}
