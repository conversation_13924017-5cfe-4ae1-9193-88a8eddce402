import Foundation
import SwiftUI

// MARK: - Wellness and Health Enums

/// Stress and mood levels
public enum StressLevel: String, CaseIterable, Codable, Sendable {
    case veryLow = "Very Low"
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case veryHigh = "Very High"

    var color: Color {
        switch self {
        case .veryLow: return .green
        case .low: return .mint
        case .medium: return .yellow
        case .high: return .orange
        case .veryHigh: return .red
        }
    }
}

public enum MoodLevel: String, CaseIterable, Codable, Sendable {
    case veryLow = "Very Low"
    case low
    case neutral
    case good
    case excellent

    var emoji: String {
        switch self {
        case .veryLow: return "😢"
        case .low: return "😕"
        case .neutral: return "😐"
        case .good: return "🙂"
        case .excellent: return "😄"
        }
    }
}

public enum EnergyLevel: String, CaseIterable, Codable, Sendable {
    case veryLow = "Very Low"
    case low
    case medium
    case high
    case veryHigh = "Very High"
}

/// Data sources
enum HeartRateSource: String, CaseIterable, Codable {
    case appleWatch = "Apple Watch"
    case healthKit = "HealthKit"
    case manual = "Manual Entry"
}
