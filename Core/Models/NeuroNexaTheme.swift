import SwiftUI

// MARK: - NeuroNexa Theme System

/// Theme system designed for neurodiversity-first accessibility
@available(iOS 18.0, *)
public enum NeuroNexaTheme: String, CaseIterable, Codable, Sendable {
    case adaptive = "adaptive"
    case light = "light"
    case dark = "dark"
    case highContrast = "high_contrast"
    case lowStimulation = "low_stimulation"
    case warmTones = "warm_tones"
    case coolTones = "cool_tones"

    // MARK: - Theme Properties

    var displayName: String {
        switch self {
        case .adaptive:
            return "Adaptive"
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        case .highContrast:
            return "High Contrast"
        case .lowStimulation:
            return "Low Stimulation"
        case .warmTones:
            return "Warm Tones"
        case .coolTones:
            return "Cool Tones"
        }
    }

    var description: String {
        switch self {
        case .adaptive:
            return "Automatically adjusts to system settings and time of day"
        case .light:
            return "Clean, bright interface with high readability"
        case .dark:
            return "Reduced eye strain with dark backgrounds"
        case .highContrast:
            return "Maximum contrast for visual accessibility"
        case .lowStimulation:
            return "Minimal visual stimulation with muted colors"
        case .warmTones:
            return "Warm, calming colors to reduce anxiety"
        case .coolTones:
            return "Cool, focusing colors to enhance concentration"
        }
    }

    // MARK: - Color Schemes

    var primaryColor: Color {
        switch self {
        case .adaptive, .light:
            return Color(red: 0.2, green: 0.6, blue: 0.9)
        case .dark:
            return Color(red: 0.3, green: 0.7, blue: 1.0)
        case .highContrast:
            return Color.black
        case .lowStimulation:
            return Color(red: 0.5, green: 0.5, blue: 0.6)
        case .warmTones:
            return Color(red: 0.8, green: 0.5, blue: 0.3)
        case .coolTones:
            return Color(red: 0.3, green: 0.6, blue: 0.8)
        }
    }

    var secondaryColor: Color {
        switch self {
        case .adaptive, .light:
            return Color(red: 0.9, green: 0.9, blue: 0.9)
        case .dark:
            return Color(red: 0.2, green: 0.2, blue: 0.2)
        case .highContrast:
            return Color.white
        case .lowStimulation:
            return Color(red: 0.9, green: 0.9, blue: 0.9)
        case .warmTones:
            return Color(red: 0.95, green: 0.9, blue: 0.85)
        case .coolTones:
            return Color(red: 0.85, green: 0.9, blue: 0.95)
        }
    }

    var backgroundColor: Color {
        switch self {
        case .adaptive, .light:
            return Color.white
        case .dark:
            return Color.black
        case .highContrast:
            return Color.white
        case .lowStimulation:
            return Color(red: 0.98, green: 0.98, blue: 0.98)
        case .warmTones:
            return Color(red: 0.98, green: 0.96, blue: 0.94)
        case .coolTones:
            return Color(red: 0.94, green: 0.96, blue: 0.98)
        }
    }

    var textColor: Color {
        switch self {
        case .adaptive, .light:
            return Color.black
        case .dark:
            return Color.white
        case .highContrast:
            return Color.black
        case .lowStimulation:
            return Color(red: 0.3, green: 0.3, blue: 0.3)
        case .warmTones:
            return Color(red: 0.2, green: 0.1, blue: 0.0)
        case .coolTones:
            return Color(red: 0.0, green: 0.1, blue: 0.2)
        }
    }

    var accentColor: Color {
        switch self {
        case .adaptive, .light:
            return Color(red: 0.0, green: 0.8, blue: 0.4)
        case .dark:
            return Color(red: 0.2, green: 0.9, blue: 0.5)
        case .highContrast:
            return Color.black
        case .lowStimulation:
            return Color(red: 0.6, green: 0.7, blue: 0.6)
        case .warmTones:
            return Color(red: 0.9, green: 0.6, blue: 0.2)
        case .coolTones:
            return Color(red: 0.2, green: 0.7, blue: 0.9)
        }
    }

    // MARK: - Cognitive Load Adaptations

    var cognitiveLoadColors: CognitiveLoadColors {
        switch self {
        case .adaptive, .light:
            return CognitiveLoadColors(
                low: Color.green.opacity(0.3),
                medium: Color.yellow.opacity(0.3),
                high: Color.red.opacity(0.3)
            )
        case .dark:
            return CognitiveLoadColors(
                low: Color.green.opacity(0.5),
                medium: Color.yellow.opacity(0.5),
                high: Color.red.opacity(0.5)
            )
        case .highContrast:
            return CognitiveLoadColors(
                low: Color.black,
                medium: Color.gray,
                high: Color.black
            )
        case .lowStimulation:
            return CognitiveLoadColors(
                low: Color.gray.opacity(0.2),
                medium: Color.gray.opacity(0.3),
                high: Color.gray.opacity(0.4)
            )
        case .warmTones:
            return CognitiveLoadColors(
                low: Color.orange.opacity(0.2),
                medium: Color.orange.opacity(0.3),
                high: Color.red.opacity(0.3)
            )
        case .coolTones:
            return CognitiveLoadColors(
                low: Color.blue.opacity(0.2),
                medium: Color.blue.opacity(0.3),
                high: Color.purple.opacity(0.3)
            )
        }
    }

    // MARK: - Typography

    var fontWeight: Font.Weight {
        switch self {
        case .adaptive, .light, .dark:
            return .regular
        case .highContrast:
            return .bold
        case .lowStimulation:
            return .light
        case .warmTones, .coolTones:
            return .medium
        }
    }

    var cornerRadius: CGFloat {
        switch self {
        case .adaptive, .light, .dark:
            return 12
        case .highContrast:
            return 0
        case .lowStimulation:
            return 8
        case .warmTones, .coolTones:
            return 16
        }
    }

    // MARK: - Animation Settings

    var animationDuration: Double {
        switch self {
        case .adaptive, .light, .dark:
            return 0.3
        case .highContrast:
            return 0.0 // No animations for high contrast
        case .lowStimulation:
            return 0.5 // Slower animations
        case .warmTones, .coolTones:
            return 0.25
        }
    }

    var reducedMotion: Bool {
        switch self {
        case .highContrast, .lowStimulation:
            return true
        default:
            return false
        }
    }
}

// MARK: - Supporting Types

struct CognitiveLoadColors {
    let low: Color
    let medium: Color
    let high: Color
}

// MARK: - Theme Extensions

@available(iOS 18.0, *)
extension NeuroNexaTheme {
    /// Get color for cognitive load level
    func colorForCognitiveLoad(_ level: CognitiveLoadLevel) -> Color {
        switch level {
        case .low:
            return cognitiveLoadColors.low
        case .medium:
            return cognitiveLoadColors.medium
        case .high:
            return cognitiveLoadColors.high
        case .overload:
            return cognitiveLoadColors.high // Use high color for overload state
        }
    }

    /// Check if theme is suitable for current time
    var isTimeAppropriate: Bool {
        let hour = Calendar.current.component(.hour, from: Date())

        switch self {
        case .adaptive:
            return true
        case .light:
            return hour >= 6 && hour < 18
        case .dark:
            return hour < 6 || hour >= 18
        case .highContrast, .lowStimulation, .warmTones, .coolTones:
            return true
        }
    }
}

// MARK: - Cognitive Load Level
// Note: CognitiveLoadLevel is defined in Core/Models/TaskEnums.swift
