import Foundation

// MARK: - User Preferences Models

@available(iOS 18.0, *)
struct UserPreferences: Codable, Equatable {
    let id: UUID
    let userId: UUID

    // Notification Preferences
    var notificationSettings: NotificationSettings

    // App Behavior Preferences
    var appBehavior: AppBehaviorSettings

    // Privacy Preferences
    var privacy: PrivacySettings

    // Accessibility Preferences
    var accessibility: AccessibilitySettings

    // Theme and Display
    var display: DisplaySettings

    // Data and Sync
    var dataSync: DataSyncSettings

    // Timestamps
    let createdAt: Date
    var updatedAt: Date

    init(
        userId: UUID,
        notificationSettings: NotificationSettings = NotificationSettings(),
        appBehavior: AppBehaviorSettings = AppBehaviorSettings(),
        privacy: PrivacySettings = PrivacySettings(),
        accessibility: AccessibilitySettings = AccessibilitySettings(),
        display: DisplaySettings = DisplaySettings(),
        dataSync: DataSyncSettings = DataSyncSettings(),
        id: UUID = UUID(),
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.userId = userId
        self.notificationSettings = notificationSettings
        self.appBehavior = appBehavior
        self.privacy = privacy
        self.accessibility = accessibility
        self.display = display
        self.dataSync = dataSync
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - Notification Settings

@available(iOS 18.0, *)
struct NotificationSettings: Codable, Equatable {
    var taskReminders: Bool
    var breathingReminders: Bool
    var breakReminders: Bool
    var achievementNotifications: Bool
    var dailySummary: Bool
    var weeklyReports: Bool

    // Quiet Hours
    var quietHoursEnabled: Bool
    var quietHoursStart: Date
    var quietHoursEnd: Date

    // Notification Timing
    var reminderFrequency: ReminderFrequency
    var snoozeEnabled: Bool
    var snoozeDuration: TimeInterval

    init(
        taskReminders: Bool = true,
        breathingReminders: Bool = true,
        breakReminders: Bool = true,
        achievementNotifications: Bool = true,
        dailySummary: Bool = false,
        weeklyReports: Bool = false,
        quietHoursEnabled: Bool = false,
        quietHoursStart: Date = Calendar.current.date(bySettingHour: 22, minute: 0, second: 0, of: Date()) ?? Date(),
        quietHoursEnd: Date = Calendar.current.date(bySettingHour: 8, minute: 0, second: 0, of: Date()) ?? Date(),
        reminderFrequency: ReminderFrequency = .moderate,
        snoozeEnabled: Bool = true,
        snoozeDuration: TimeInterval = 300 // 5 minutes
    ) {
        self.taskReminders = taskReminders
        self.breathingReminders = breathingReminders
        self.breakReminders = breakReminders
        self.achievementNotifications = achievementNotifications
        self.dailySummary = dailySummary
        self.weeklyReports = weeklyReports
        self.quietHoursEnabled = quietHoursEnabled
        self.quietHoursStart = quietHoursStart
        self.quietHoursEnd = quietHoursEnd
        self.reminderFrequency = reminderFrequency
        self.snoozeEnabled = snoozeEnabled
        self.snoozeDuration = snoozeDuration
    }
}

@available(iOS 18.0, *)
enum ReminderFrequency: String, CaseIterable, Codable {
    case minimal
    case light
    case moderate
    case frequent
    case intensive

    var displayName: String {
        switch self {
        case .minimal: return "Minimal"
        case .light: return "Light"
        case .moderate: return "Moderate"
        case .frequent: return "Frequent"
        case .intensive: return "Intensive"
        }
    }

    var intervalMinutes: Int {
        switch self {
        case .minimal: return 240 // 4 hours
        case .light: return 120 // 2 hours
        case .moderate: return 60 // 1 hour
        case .frequent: return 30 // 30 minutes
        case .intensive: return 15 // 15 minutes
        }
    }
}

// MARK: - App Behavior Settings

@available(iOS 18.0, *)
struct AppBehaviorSettings: Codable, Equatable {
    var autoStartBreathingExercises: Bool
    var autoSaveProgress: Bool
    var backgroundAppRefresh: Bool
    var hapticFeedback: Bool
    var soundEffects: Bool
    var voiceGuidance: Bool

    // Session Management
    var autoEndSessions: Bool
    var sessionTimeoutMinutes: Int
    var pauseOnInterruption: Bool

    // AI and Personalization
    var enableAICoaching: Bool
    var personalizedRecommendations: Bool
    var adaptiveInterface: Bool
    
    // Claude: Adding preferredDifficulty property for breathing service compatibility
    var preferredDifficulty: ExerciseDifficulty?

    init(
        autoStartBreathingExercises: Bool = false,
        autoSaveProgress: Bool = true,
        backgroundAppRefresh: Bool = true,
        hapticFeedback: Bool = true,
        soundEffects: Bool = true,
        voiceGuidance: Bool = false,
        autoEndSessions: Bool = false,
        sessionTimeoutMinutes: Int = 30,
        pauseOnInterruption: Bool = true,
        enableAICoaching: Bool = true,
        personalizedRecommendations: Bool = true,
        adaptiveInterface: Bool = true,
        preferredDifficulty: ExerciseDifficulty? = nil // Claude: Adding preferredDifficulty parameter
    ) {
        self.autoStartBreathingExercises = autoStartBreathingExercises
        self.autoSaveProgress = autoSaveProgress
        self.backgroundAppRefresh = backgroundAppRefresh
        self.hapticFeedback = hapticFeedback
        self.soundEffects = soundEffects
        self.voiceGuidance = voiceGuidance
        self.autoEndSessions = autoEndSessions
        self.sessionTimeoutMinutes = sessionTimeoutMinutes
        self.pauseOnInterruption = pauseOnInterruption
        self.enableAICoaching = enableAICoaching
        self.personalizedRecommendations = personalizedRecommendations
        self.adaptiveInterface = adaptiveInterface
        self.preferredDifficulty = preferredDifficulty // Claude: Initialize preferredDifficulty property
    }
}

// MARK: - Display Settings

@available(iOS 18.0, *)
struct DisplaySettings: Codable, Equatable {
    var theme: AppTheme
    var fontSize: FontSize
    var boldText: Bool
    var highContrast: Bool
    var reduceMotion: Bool
    var colorScheme: ColorScheme

    // Layout Preferences
    var compactLayout: Bool
    var showProgressBars: Bool
    var showStatistics: Bool

    init(
        theme: AppTheme = .system,
        fontSize: FontSize = .medium,
        boldText: Bool = false,
        highContrast: Bool = false,
        reduceMotion: Bool = false,
        colorScheme: ColorScheme = .adaptive,
        compactLayout: Bool = false,
        showProgressBars: Bool = true,
        showStatistics: Bool = true
    ) {
        self.theme = theme
        self.fontSize = fontSize
        self.boldText = boldText
        self.highContrast = highContrast
        self.reduceMotion = reduceMotion
        self.colorScheme = colorScheme
        self.compactLayout = compactLayout
        self.showProgressBars = showProgressBars
        self.showStatistics = showStatistics
    }
}

@available(iOS 18.0, *)
enum AppTheme: String, CaseIterable, Codable {
    case light
    case dark
    case system

    var displayName: String {
        switch self {
        case .light: return "Light"
        case .dark: return "Dark"
        case .system: return "System"
        }
    }
}

@available(iOS 18.0, *)
enum FontSize: String, CaseIterable, Codable {
    case small
    case medium
    case large
    case extraLarge

    var displayName: String {
        switch self {
        case .small: return "Small"
        case .medium: return "Medium"
        case .large: return "Large"
        case .extraLarge: return "Extra Large"
        }
    }

    var scaleFactor: CGFloat {
        switch self {
        case .small: return 0.85
        case .medium: return 1.0
        case .large: return 1.15
        case .extraLarge: return 1.3
        }
    }
}

@available(iOS 18.0, *)
enum ColorScheme: String, CaseIterable, Codable {
    case adaptive
    case calm
    case vibrant
    case monochrome

    var displayName: String {
        switch self {
        case .adaptive: return "Adaptive"
        case .calm: return "Calm"
        case .vibrant: return "Vibrant"
        case .monochrome: return "Monochrome"
        }
    }
}

// MARK: - Data Sync Settings

@available(iOS 18.0, *)
struct DataSyncSettings: Codable, Equatable {
    var cloudSyncEnabled: Bool
    var autoSync: Bool
    var syncFrequency: SyncFrequency
    var wifiOnlySync: Bool
    var backupEnabled: Bool
    var exportDataEnabled: Bool

    init(
        cloudSyncEnabled: Bool = true,
        autoSync: Bool = true,
        syncFrequency: SyncFrequency = .daily,
        wifiOnlySync: Bool = true,
        backupEnabled: Bool = true,
        exportDataEnabled: Bool = true
    ) {
        self.cloudSyncEnabled = cloudSyncEnabled
        self.autoSync = autoSync
        self.syncFrequency = syncFrequency
        self.wifiOnlySync = wifiOnlySync
        self.backupEnabled = backupEnabled
        self.exportDataEnabled = exportDataEnabled
    }
}

@available(iOS 18.0, *)
enum SyncFrequency: String, CaseIterable, Codable {
    case realtime
    case hourly
    case daily
    case weekly
    case manual

    var displayName: String {
        switch self {
        case .realtime: return "Real-time"
        case .hourly: return "Hourly"
        case .daily: return "Daily"
        case .weekly: return "Weekly"
        case .manual: return "Manual"
        }
    }
}
