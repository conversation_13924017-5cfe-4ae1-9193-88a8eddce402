import Foundation

// MARK: - Sensory Adaptation Models

@available(iOS 18.0, *)
struct MotionAdaptation: Codable {
    let level: MotionSensitivity
    let modifications: [MotionModification]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
enum MotionSensitivity: String, CaseIterable, Codable {
    case none
    case low
    case moderate
    case high
    case extreme

    var displayName: String {
        switch self {
        case .none: return "No Sensitivity"
        case .low: return "Low Sensitivity"
        case .moderate: return "Moderate Sensitivity"
        case .high: return "High Sensitivity"
        case .extreme: return "Extreme Sensitivity"
        }
    }
}

@available(iOS 18.0, *)
struct MotionModification: Codable {
    let type: MotionModificationType
    let value: Double
    let description: String
}

@available(iOS 18.0, *)
enum MotionModificationType: String, CaseIterable, Codable {
    case reduceAnimationSpeed
    case disableParallax
    case minimizeTransitions
    case staticElements
    case reduceAutoplay
}

// MARK: - Color Adaptation Models

@available(iOS 18.0, *)
struct ColorAdaptation: Codable {
    let level: ColorContrast
    let modifications: [ColorModification]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
enum ColorContrast: String, CaseIterable, Codable {
    case standard
    case enhanced
    case high
    case maximum

    var displayName: String {
        switch self {
        case .standard: return "Standard Contrast"
        case .enhanced: return "Enhanced Contrast"
        case .high: return "High Contrast"
        case .maximum: return "Maximum Contrast"
        }
    }

    var contrastRatio: Double {
        switch self {
        case .standard: return 4.5
        case .enhanced: return 7.0
        case .high: return 10.0
        case .maximum: return 15.0
        }
    }
}

@available(iOS 18.0, *)
struct ColorModification: Codable {
    let type: ColorModificationType
    let value: String
    let description: String
}

@available(iOS 18.0, *)
enum ColorModificationType: String, CaseIterable, Codable {
    case increaseContrast
    case adjustBrightness
    case modifyColorPalette
    case enhanceBorders
    case simplifyColors
}

// MARK: - Sound Adaptation Models

@available(iOS 18.0, *)
struct SoundAdaptation: Codable {
    let level: SoundSensitivity
    let modifications: [SoundModification]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
enum SoundSensitivity: String, CaseIterable, Codable {
    case none
    case low
    case moderate
    case high
    case extreme

    var displayName: String {
        switch self {
        case .none: return "No Sensitivity"
        case .low: return "Low Sensitivity"
        case .moderate: return "Moderate Sensitivity"
        case .high: return "High Sensitivity"
        case .extreme: return "Extreme Sensitivity"
        }
    }
}

@available(iOS 18.0, *)
struct SoundModification: Codable {
    let type: SoundModificationType
    let value: Double
    let description: String
}

@available(iOS 18.0, *)
enum SoundModificationType: String, CaseIterable, Codable {
    case reduceVolume
    case disableNotificationSounds
    case enableVisualAlerts
    case customSoundProfile
    case silentMode
}
