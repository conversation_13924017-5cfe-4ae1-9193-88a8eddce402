import Foundation
import SwiftUI

// MARK: - Placeholder Views

@available(iOS 18.0, *)
struct AuthenticationView: View {
    var body: some View {
        VStack {
            Text("NeuroNexa")
                .font(.largeTitle)
                .fontWeight(.bold)

            Text("Authentication View")
                .font(.title2)
                .foregroundColor(.secondary)

            But<PERSON>("Sign In") {
                // Authentication logic
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct OnboardingView: View {
    var body: some View {
        VStack {
            Text("Welcome to NeuroNexa")
                .font(.largeTitle)
                .fontWeight(.bold)

            Text("Your neurodiversity-first productivity companion")
                .font(.title3)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            VStack(spacing: 20) {
                FeatureRow(icon: "brain.head.profile", title: "Cognitive Support", description: "Adaptive interfaces for different cognitive needs")
                FeatureRow(icon: "heart.fill", title: "Sensory Adaptation", description: "Customizable sensory experiences")
                FeatureRow(icon: "list.bullet.clipboard", title: "Executive Function", description: "Task breakdown and organization tools")
            }
            .padding()

            <PERSON><PERSON>("Get Started") {
                // Onboarding completion logic
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 30)
                .accessibilityLabel("\(title) feature icon")

            VStack(alignment: .leading) {
                Text(title)
                    .font(.headline)
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

// Note: AITaskCoachView moved to UI/Views/AITaskCoachView.swift

@available(iOS 18.0, *)
struct RoutineBuilderView: View {
    let viewModel: RoutineBuilderViewModel

    var body: some View {
        NavigationView {
            VStack {
                Text("Routine Builder")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("Create personalized routines")
                    .font(.title3)
                    .foregroundColor(.secondary)

                // Placeholder content
                VStack(spacing: 16) {
                    RoutineCard(title: "Morning Routine", tasks: 5, duration: "45 min")
                    RoutineCard(title: "Work Focus", tasks: 3, duration: "2 hours")
                    RoutineCard(title: "Evening Wind-down", tasks: 4, duration: "30 min")
                }
                .padding()

                Button("Create New Routine") {
                    // Routine creation logic
                }
                .buttonStyle(.borderedProminent)

                Spacer()
            }
            .navigationTitle("Routines")
        }
    }
}

@available(iOS 18.0, *)
struct RoutineCard: View {
    let title: String
    let tasks: Int
    let duration: String

    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.headline)
            HStack {
                Text("\(tasks) tasks")
                Spacer()
                Text(duration)
            }
            .font(.caption)
            .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - View Models

// Note: AITaskCoachViewModel moved to dedicated ViewModel files to avoid conflicts

@available(iOS 18.0, *)
class RoutineBuilderViewModel: ObservableObject {
    @Published var routines: [Routine] = []
    @Published var currentRoutine: Routine?

    init() {}

    func loadRoutines() {
        // Load routines logic
    }

    func createRoutine() {
        // Create routine logic
    }

    func deleteRoutine(_ routine: Routine) {
        // Delete routine logic
    }
}
