import Foundation

// MARK: - Sensory Cognitive Patterns and Optimizations

@available(iOS 18.0, *)
struct SensoryCognitivePatterns: Codable {
    let patterns: [SensoryCognitivePattern]
    let trends: [SensoryCognitiveTrend]
    let correlations: [SensoryCognitiveCorrelation]
    let analysisDate: Date

    init(
        patterns: [SensoryCognitivePattern] = [],
        trends: [SensoryCognitiveTrend] = [],
        correlations: [SensoryCognitiveCorrelation] = [],
        analysisDate: Date = Date()
    ) {
        self.patterns = patterns
        self.trends = trends
        self.correlations = correlations
        self.analysisDate = analysisDate
    }
}

@available(iOS 18.0, *)
struct SensoryCognitiveTrend: Identifiable, Codable {
    let id = UUID()
    let metric: SensoryCognitiveMetricType
    let direction: SensoryTrendDirection
    let magnitude: Double
    let timeframe: SensoryTrendTimeframe

    init(metric: SensoryCognitiveMetricType, direction: SensoryTrendDirection, magnitude: Double, timeframe: SensoryTrendTimeframe) {
        self.metric = metric
        self.direction = direction
        self.magnitude = magnitude
        self.timeframe = timeframe
    }
}

@available(iOS 18.0, *)
enum SensoryCognitiveMetricType: String, CaseIterable, Codable {
    case attention
    case workingMemory
    case processingSpeed
    case executiveFunction
    case overallLoad
}

@available(iOS 18.0, *)
enum SensoryTrendDirection: String, CaseIterable, Codable {
    case improving
    case stable
    case declining
}

@available(iOS 18.0, *)
enum SensoryTrendTimeframe: String, CaseIterable, Codable {
    case daily
    case weekly
    case monthly
}

@available(iOS 18.0, *)
struct SensoryCognitiveCorrelation: Identifiable, Codable {
    let id = UUID()
    let factor1: SensoryCorrelationFactor
    let factor2: SensoryCorrelationFactor
    let strength: Double
    let direction: SensoryCorrelationDirection

    init(factor1: SensoryCorrelationFactor, factor2: SensoryCorrelationFactor, strength: Double, direction: SensoryCorrelationDirection) {
        self.factor1 = factor1
        self.factor2 = factor2
        self.strength = strength
        self.direction = direction
    }
}

@available(iOS 18.0, *)
enum SensoryCorrelationFactor: String, CaseIterable, Codable {
    case timeOfDay
    case taskType
    case environmentalNoise
    case cognitiveLoad
    case breakFrequency
}

@available(iOS 18.0, *)
enum SensoryCorrelationDirection: String, CaseIterable, Codable {
    case positive
    case negative
    case neutral
}

@available(iOS 18.0, *)
struct SensoryCognitiveOptimization: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let category: SensoryOptimizationCategory
    let expectedImprovement: Double
    let implementationSteps: [SensoryOptimizationStep]

    init(
        title: String,
        description: String,
        category: SensoryOptimizationCategory,
        expectedImprovement: Double = 0.2,
        implementationSteps: [SensoryOptimizationStep] = []
    ) {
        self.title = title
        self.description = description
        self.category = category
        self.expectedImprovement = expectedImprovement
        self.implementationSteps = implementationSteps
    }
}

@available(iOS 18.0, *)
enum SensoryOptimizationCategory: String, CaseIterable, Codable {
    case scheduling
    case environment
    case taskStructure
    case breakStrategy
    case cognitiveSupport
}

@available(iOS 18.0, *)
struct SensoryOptimizationStep: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let order: Int
    let estimatedDuration: TimeInterval

    init(title: String, description: String, order: Int, estimatedDuration: TimeInterval = 300) {
        self.title = title
        self.description = description
        self.order = order
        self.estimatedDuration = estimatedDuration
    }
}
