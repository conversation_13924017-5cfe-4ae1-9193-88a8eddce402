import Foundation

// MARK: - OpenAI Integration Types

@available(iOS 18.0, *)
struct OpenAITaskCoach: Codable {
    let id: String
    let name: String
    let specialization: CoachSpecialization
    let personality: CoachPersonality
    let capabilities: [CoachCapability]
    let neurodiversityExpertise: [NeurodiversityType]

    static let `default` = OpenAITaskCoach(
        id: "neuronexa-coach-v1",
        name: "NeuroNexa AI Coach",
        specialization: .neurodiversity,
        personality: .supportive,
        capabilities: [.taskBreakdown, .executiveSupport, .anxietyManagement, .cognitiveAdaptation],
        neurodiversityExpertise: [.adhd, .autism, .anxiety, .executiveFunction]
    )
}

@available(iOS 18.0, *)
enum CoachSpecialization: String, CaseIterable, Codable {
    case neurodiversity
    case productivity
    case wellness
    case learning
}

@available(iOS 18.0, *)
enum CoachPersonality: String, CaseIterable, Codable {
    case supportive
    case encouraging
    case analytical
    case gentle
    case energetic
}

@available(iOS 18.0, *)
enum CoachCapability: String, CaseIterable, Codable {
    case taskBreakdown
    case executiveSupport
    case anxietyManagement
    case cognitiveAdaptation
    case timeManagement
    case motivationalSupport
}

@available(iOS 18.0, *)
struct UserContext: Codable {
    let userId: UUID
    let currentState: UserState
    let cognitiveLoad: CognitiveLoadLevel
    let recentTasks: [AITask]
    let preferences: UserPreferences
    let environmentalContext: EnvironmentalContext
    let timeContext: TimeContext
    let socialContext: SocialContext
    let healthContext: HealthContext
    let deviceContext: DeviceContext

    init(
        userId: UUID,
        preferences: UserPreferences,
        currentState: UserState = .active,
        cognitiveLoad: CognitiveLoadLevel = .moderate,
        recentTasks: [AITask] = [],
        environmentalContext: EnvironmentalContext = EnvironmentalContext(),
        timeContext: TimeContext = TimeContext(),
        socialContext: SocialContext = SocialContext(),
        healthContext: HealthContext = HealthContext(),
        deviceContext: DeviceContext = DeviceContext()
    ) {
        self.userId = userId
        self.currentState = currentState
        self.cognitiveLoad = cognitiveLoad
        self.recentTasks = recentTasks
        self.preferences = preferences
        self.environmentalContext = environmentalContext
        self.timeContext = timeContext
        self.socialContext = socialContext
        self.healthContext = healthContext
        self.deviceContext = deviceContext
    }
}

@available(iOS 18.0, *)
enum UserState: String, CaseIterable, Codable {
    case active
    case focused
    case overwhelmed
    case resting
    case transitioning
}

@available(iOS 18.0, *)
struct EnvironmentalContext: Codable {
    let location: LocationContext
    let noiseLevel: NoiseLevel
    let lightingConditions: LightingConditions
    let crowdLevel: CrowdLevel

    init(
        location: LocationContext = .home,
        noiseLevel: NoiseLevel = .moderate,
        lightingConditions: LightingConditions = .natural,
        crowdLevel: CrowdLevel = .low
    ) {
        self.location = location
        self.noiseLevel = noiseLevel
        self.lightingConditions = lightingConditions
        self.crowdLevel = crowdLevel
    }
}

@available(iOS 18.0, *)
enum LocationContext: String, CaseIterable, Codable {
    case home
    case work
    case school
    case `public`
    case transport
    case outdoor
}

@available(iOS 18.0, *)
enum NoiseLevel: String, CaseIterable, Codable {
    case silent
    case quiet
    case moderate
    case loud
    case overwhelming
}

@available(iOS 18.0, *)
enum LightingConditions: String, CaseIterable, Codable {
    case dim
    case natural
    case bright
    case artificial
    case harsh
}

@available(iOS 18.0, *)
enum CrowdLevel: String, CaseIterable, Codable {
    case alone
    case low
    case moderate
    case high
    case overwhelming
}

@available(iOS 18.0, *)
struct TimeContext: Codable {
    let currentTime: Date
    let timeOfDay: TimeOfDay
    let dayOfWeek: DayOfWeek
    let isWorkingHours: Bool
    let timeZone: TimeZone

    init(
        currentTime: Date = Date(),
        timeOfDay: TimeOfDay = .morning,
        dayOfWeek: DayOfWeek = .monday,
        isWorkingHours: Bool = true,
        timeZone: TimeZone = .current
    ) {
        self.currentTime = currentTime
        self.timeOfDay = timeOfDay
        self.dayOfWeek = dayOfWeek
        self.isWorkingHours = isWorkingHours
        self.timeZone = timeZone
    }
}

@available(iOS 18.0, *)
enum TimeOfDay: String, CaseIterable, Codable {
    case earlyMorning
    case morning
    case midday
    case afternoon
    case evening
    case night
    case lateNight
}

@available(iOS 18.0, *)
enum DayOfWeek: String, CaseIterable, Codable {
    case monday
    case tuesday
    case wednesday
    case thursday
    case friday
    case saturday
    case sunday
}

@available(iOS 18.0, *)
struct SocialContext: Codable {
    let socialSetting: SocialSetting
    let interactionLevel: InteractionLevel
    let supportAvailable: Bool
    let communicationPreference: CommunicationPreference

    init(
        socialSetting: SocialSetting = .private,
        interactionLevel: InteractionLevel = .minimal,
        supportAvailable: Bool = true,
        communicationPreference: CommunicationPreference = .text
    ) {
        self.socialSetting = socialSetting
        self.interactionLevel = interactionLevel
        self.supportAvailable = supportAvailable
        self.communicationPreference = communicationPreference
    }
}

@available(iOS 18.0, *)
enum SocialSetting: String, CaseIterable, Codable {
    case private
    case smallGroup
    case largeGroup
    case `public`
    case professional
}

@available(iOS 18.0, *)
enum InteractionLevel: String, CaseIterable, Codable {
    case none
    case minimal
    case moderate
    case high
    case intensive
}

@available(iOS 18.0, *)
enum CommunicationPreference: String, CaseIterable, Codable {
    case text
    case voice
    case visual
    case tactile
    case multimodal
}

@available(iOS 18.0, *)
struct HealthContext: Codable {
    let energyLevel: EnergyLevel
    let stressLevel: StressLevel
    let sleepQuality: SleepQuality
    let medicationEffects: MedicationEffects
    let physicalComfort: PhysicalComfort

    init(
        energyLevel: EnergyLevel = .moderate,
        stressLevel: StressLevel = .low,
        sleepQuality: SleepQuality = .good,
        medicationEffects: MedicationEffects = .stable,
        physicalComfort: PhysicalComfort = .comfortable
    ) {
        self.energyLevel = energyLevel
        self.stressLevel = stressLevel
        self.sleepQuality = sleepQuality
        self.medicationEffects = medicationEffects
        self.physicalComfort = physicalComfort
    }
}

@available(iOS 18.0, *)
enum EnergyLevel: String, CaseIterable, Codable {
    case veryLow
    case low
    case moderate
    case high
    case veryHigh
}

@available(iOS 18.0, *)
enum StressLevel: String, CaseIterable, Codable {
    case minimal
    case low
    case moderate
    case high
    case overwhelming
}

@available(iOS 18.0, *)
enum SleepQuality: String, CaseIterable, Codable {
    case poor
    case fair
    case good
    case excellent
}

@available(iOS 18.0, *)
enum MedicationEffects: String, CaseIterable, Codable {
    case stable
    case improving
    case declining
    case sideEffects
    case adjusting
}

@available(iOS 18.0, *)
enum PhysicalComfort: String, CaseIterable, Codable {
    case uncomfortable
    case neutral
    case comfortable
    case veryComfortable
}

@available(iOS 18.0, *)
struct DeviceContext: Codable {
    let deviceType: DeviceType
    let batteryLevel: Double
    let connectivityStatus: ConnectivityStatus
    let accessibilitySettings: AccessibilitySettings

    init(
        accessibilitySettings: AccessibilitySettings,
        deviceType: DeviceType = .phone,
        batteryLevel: Double = 1.0,
        connectivityStatus: ConnectivityStatus = .connected
    ) {
        self.deviceType = deviceType
        self.batteryLevel = batteryLevel
        self.connectivityStatus = connectivityStatus
        self.accessibilitySettings = accessibilitySettings
    }
}

@available(iOS 18.0, *)
enum DeviceType: String, CaseIterable, Codable {
    case phone
    case tablet
    case watch
    case computer
    case tv
}

@available(iOS 18.0, *)
enum ConnectivityStatus: String, CaseIterable, Codable {
    case connected
    case limited
    case offline
}
