import Foundation
import SwiftUI

// MARK: - Cognitive Optimization

@available(iOS 18.0, *)
struct CognitiveOptimization: Codable, Identifiable, Equatable {
    let id: UUID
    var type: OptimizationType
    var title: String
    var description: String
    var implementation: ImplementationStrategy
    var expectedImpact: ImpactLevel
    var priority: OptimizationPriority
    var estimatedEffort: EffortLevel
    var targetMetrics: [String]
    var successCriteria: [String]
    var timeframe: TimeInterval
    var dependencies: [UUID]
    var status: OptimizationStatus
    var createdAt: Date
    var lastUpdated: Date

    init(
        type: OptimizationType,
        title: String,
        description: String,
        implementation: ImplementationStrategy,
        expectedImpact: ImpactLevel,
        priority: OptimizationPriority,
        estimatedEffort: EffortLevel,
        targetMetrics: [String] = [],
        successCriteria: [String] = [],
        timeframe: TimeInterval = 604_800, // 1 week
        dependencies: [UUID] = [],
        status: OptimizationStatus = .pending,
        createdAt: Date = Date(),
        id: UUID = UUID(),
        lastUpdated: Date = Date()
    ) {
        self.id = id
        self.type = type
        self.title = title
        self.description = description
        self.implementation = implementation
        self.expectedImpact = expectedImpact
        self.priority = priority
        self.estimatedEffort = estimatedEffort
        self.targetMetrics = targetMetrics
        self.successCriteria = successCriteria
        self.timeframe = timeframe
        self.dependencies = dependencies
        self.status = status
        self.createdAt = createdAt
        self.lastUpdated = lastUpdated
    }
}

// MARK: - Optimization Types

@available(iOS 18.0, *)
enum OptimizationType: String, Codable, CaseIterable {
    case cognitiveLoadReduction
    case attentionImprovement
    case distractionReduction
    case timeManagementOptimization
    case workflowOptimization
    case environmentalOptimization
    case breakOptimization
    case taskSequencingOptimization
}

@available(iOS 18.0, *)
enum ImplementationStrategy: String, Codable, CaseIterable {
    case taskBreakdown
    case focusTraining
    case environmentalChanges
    case scheduleAdjustment
    case toolIntegration
    case habitFormation
    case cognitiveTraining
    case behavioralModification
}

@available(iOS 18.0, *)
enum ImpactLevel: String, Codable, CaseIterable {
    case low
    case medium
    case high
    case transformative
}

@available(iOS 18.0, *)
enum OptimizationPriority: String, Codable, CaseIterable {
    case low
    case medium
    case high
    case critical
}

@available(iOS 18.0, *)
enum EffortLevel: String, Codable, CaseIterable {
    case minimal
    case low
    case medium
    case high
    case intensive
}

@available(iOS 18.0, *)
enum OptimizationStatus: String, Codable, CaseIterable {
    case pending
    case inProgress
    case completed
    case paused
    case cancelled
}
