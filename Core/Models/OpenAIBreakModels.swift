import Foundation

// MARK: - Break Suggestion Models

@available(iOS 18.0, *)
struct BreakSuggestion: Identifiable, Codable {
    let id = UUID()
    let type: BreakType
    let title: String
    let description: String
    let duration: TimeInterval
    let urgency: NeedUrgency
    let activities: [BreakActivity]
    let reasoning: String

    init(
        type: BreakType,
        title: String,
        description: String,
        reasoning: String,
        duration: TimeInterval = 300,
        urgency: NeedUrgency = .medium,
        activities: [BreakActivity] = []
    ) {
        self.type = type
        self.title = title
        self.description = description
        self.duration = duration
        self.urgency = urgency
        self.activities = activities
        self.reasoning = reasoning
    }
}

@available(iOS 18.0, *)
enum BreakType: String, CaseIterable, Codable {
    case cognitive
    case physical
    case sensory
    case social
    case creative
    case restorative
}

@available(iOS 18.0, *)
struct BreakActivity: Identifiable, Codable {
    let id = UUID()
    let name: String
    let description: String
    let duration: TimeInterval
    let neurodiversitySupport: [NeurodiversityType]
    let difficulty: ActivityDifficulty

    init(
        name: String,
        description: String,
        duration: TimeInterval = 300,
        neurodiversitySupport: [NeurodiversityType] = [],
        difficulty: ActivityDifficulty = .easy
    ) {
        self.name = name
        self.description = description
        self.duration = duration
        self.neurodiversitySupport = neurodiversitySupport
        self.difficulty = difficulty
    }
}

@available(iOS 18.0, *)
enum ActivityDifficulty: String, CaseIterable, Codable {
    case easy
    case moderate
    case challenging
}
