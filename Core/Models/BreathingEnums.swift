import Foundation
import SwiftUI

// MARK: - Breathing Exercise Enums

/// Breathing exercise types
enum BreathingType: String, CaseIterable, Codable {
    case boxBreathing = "Box Breathing"
    case fourSevenEight = "4-7-8 Breathing"
    case equalBreathing = "Equal Breathing"
    case bellowsBreath = "Bellows Breath"
    case alternateNostril = "Alternate Nostril"
    case coherentBreathing = "Coherent Breathing"
    case triangleBreathing = "Triangle Breathing"

    var description: String {
        switch self {
        case .boxBreathing: return "Inhale, hold, exhale, hold for equal counts"
        case .fourSevenEight: return "Inhale for 4, hold for 7, exhale for 8"
        case .equalBreathing: return "Equal inhale and exhale durations"
        case .bellowsBreath: return "Rapid, energizing breathing technique"
        case .alternateNostril: return "Breathing through alternating nostrils"
        case .coherentBreathing: return "5-second inhale and exhale rhythm"
        case .triangleBreathing: return "Inhale, hold, exhale in equal parts"
        }
    }
}

/// Visual guide types for breathing exercises
enum VisualGuideType: String, CaseIterable, Codable {
    case circle = "Circle"
    case square = "Square"
    case wave = "Wave"
    case flower = "Flower"
    case minimal
    case none
}

/// Audio guide types
enum AudioGuideType: String, CaseIterable, Codable {
    case gentle = "Gentle"
    case nature = "Nature"
    case chimes = "Chimes"
    case voice = "Voice"
    case minimal
    case none
}

/// Haptic feedback types
enum HapticFeedbackType: String, CaseIterable, Codable {
    case subtle = "Subtle"
    case rhythmic = "Rhythmic"
    case strong = "Strong"
    case none
}
