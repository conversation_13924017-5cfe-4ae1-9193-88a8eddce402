import Foundation
import SwiftUI

// MARK: - Comprehensive Type Aliases for Build Success
// This file provides all necessary type aliases to resolve BreathingExercise → BreathingPattern migration

@available(iOS 26.0, *)
public typealias BreathingExercise = BreathingPattern

@available(iOS 26.0, *)
public typealias BreathingExerciseType = BreathingDifficulty

@available(iOS 26.0, *)
public typealias BreathingExerciseServiceProtocol = BreathingServiceProtocol

@available(iOS 26.0, *)
public typealias BreathingExerciseServiceError = BreathingServiceError

@available(iOS 26.0, *)
public typealias BreathingPatternServiceProtocol = BreathingServiceProtocol

@available(iOS 26.0, *)
public typealias BreathingPatternServiceError = BreathingServiceError

// MARK: - Supporting Type Aliases

@available(iOS 26.0, *)
public typealias ExerciseStatistics = BreathingStatistics

@available(iOS 26.0, *)
public struct BreathingStatistics: Codable, Sendable {
    public let exerciseId: UUID
    public let totalSessions: Int
    public let averageDuration: TimeInterval
    public let completionRate: Double
    public let lastUsed: Date
    
    public init(
        exerciseId: UUID,
        totalSessions: Int = 0,
        averageDuration: TimeInterval = 0,
        completionRate: Double = 0,
        lastUsed: Date = Date()
    ) {
        self.exerciseId = exerciseId
        self.totalSessions = totalSessions
        self.averageDuration = averageDuration
        self.completionRate = completionRate
        self.lastUsed = lastUsed
    }
}

@available(iOS 26.0, *)
public enum BreathingServiceError: Error, LocalizedError {
    case sessionAlreadyActive
    case healthKitNotAvailable
    case watchNotConnected
    case invalidPattern
    case userNotFound
    
    public var errorDescription: String? {
        switch self {
        case .sessionAlreadyActive:
            return "A breathing session is already active"
        case .healthKitNotAvailable:
            return "HealthKit is not available"
        case .watchNotConnected:
            return "Apple Watch is not connected"
        case .invalidPattern:
            return "Invalid breathing pattern"
        case .userNotFound:
            return "User profile not found"
        }
    }
}

// MARK: - Extension to BreathingPattern for BreathingExercise compatibility

@available(iOS 26.0, *)
public extension BreathingPattern {
    // Add properties that BreathingExercise had but BreathingPattern doesn't
    var duration: TimeInterval {
        // Calculate duration based on pattern timing
        let cycleTime = Double(inhaleCount + holdCount + exhaleCount + pauseCount)
        return cycleTime * Double(cycles)
    }
    
    var type: BreathingDifficulty {
        difficulty
    }
    
    var instructions: [String] {
        [
            "Prepare yourself in a comfortable position",
            "Breathe in for \(inhaleCount) counts",
            holdCount > 0 ? "Hold for \(holdCount) counts" : nil,
            "Breathe out for \(exhaleCount) counts",
            pauseCount > 0 ? "Pause for \(pauseCount) counts" : nil,
            "Repeat for \(cycles) cycles"
        ].compactMap { $0 }
    }
    
    var benefits: [String] {
        switch difficulty {
        case .beginner:
            return ["Reduces stress", "Improves focus", "Easy to learn"]
        case .intermediate:
            return ["Enhanced relaxation", "Better emotional regulation", "Improved concentration"]
        case .advanced:
            return ["Deep meditation state", "Advanced stress management", "Enhanced mindfulness"]
        }
    }
    
    var isCustom: Bool {
        !BreathingPattern.defaultPatterns.contains(self)
    }
    
    var usageCount: Int {
        0 // Stub - would be tracked in user data
    }
    
    var averageRating: Double {
        4.5 // Stub - would be calculated from user ratings
    }
    
    var tags: [String] {
        var tags = [difficulty.displayName.lowercased()]
        tags.append(contentsOf: neurodiversitySupport.map { $0.rawValue })
        return tags
    }
}
