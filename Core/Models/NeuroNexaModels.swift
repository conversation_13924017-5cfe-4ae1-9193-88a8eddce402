import CloudKit
import CoreData
import Foundation
import SwiftUI

// MARK: - NeuroNexa Models - Central Import File
// Note: All model types are available through individual Swift files in the same target

// MARK: - Essential Services (Compact Implementation) - REMOVED TO AVOID CONFLICTS
// Note: Individual service implementations are available in their respective files

// MARK: - Essential Views (Minimal Placeholders)
// Note: View implementations moved to Core/Models/ViewPlaceholders.swift to avoid conflicts

// UserContext and UserState are defined in Core/Models/UserProfileModels.swift

@available(iOS 18.0, *)
struct PersonalizedContent: Identifiable, Codable {
    let id = UUID()
    let type: ContentType
    let title: String
    let content: String
    let priority: ContentPriority

    init(type: ContentType, title: String, content: String, priority: ContentPriority = .medium) {
        self.type = type; self.title = title; self.content = content; self.priority = priority
    }
}

@available(iOS 18.0, *)
enum ContentType: String, CaseIterable, Codable {
    case taskSuggestion, motivationalMessage, educationalTip, breathingExercise, cognitiveStrategy, executiveSupport, sensoryBreak, socialScript
}

@available(iOS 18.0, *)
enum ContentPriority: String, CaseIterable, Codable {
    case low, medium, high, urgent
}

@available(iOS 18.0, *)
struct CognitiveAdaptation: Codable {
    let adaptationType: AdaptationType
    let modifications: [UIModification]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
enum AdaptationType: String, CaseIterable, Codable {
    case visual, auditory, cognitive, motor, sensory, temporal
}

@available(iOS 18.0, *)
struct UIModification: Codable {
    let component: UIComponent
    let property: UIProperty
    let value: String
}

@available(iOS 18.0, *)
enum UIComponent: String, CaseIterable, Codable {
    case button, text, background, animation, transition, layout, navigation, input
}

@available(iOS 18.0, *)
enum UIProperty: String, CaseIterable, Codable {
    case color, size, spacing, duration, opacity, contrast, motion, sound
}

@available(iOS 18.0, *)
struct BreakSuggestion: Identifiable, Codable {
    let id = UUID()
    let type: BreakType
    let title: String
    let description: String
    let duration: TimeInterval
    let urgency: BreakUrgency

    init(type: BreakType, title: String, description: String, duration: TimeInterval, urgency: BreakUrgency = .moderate) {
        self.type = type; self.title = title; self.description = description; self.duration = duration; self.urgency = urgency
    }
}

@available(iOS 18.0, *)
enum BreakType: String, CaseIterable, Codable {
    case cognitive, sensory, physical, emotional, social, creative
}

@available(iOS 18.0, *)
enum BreakUrgency: String, CaseIterable, Codable {
    case low, moderate, high, critical
}

// MARK: - Essential Protocols for Build Target

// UserRepositoryProtocol is defined in Core/Models/UserProfileModels.swift

@available(iOS 18.0, *)
protocol BreathingExerciseServiceProtocol {
    func getAvailableExercises() async -> [BreathingExercise]
    func getPersonalizedExercises(for userProfile: UserProfile) async -> [BreathingExercise]
    func startBreathingSession(_ exercise: BreathingExercise) async throws
    func pauseSession() async
    func resumeSession() async
    func endBreathingSession() async throws -> BreathingSessionResult
}

// MARK: - Essential Types for Build Target
// Note: BreathingSessionResult is defined in Core/Models/BreathingModels.swift

@available(iOS 18.0, *)
struct AnxietyDetection: Codable {
    let level: OverwhelmLevel
    let confidence: Double
    let timestamp: Date
    let triggers: [String]
}

// AnxietyOverwhelmDetection is defined in Core/Models/BreathingModels.swift

// MARK: - Additional Missing Types

@available(iOS 18.0, *)
protocol SettingsServiceProtocol {
    func getSettings() async -> UserSettings
    func saveSettings(_ settings: UserSettings) async throws
}

// Note: Routine, RoutineSchedule, and RoutineFrequency moved to Core/Repositories/RoutineRepository.swift to avoid conflicts

@available(iOS 18.0, *)
struct MotionAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
struct ColorAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
struct SoundAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
struct ExecutiveFunctionInsights: Codable {
    let patterns: [String]
    let recommendations: [String]
    let score: Double
}

@available(iOS 18.0, *)
struct BehaviorInsights: Codable {
    let patterns: [String]
    let trends: [String]
    let recommendations: [String]
}

// UserBehaviorData and UserNeed are defined in Core/Models/UserProfileModels.swift

// Note: TaskTiming is defined in Core/Models/TaskModels.swift

@available(iOS 18.0, *)
struct CognitiveAnalysis: Codable {
    let patterns: [String]
    let insights: [String]
    let recommendations: [String]
    let score: Double
}

@available(iOS 18.0, *)
struct CognitivePatterns: Codable {
    let patterns: [String]
    let frequency: [String: Int]
    let trends: [String]
}

@available(iOS 18.0, *)
struct CognitiveOptimization: Codable {
    let type: String
    let description: String
    let expectedImprovement: Double
    let implementation: [String]
}

// Note: CognitivePreferences is defined in Core/Models/UserProfileModels.swift

@available(iOS 18.0, *)
struct NeuroNexaNotificationSettings: Codable {
    let enabled: Bool
    let taskReminders: Bool
    let breathingReminders: Bool
    let cognitiveBreaks: Bool
    let quietHours: QuietHours

    init(enabled: Bool = true, taskReminders: Bool = true, breathingReminders: Bool = true, cognitiveBreaks: Bool = true, quietHours: QuietHours = QuietHours(enabled: false, startTime: Date(), endTime: Date())) {
        self.enabled = enabled
        self.taskReminders = taskReminders
        self.breathingReminders = breathingReminders
        self.cognitiveBreaks = cognitiveBreaks
        self.quietHours = quietHours
    }
}

@available(iOS 18.0, *)
struct NeuroNexaPrivacySettings: Codable {
    let dataSharing: Bool
    let analytics: Bool
    let crashReporting: Bool
    let personalizedContent: Bool

    init(dataSharing: Bool = false, analytics: Bool = true, crashReporting: Bool = true, personalizedContent: Bool = true) {
        self.dataSharing = dataSharing
        self.analytics = analytics
        self.crashReporting = crashReporting
        self.personalizedContent = personalizedContent
    }
}

@available(iOS 18.0, *)
struct UserSettings: Codable {
    let id: UUID
    let userId: UUID
    let theme: NeuroNexaTheme
    let notifications: NeuroNexaNotificationSettings
    let accessibility: AccessibilitySettings
    let privacy: NeuroNexaPrivacySettings
}

@available(iOS 18.0, *)
struct NotificationSettings: Codable {
    let enabled: Bool
    let taskReminders: Bool
    let breathingReminders: Bool
    let cognitiveBreaks: Bool
    let quietHours: QuietHours
}

@available(iOS 18.0, *)
struct QuietHours: Codable {
    let enabled: Bool
    let startTime: Date
    let endTime: Date
}

@available(iOS 18.0, *)
struct PrivacySettings: Codable {
    let dataSharing: Bool
    let analytics: Bool
    let crashReporting: Bool
    let personalizedAds: Bool
}

// Note: BreathingService is defined in Core/Services/BreathingService.swift

// MARK: - Essential Services (Compact) - REMOVED TO AVOID CONFLICTS
// Note: Individual service implementations are available in their respective files

// Note: Service implementations are available in their respective files:
// - PersonalizedTaskService: Core/Services/PersonalizedTaskService.swift
// - CognitiveAnalysisService: Core/Services/CognitiveAnalysisService.swift
// - WatchConnectivityService: Core/Services/WatchConnectivityService.swift
// - UserProfileRepository: Core/Repositories/UserProfileRepository.swift
// - TaskRepository: Core/Repositories/TaskRepository.swift
// - RoutineRepository: Core/Repositories/RoutineRepository.swift
// - BreathingSessionRepository: Core/Repositories/BreathingSessionRepository.swift

// MARK: - Missing Views (Minimal Placeholders)
// Note: AuthenticationView and OnboardingView moved to Core/Models/ViewPlaceholders.swift to avoid conflicts
// Note: RoutineBuilderView moved to Core/Models/ViewPlaceholders.swift to avoid conflicts
// Note: AITaskCoachView moved to dedicated view files to avoid conflicts
// Note: BreathingView moved to UI/Views/BreathingView.swift to avoid conflicts
