import Foundation

// MARK: - Sensory Cognitive Analysis Models

@available(iOS 18.0, *)
struct SensoryCognitiveAnalysis: Identifiable, Codable {
    let id = UUID()
    let taskId: UUID
    let analysisDate: Date
    let cognitiveMetrics: SensoryCognitiveMetrics
    let patterns: [SensoryCognitivePattern]
    let insights: [SensoryCognitiveInsight]
    let recommendations: [SensoryCognitiveRecommendation]

    init(
        taskId: UUID,
        cognitiveMetrics: SensoryCognitiveMetrics,
        analysisDate: Date = Date(),
        patterns: [SensoryCognitivePattern] = [],
        insights: [SensoryCognitiveInsight] = [],
        recommendations: [SensoryCognitiveRecommendation] = []
    ) {
        self.taskId = taskId
        self.analysisDate = analysisDate
        self.cognitiveMetrics = cognitiveMetrics
        self.patterns = patterns
        self.insights = insights
        self.recommendations = recommendations
    }
}

@available(iOS 18.0, *)
struct SensoryCognitiveMetrics: Codable {
    let attentionScore: Double
    let workingMemoryLoad: Double
    let processingSpeed: Double
    let executiveFunctionScore: Double
    let overallCognitiveLoad: Double

    init(
        attentionScore: Double = 0.5,
        workingMemoryLoad: Double = 0.5,
        processingSpeed: Double = 0.5,
        executiveFunctionScore: Double = 0.5,
        overallCognitiveLoad: Double = 0.5
    ) {
        self.attentionScore = attentionScore
        self.workingMemoryLoad = workingMemoryLoad
        self.processingSpeed = processingSpeed
        self.executiveFunctionScore = executiveFunctionScore
        self.overallCognitiveLoad = overallCognitiveLoad
    }
}

@available(iOS 18.0, *)
struct SensoryCognitivePattern: Identifiable, Codable {
    let id = UUID()
    let type: SensoryPatternType
    let description: String
    let frequency: Double
    let impact: SensoryPatternImpact
    let confidence: Double

    init(type: SensoryPatternType, description: String, frequency: Double, impact: SensoryPatternImpact, confidence: Double = 0.8) {
        self.type = type
        self.description = description
        self.frequency = frequency
        self.impact = impact
        self.confidence = confidence
    }
}

@available(iOS 18.0, *)
enum SensoryPatternType: String, CaseIterable, Codable {
    case timeOfDayPerformance
    case taskTypePreference
    case cognitiveLoadTolerance
    case breakPatterns
    case environmentalInfluence
}

@available(iOS 18.0, *)
enum SensoryPatternImpact: String, CaseIterable, Codable {
    case positive
    case neutral
    case negative
}

@available(iOS 18.0, *)
struct SensoryCognitiveInsight: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let category: SensoryInsightCategory
    let actionable: Bool
    let priority: SensoryInsightPriority

    init(title: String, description: String, category: SensoryInsightCategory, actionable: Bool = true, priority: SensoryInsightPriority = .medium) {
        self.title = title
        self.description = description
        self.category = category
        self.actionable = actionable
        self.priority = priority
    }
}

@available(iOS 18.0, *)
enum SensoryInsightCategory: String, CaseIterable, Codable {
    case performance
    case wellbeing
    case efficiency
    case adaptation
}

@available(iOS 18.0, *)
enum SensoryInsightPriority: String, CaseIterable, Codable {
    case low
    case medium
    case high
}

@available(iOS 18.0, *)
struct SensoryCognitiveRecommendation: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let type: SensoryRecommendationType
    let estimatedImpact: Double
    let implementationDifficulty: SensoryImplementationDifficulty

    init(
        title: String,
        description: String,
        type: SensoryRecommendationType,
        estimatedImpact: Double = 0.5,
        implementationDifficulty: SensoryImplementationDifficulty = .medium
    ) {
        self.title = title
        self.description = description
        self.type = type
        self.estimatedImpact = estimatedImpact
        self.implementationDifficulty = implementationDifficulty
    }
}

@available(iOS 18.0, *)
enum SensoryImplementationDifficulty: String, CaseIterable, Codable {
    case easy
    case medium
    case hard
}
