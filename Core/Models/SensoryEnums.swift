import Foundation
import SwiftUI

// MARK: - Sensory-Related Enums

/// Sensory sensitivity levels
enum MotionSensitivity: String, CaseIterable, Codable {
    case none
    case low
    case medium
    case high
    case extreme

    var animationDuration: Double {
        switch self {
        case .none: return 0.3
        case .low: return 0.5
        case .medium: return 0.8
        case .high: return 1.2
        case .extreme: return 2.0
        }
    }

    var allowsAnimation: Bool {
        switch self {
        case .none, .low: return true
        case .medium, .high, .extreme: return false
        }
    }
}

enum SoundSensitivity: String, CaseIterable, Codable {
    case none
    case low
    case medium
    case high
    case extreme

    var volumeMultiplier: Double {
        switch self {
        case .none: return 1.0
        case .low: return 0.8
        case .medium: return 0.6
        case .high: return 0.4
        case .extreme: return 1.0
        }
    }
}

enum HapticIntensity: String, CaseIterable, Codable {
    case none
    case light
    case medium
    case strong

    var intensity: CGFloat {
        switch self {
        case .none: return 0.0
        case .light: return 0.3
        case .medium: return 0.6
        case .strong: return 1.0
        }
    }
}

enum LightSensitivity: String, CaseIterable, Codable {
    case low
    case medium
    case high
    case extreme

    var brightnessMultiplier: Double {
        switch self {
        case .low: return 1.0
        case .medium: return 0.8
        case .high: return 0.6
        case .extreme: return 0.4
        }
    }
}

enum TexturePreferences: String, CaseIterable, Codable {
    case smooth
    case textured
    case minimal
    case rich
}

/// Sensory modes for reminders
public enum SensoryMode: String, CaseIterable, Codable, Sendable {
    case visual
    case audio
    case haptic
    case multimodal
    case minimal
}

/// Sensory considerations for tasks
public enum SensoryConsideration: String, CaseIterable, Codable, Sendable {
    case quietEnvironment = "Quiet Environment"
    case lowLight = "Low Light"
    case minimalVisualStimuli = "Minimal Visual Stimuli"
    case comfortableSeating = "Comfortable Seating"
    case temperatureControl = "Temperature Control"
    case reducedCrowds = "Reduced Crowds"
    case familiarEnvironment = "Familiar Environment"
}
