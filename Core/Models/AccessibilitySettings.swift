import Foundation

// MARK: - Accessibility Settings Models

@available(iOS 18.0, *)
public struct AccessibilitySettings: Codable, Equatable, Sendable {
    let id: UUID

    // Voice and Audio
    var voiceOverSupport: Bool
    var speechRate: SpeechRate
    var audioDescriptions: Bool
    var soundEffectsEnabled: Bool
    var hapticFeedbackEnabled: Bool

    // Visual Accessibility
    var reduceMotion: Bool
    var increaseContrast: Bool
    var boldText: Bool
    var largerText: Bool
    var buttonShapes: Bool
    var reduceTransparency: Bool

    // Motor Accessibility
    var assistiveTouch: Bool
    var touchAccommodations: Bool
    var holdDuration: TimeInterval
    var ignoreRepeat: TimeInterval

    // Cognitive Accessibility
    var simplifiedInterface: Bool
    var reducedAnimations: Bool
    var extendedTimeouts: Bool
    var confirmationDialogs: Bool
    var guidedAccess: Bool

    // Sensory Preferences
    var sensoryPreferences: SensoryPreferences

    // Timestamps
    let createdAt: Date
    var updatedAt: Date

    init(
        id: UUID = UUID(),
        voiceOverSupport: Bool = false,
        speechRate: SpeechRate = .normal,
        audioDescriptions: Bool = false,
        soundEffectsEnabled: Bool = true,
        hapticFeedbackEnabled: Bool = true,
        reduceMotion: Bool = false,
        increaseContrast: Bool = false,
        boldText: Bool = false,
        largerText: Bool = false,
        buttonShapes: Bool = false,
        reduceTransparency: Bool = false,
        assistiveTouch: Bool = false,
        touchAccommodations: Bool = false,
        holdDuration: TimeInterval = 0.1,
        ignoreRepeat: TimeInterval = 0.0,
        simplifiedInterface: Bool = false,
        reducedAnimations: Bool = false,
        extendedTimeouts: Bool = false,
        confirmationDialogs: Bool = false,
        guidedAccess: Bool = false,
        sensoryPreferences: SensoryPreferences = SensoryPreferences(),
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.voiceOverSupport = voiceOverSupport
        self.speechRate = speechRate
        self.audioDescriptions = audioDescriptions
        self.soundEffectsEnabled = soundEffectsEnabled
        self.hapticFeedbackEnabled = hapticFeedbackEnabled
        self.reduceMotion = reduceMotion
        self.increaseContrast = increaseContrast
        self.boldText = boldText
        self.largerText = largerText
        self.buttonShapes = buttonShapes
        self.reduceTransparency = reduceTransparency
        self.assistiveTouch = assistiveTouch
        self.touchAccommodations = touchAccommodations
        self.holdDuration = holdDuration
        self.ignoreRepeat = ignoreRepeat
        self.simplifiedInterface = simplifiedInterface
        self.reducedAnimations = reducedAnimations
        self.extendedTimeouts = extendedTimeouts
        self.confirmationDialogs = confirmationDialogs
        self.guidedAccess = guidedAccess
        self.sensoryPreferences = sensoryPreferences
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // MARK: - Static Default

    nonisolated(unsafe) public static let `default` = AccessibilitySettings()
}

// MARK: - Speech Rate

@available(iOS 18.0, *)
public enum SpeechRate: String, CaseIterable, Codable, Sendable {
    case verySlow
    case slow
    case normal
    case fast
    case veryFast

    var displayName: String {
        switch self {
        case .verySlow: return "Very Slow"
        case .slow: return "Slow"
        case .normal: return "Normal"
        case .fast: return "Fast"
        case .veryFast: return "Very Fast"
        }
    }

    var rate: Float {
        switch self {
        case .verySlow: return 0.3
        case .slow: return 0.4
        case .normal: return 0.5
        case .fast: return 0.6
        case .veryFast: return 0.7
        }
    }
}

// MARK: - Sensory Preferences

@available(iOS 18.0, *)
public struct SensoryPreferences: Codable, Equatable, Sendable {
    // Motion Sensitivity
    public var motionSensitivity: Double // 0.0 to 1.0
    public var motionReductionLevel: MotionReductionLevel

    // Sound Sensitivity
    public var soundSensitivity: Double // 0.0 to 1.0
    public var soundReductionEnabled: Bool
    public var backgroundSoundsEnabled: Bool

    // Light Sensitivity
    public var lightSensitivity: Double // 0.0 to 1.0
    public var autoBrightnessEnabled: Bool
    public var blueLightFilterEnabled: Bool
    public var darkModePreferred: Bool

    // Touch Sensitivity
    public var touchSensitivity: Double // 0.0 to 1.0
    public var vibrationIntensity: VibrationIntensity

    // Color Preferences
    public var colorContrast: ColorContrast
    public var colorBlindnessSupport: ColorBlindnessType

    public init(
        motionSensitivity: Double = 0.5,
        motionReductionLevel: MotionReductionLevel = .moderate,
        soundSensitivity: Double = 0.5,
        soundReductionEnabled: Bool = false,
        backgroundSoundsEnabled: Bool = false,
        lightSensitivity: Double = 0.5,
        autoBrightnessEnabled: Bool = true,
        blueLightFilterEnabled: Bool = false,
        darkModePreferred: Bool = false,
        touchSensitivity: Double = 0.5,
        vibrationIntensity: VibrationIntensity = .medium,
        colorContrast: ColorContrast = .normal,
        colorBlindnessSupport: ColorBlindnessType = .none
    ) {
        self.motionSensitivity = motionSensitivity
        self.motionReductionLevel = motionReductionLevel
        self.soundSensitivity = soundSensitivity
        self.soundReductionEnabled = soundReductionEnabled
        self.backgroundSoundsEnabled = backgroundSoundsEnabled
        self.lightSensitivity = lightSensitivity
        self.autoBrightnessEnabled = autoBrightnessEnabled
        self.blueLightFilterEnabled = blueLightFilterEnabled
        self.darkModePreferred = darkModePreferred
        self.touchSensitivity = touchSensitivity
        self.vibrationIntensity = vibrationIntensity
        self.colorContrast = colorContrast
        self.colorBlindnessSupport = colorBlindnessSupport
    }

    // MARK: - Static Default

    nonisolated(unsafe) public static let `default` = SensoryPreferences()
}

// MARK: - Motion Reduction Level

@available(iOS 18.0, *)
public enum MotionReductionLevel: String, CaseIterable, Codable, Sendable {
    case none
    case minimal
    case moderate
    case significant
    case maximum

    var displayName: String {
        switch self {
        case .none: return "None"
        case .minimal: return "Minimal"
        case .moderate: return "Moderate"
        case .significant: return "Significant"
        case .maximum: return "Maximum"
        }
    }

    var reductionFactor: Double {
        switch self {
        case .none: return 1.0
        case .minimal: return 0.8
        case .moderate: return 0.6
        case .significant: return 0.4
        case .maximum: return 0.2
        }
    }
}

// MARK: - Vibration Intensity

@available(iOS 18.0, *)
public enum VibrationIntensity: String, CaseIterable, Codable, Sendable {
    case off
    case light
    case medium
    case strong

    var displayName: String {
        switch self {
        case .off: return "Off"
        case .light: return "Light"
        case .medium: return "Medium"
        case .strong: return "Strong"
        }
    }

    var intensity: Double {
        switch self {
        case .off: return 0.0
        case .light: return 0.3
        case .medium: return 0.6
        case .strong: return 1.0
        }
    }
}

// MARK: - Color Contrast

@available(iOS 18.0, *)
public enum ColorContrast: String, CaseIterable, Codable, Sendable {
    case normal
    case increased
    case high
    case maximum

    var displayName: String {
        switch self {
        case .normal: return "Normal"
        case .increased: return "Increased"
        case .high: return "High"
        case .maximum: return "Maximum"
        }
    }

    var contrastRatio: Double {
        switch self {
        case .normal: return 4.5
        case .increased: return 7.0
        case .high: return 10.0
        case .maximum: return 15.0
        }
    }
}

// MARK: - Color Blindness Support

@available(iOS 18.0, *)
public enum ColorBlindnessType: String, CaseIterable, Codable, Sendable {
    case none
    case protanopia
    case deuteranopia
    case tritanopia
    case achromatopsia

    var displayName: String {
        switch self {
        case .none: return "None"
        case .protanopia: return "Protanopia (Red-blind)"
        case .deuteranopia: return "Deuteranopia (Green-blind)"
        case .tritanopia: return "Tritanopia (Blue-blind)"
        case .achromatopsia: return "Achromatopsia (Color-blind)"
        }
    }

    var description: String {
        switch self {
        case .none: return "No color vision deficiency"
        case .protanopia: return "Difficulty distinguishing red and green"
        case .deuteranopia: return "Difficulty distinguishing red and green"
        case .tritanopia: return "Difficulty distinguishing blue and yellow"
        case .achromatopsia: return "Complete absence of color vision"
        }
    }
}
