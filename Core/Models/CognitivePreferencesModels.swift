import Foundation
import SwiftUI

// MARK: - Cognitive Preferences

@available(iOS 18.0, *)
public struct CognitivePreferences: Codable, Identifiable, Equatable, Sendable {
    public let id: UUID
    public var preferredCognitiveLoad: Double // 0.0 to 1.0
    public var maxCognitiveLoad: Double // 0.0 to 1.0
    public var preferredTaskComplexity: TaskComplexity
    public var preferredSessionLength: TimeInterval
    public var breakFrequency: TimeInterval
    public var breakDuration: TimeInterval
    public var focusEnhancementTechniques: [FocusEnhancementTechnique]
    public var cognitiveSupports: [CognitiveSupport]
    public var adaptationSpeed: AdaptationSpeed
    public var feedbackPreferences: FeedbackPreferences
    public var lastUpdated: Date

    public init(
        id: UUID = UUID(),
        preferredCognitiveLoad: Double = 0.5,
        maxCognitiveLoad: Double = 0.8,
        preferredTaskComplexity: TaskComplexity = .medium,
        preferredSessionLength: TimeInterval = 1_800, // 30 minutes
        breakFrequency: TimeInterval = 1_800, // Every 30 minutes
        breakDuration: TimeInterval = 300, // 5 minutes
        focusEnhancementTechniques: [FocusEnhancementTechnique] = [.timeBlocking],
        cognitiveSupports: [CognitiveSupport] = [.visualCues],
        adaptationSpeed: AdaptationSpeed = .moderate,
        feedbackPreferences: FeedbackPreferences = FeedbackPreferences(),
        lastUpdated: Date = Date()
    ) {
        self.id = id
        self.preferredCognitiveLoad = preferredCognitiveLoad
        self.maxCognitiveLoad = maxCognitiveLoad
        self.preferredTaskComplexity = preferredTaskComplexity
        self.preferredSessionLength = preferredSessionLength
        self.breakFrequency = breakFrequency
        self.breakDuration = breakDuration
        self.focusEnhancementTechniques = focusEnhancementTechniques
        self.cognitiveSupports = cognitiveSupports
        self.adaptationSpeed = adaptationSpeed
        self.feedbackPreferences = feedbackPreferences
        self.lastUpdated = lastUpdated
    }
}
