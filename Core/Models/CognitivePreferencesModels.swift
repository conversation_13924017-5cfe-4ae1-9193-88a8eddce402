import Foundation
import SwiftUI

// MARK: - Cognitive Preferences

@available(iOS 18.0, *)
struct CognitivePreferences: Codable, Identifiable, Equatable {
    let id: UUID
    var preferredCognitiveLoad: Double // 0.0 to 1.0
    var maxCognitiveLoad: Double // 0.0 to 1.0
    var preferredTaskComplexity: TaskComplexity
    var preferredSessionLength: TimeInterval
    var breakFrequency: TimeInterval
    var breakDuration: TimeInterval
    var focusEnhancementTechniques: [FocusEnhancementTechnique]
    var cognitiveSupports: [CognitiveSupport]
    var adaptationSpeed: AdaptationSpeed
    var feedbackPreferences: FeedbackPreferences
    var lastUpdated: Date

    init(
        id: UUID = UUID(),
        preferredCognitiveLoad: Double = 0.5,
        maxCognitiveLoad: Double = 0.8,
        preferredTaskComplexity: TaskComplexity = .medium,
        preferredSessionLength: TimeInterval = 1_800, // 30 minutes
        breakFrequency: TimeInterval = 1_800, // Every 30 minutes
        breakDuration: TimeInterval = 300, // 5 minutes
        focusEnhancementTechniques: [FocusEnhancementTechnique] = [.timeBlocking],
        cognitiveSupports: [CognitiveSupport] = [.visualCues],
        adaptationSpeed: AdaptationSpeed = .moderate,
        feedbackPreferences: FeedbackPreferences = FeedbackPreferences(),
        lastUpdated: Date = Date()
    ) {
        self.id = id
        self.preferredCognitiveLoad = preferredCognitiveLoad
        self.maxCognitiveLoad = maxCognitiveLoad
        self.preferredTaskComplexity = preferredTaskComplexity
        self.preferredSessionLength = preferredSessionLength
        self.breakFrequency = breakFrequency
        self.breakDuration = breakDuration
        self.focusEnhancementTechniques = focusEnhancementTechniques
        self.cognitiveSupports = cognitiveSupports
        self.adaptationSpeed = adaptationSpeed
        self.feedbackPreferences = feedbackPreferences
        self.lastUpdated = lastUpdated
    }
}
