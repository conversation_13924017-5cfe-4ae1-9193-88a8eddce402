import Foundation

// MARK: - Break Suggestion Types

@available(iOS 18.0, *)
struct BreakSuggestion: Identifiable, Codable {
    let id = UUID()
    let type: BreakType
    let title: String
    let description: String
    let duration: TimeInterval
    let urgency: BreakUrgency
    let targetConditions: [NeurodiversityType]
    let activities: [BreakActivity]
    let environment: BreakEnvironment
    let effectiveness: Double
    let personalizedMessage: String

    init(
        type: BreakType,
        title: String,
        description: String,
        duration: TimeInterval,
        urgency: BreakUrgency = .moderate,
        targetConditions: [NeurodiversityType] = [],
        activities: [BreakActivity] = [],
        environment: BreakEnvironment = BreakEnvironment(),
        effectiveness: Double = 0.0,
        personalizedMessage: String = ""
    ) {
        self.type = type
        self.title = title
        self.description = description
        self.duration = duration
        self.urgency = urgency
        self.targetConditions = targetConditions
        self.activities = activities
        self.environment = environment
        self.effectiveness = effectiveness
        self.personalizedMessage = personalizedMessage
    }
}

@available(iOS 18.0, *)
enum BreakType: String, CaseIterable, Codable {
    case cognitive
    case sensory
    case physical
    case emotional
    case social
    case creative
}

@available(iOS 18.0, *)
enum BreakUrgency: String, CaseIterable, Codable {
    case low
    case moderate
    case high
    case critical
}

@available(iOS 18.0, *)
struct BreakActivity: Codable {
    let name: String
    let description: String
    let duration: TimeInterval
    let type: ActivityType
    let difficulty: ActivityDifficulty
    let requirements: [ActivityRequirement]

    init(
        name: String,
        description: String,
        duration: TimeInterval,
        type: ActivityType,
        difficulty: ActivityDifficulty = .easy,
        requirements: [ActivityRequirement] = []
    ) {
        self.name = name
        self.description = description
        self.duration = duration
        self.type = type
        self.difficulty = difficulty
        self.requirements = requirements
    }
}

@available(iOS 18.0, *)
enum ActivityType: String, CaseIterable, Codable {
    case breathing
    case stretching
    case meditation
    case visualization
    case movement
    case sensory
    case creative
    case social
}

@available(iOS 18.0, *)
enum ActivityDifficulty: String, CaseIterable, Codable {
    case easy
    case moderate
    case challenging
}

@available(iOS 18.0, *)
struct ActivityRequirement: Codable {
    let type: RequirementType
    let description: String
    let optional: Bool

    init(type: RequirementType, description: String, optional: Bool = false) {
        self.type = type
        self.description = description
        self.optional = optional
    }
}

@available(iOS 18.0, *)
enum RequirementType: String, CaseIterable, Codable {
    case space
    case equipment
    case privacy
    case time
    case energy
}

@available(iOS 18.0, *)
struct BreakEnvironment: Codable {
    let preferredLocation: LocationPreference
    let noiseLevel: NoiseLevel
    let lightingPreference: LightingPreference
    let socialSetting: SocialSetting
    let accessibilityNeeds: [AccessibilityNeed]

    init(
        preferredLocation: LocationPreference = .current,
        noiseLevel: NoiseLevel = .quiet,
        lightingPreference: LightingPreference = .dim,
        socialSetting: SocialSetting = .private,
        accessibilityNeeds: [AccessibilityNeed] = []
    ) {
        self.preferredLocation = preferredLocation
        self.noiseLevel = noiseLevel
        self.lightingPreference = lightingPreference
        self.socialSetting = socialSetting
        self.accessibilityNeeds = accessibilityNeeds
    }
}

@available(iOS 18.0, *)
enum LocationPreference: String, CaseIterable, Codable {
    case current
    case quiet
    case `private`
    case outdoor
    case indoor
    case flexible
}

@available(iOS 18.0, *)
enum LightingPreference: String, CaseIterable, Codable {
    case dim
    case natural
    case bright
    case colored
    case adjustable
}

@available(iOS 18.0, *)
enum AccessibilityNeed: String, CaseIterable, Codable {
    case mobility
    case vision
    case hearing
    case cognitive
    case sensory
    case communication
}
