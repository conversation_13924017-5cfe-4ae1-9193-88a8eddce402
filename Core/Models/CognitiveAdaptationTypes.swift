import Foundation

// MARK: - Cognitive Adaptation Types

@available(iOS 18.0, *)
struct CognitiveAdaptation: Codable {
    let adaptationType: AdaptationType
    let modifications: [UIModification]
    let reasoning: String
    let expectedBenefit: String
    let duration: AdaptationDuration
    let targetConditions: [NeurodiversityType]
    let priority: AdaptationPriority
    let reversible: Bool

    init(
        adaptationType: AdaptationType,
        modifications: [UIModification],
        reasoning: String,
        expectedBenefit: String,
        duration: AdaptationDuration = .session,
        targetConditions: [NeurodiversityType] = [],
        priority: AdaptationPriority = .medium,
        reversible: Bool = true
    ) {
        self.adaptationType = adaptationType
        self.modifications = modifications
        self.reasoning = reasoning
        self.expectedBenefit = expectedBenefit
        self.duration = duration
        self.targetConditions = targetConditions
        self.priority = priority
        self.reversible = reversible
    }
}

@available(iOS 18.0, *)
struct UIModification: Codable {
    let component: UIComponent
    let property: UIProperty
    let value: ModificationValue
    let condition: ModificationCondition?

    init(
        component: UIComponent,
        property: UIProperty,
        value: ModificationValue,
        condition: ModificationCondition? = nil
    ) {
        self.component = component
        self.property = property
        self.value = value
        self.condition = condition
    }
}

@available(iOS 18.0, *)
enum UIComponent: String, CaseIterable, Codable {
    case button
    case text
    case background
    case animation
    case transition
    case layout
    case navigation
    case input
}

@available(iOS 18.0, *)
enum UIProperty: String, CaseIterable, Codable {
    case color
    case size
    case spacing
    case duration
    case opacity
    case contrast
    case motion
    case sound
}

@available(iOS 18.0, *)
enum ModificationValue: Codable {
    case string(String)
    case double(Double)
    case bool(Bool)
    case color(String)

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let stringValue = try? container.decode(String.self) {
            self = .string(stringValue)
        } else if let doubleValue = try? container.decode(Double.self) {
            self = .double(doubleValue)
        } else if let boolValue = try? container.decode(Bool.self) {
            self = .bool(boolValue)
        } else {
            throw DecodingError.typeMismatch(ModificationValue.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Invalid ModificationValue"))
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch self {
        case .string(let value):
            try container.encode(value)
        case .double(let value):
            try container.encode(value)
        case .bool(let value):
            try container.encode(value)
        case .color(let value):
            try container.encode(value)
        }
    }
}

@available(iOS 18.0, *)
struct ModificationCondition: Codable {
    let trigger: ConditionTrigger
    let threshold: Double
    let operator: ComparisonOperator

    init(trigger: ConditionTrigger, threshold: Double, operator: ComparisonOperator) {
        self.trigger = trigger
        self.threshold = threshold
        self.operator = `operator`
    }
}

@available(iOS 18.0, *)
enum ConditionTrigger: String, CaseIterable, Codable {
    case cognitiveLoad
    case stressLevel
    case timeOfDay
    case batteryLevel
    case noiseLevel
}

@available(iOS 18.0, *)
enum ComparisonOperator: String, CaseIterable, Codable {
    case greaterThan
    case lessThan
    case equalTo
    case greaterThanOrEqual
    case lessThanOrEqual
}

@available(iOS 18.0, *)
enum AdaptationDuration: String, CaseIterable, Codable {
    case temporary
    case session
    case daily
    case persistent
}

@available(iOS 18.0, *)
enum AdaptationPriority: String, CaseIterable, Codable {
    case low
    case medium
    case high
    case critical
}
