import Foundation
import SwiftUI

// MARK: - Executive Function Insights

@available(iOS 18.0, *)
struct ExecutiveFunctionInsights: Codable, Identifiable, Equatable {
    let id: UUID
    let userId: UUID
    let generatedAt: Date
    var workingMemoryCapacity: WorkingMemoryAssessment
    var inhibitoryControl: InhibitoryControlAssessment
    var cognitiveFlexibility: CognitiveFlexibilityAssessment
    var planningAndOrganization: PlanningAssessment
    var taskInitiation: TaskInitiationAssessment
    var timeManagement: TimeManagementAssessment
    var goalDirectedPersistence: PersistenceAssessment
    var metacognition: MetacognitionAssessment
    var overallExecutiveFunctionScore: Double
    var strengths: [ExecutiveFunctionStrength]
    var challenges: [ExecutiveFunctionChallenge]
    var recommendations: [ExecutiveFunctionRecommendation]
    var progressTracking: ExecutiveFunctionProgress

    init(
        userId: UUID,
        id: UUID = UUID(),
        generatedAt: Date = Date(),
        workingMemoryCapacity: WorkingMemoryAssessment = WorkingMemoryAssessment(),
        inhibitoryControl: InhibitoryControlAssessment = InhibitoryControlAssessment(),
        cognitiveFlexibility: CognitiveFlexibilityAssessment = CognitiveFlexibilityAssessment(),
        planningAndOrganization: PlanningAssessment = PlanningAssessment(),
        taskInitiation: TaskInitiationAssessment = TaskInitiationAssessment(),
        timeManagement: TimeManagementAssessment = TimeManagementAssessment(),
        goalDirectedPersistence: PersistenceAssessment = PersistenceAssessment(),
        metacognition: MetacognitionAssessment = MetacognitionAssessment(),
        overallExecutiveFunctionScore: Double = 0.0,
        strengths: [ExecutiveFunctionStrength] = [],
        challenges: [ExecutiveFunctionChallenge] = [],
        recommendations: [ExecutiveFunctionRecommendation] = [],
        progressTracking: ExecutiveFunctionProgress = ExecutiveFunctionProgress()
    ) {
        self.id = id
        self.userId = userId
        self.generatedAt = generatedAt
        self.workingMemoryCapacity = workingMemoryCapacity
        self.inhibitoryControl = inhibitoryControl
        self.cognitiveFlexibility = cognitiveFlexibility
        self.planningAndOrganization = planningAndOrganization
        self.taskInitiation = taskInitiation
        self.timeManagement = timeManagement
        self.goalDirectedPersistence = goalDirectedPersistence
        self.metacognition = metacognition
        self.overallExecutiveFunctionScore = overallExecutiveFunctionScore
        self.strengths = strengths
        self.challenges = challenges
        self.recommendations = recommendations
        self.progressTracking = progressTracking
    }
}

// MARK: - Executive Function Assessment Types

@available(iOS 18.0, *)
struct WorkingMemoryAssessment: Codable, Equatable {
    var capacity: Double = 0.0
    var processing: Double = 0.0
    var manipulation: Double = 0.0
    var overallScore: Double = 0.0
}

@available(iOS 18.0, *)
struct InhibitoryControlAssessment: Codable, Equatable {
    var responseInhibition: Double = 0.0
    var interferenceControl: Double = 0.0
    var cognitiveInhibition: Double = 0.0
    var overallScore: Double = 0.0
}

@available(iOS 18.0, *)
struct CognitiveFlexibilityAssessment: Codable, Equatable {
    var taskSwitching: Double = 0.0
    var mentalFlexibility: Double = 0.0
    var adaptability: Double = 0.0
    var overallScore: Double = 0.0
}

@available(iOS 18.0, *)
struct PlanningAssessment: Codable, Equatable {
    var strategicPlanning: Double = 0.0
    var organization: Double = 0.0
    var prioritization: Double = 0.0
    var overallScore: Double = 0.0
}

@available(iOS 18.0, *)
struct TaskInitiationAssessment: Codable, Equatable {
    var initiationSpeed: Double = 0.0
    var motivationLevel: Double = 0.0
    var procrastinationTendency: Double = 0.0
    var overallScore: Double = 0.0
}

@available(iOS 18.0, *)
struct TimeManagementAssessment: Codable, Equatable {
    var timeEstimation: Double = 0.0
    var timeMonitoring: Double = 0.0
    var scheduleAdherence: Double = 0.0
    var overallScore: Double = 0.0
}

@available(iOS 18.0, *)
struct PersistenceAssessment: Codable, Equatable {
    var goalMaintenance: Double = 0.0
    var effortSustaining: Double = 0.0
    var distractionResistance: Double = 0.0
    var overallScore: Double = 0.0
}

@available(iOS 18.0, *)
struct MetacognitionAssessment: Codable, Equatable {
    var selfAwareness: Double = 0.0
    var selfMonitoring: Double = 0.0
    var selfRegulation: Double = 0.0
    var overallScore: Double = 0.0
}

// MARK: - Executive Function Support Types

@available(iOS 18.0, *)
struct ExecutiveFunctionStrength: Codable, Identifiable, Equatable {
    let id = UUID()
    var domain: ExecutiveFunctionDomain
    var description: String
    var score: Double
}

@available(iOS 18.0, *)
public struct ExecutiveFunctionChallenge: Codable, Identifiable, Equatable {
    public let id = UUID()
    public var domain: ExecutiveFunctionDomain
    public var description: String
    public var severity: ChallengeSeverity
    public var impact: String

    public init(domain: ExecutiveFunctionDomain, description: String, severity: ChallengeSeverity, impact: String) {
        self.domain = domain
        self.description = description
        self.severity = severity
        self.impact = impact
    }
}

@available(iOS 18.0, *)
struct ExecutiveFunctionRecommendation: Codable, Identifiable, Equatable {
    let id = UUID()
    var targetDomain: ExecutiveFunctionDomain
    var strategy: String
    var implementation: String
    var expectedOutcome: String
    var priority: RecommendationPriority
}

@available(iOS 18.0, *)
struct ExecutiveFunctionProgress: Codable, Equatable {
    var baseline = Date()
    var currentAssessment = Date()
    var improvementAreas: [ExecutiveFunctionDomain] = []
    var progressMetrics: [String: Double] = [:]
    var nextAssessmentDate = Date()
}

@available(iOS 18.0, *)
public enum ExecutiveFunctionDomain: String, Codable, CaseIterable {
    case workingMemory
    case inhibitoryControl
    case cognitiveFlexibility
    case planning
    case taskInitiation
    case timeManagement
    case persistence
    case metacognition
}

@available(iOS 18.0, *)
public enum ChallengeSeverity: String, Codable, CaseIterable {
    case mild
    case moderate
    case severe
}

@available(iOS 18.0, *)
enum RecommendationPriority: String, Codable, CaseIterable {
    case low
    case medium
    case high
    case critical
}
