import Foundation

// MARK: - Privacy Settings Models

@available(iOS 18.0, *)
struct PrivacySettings: Codable, Equatable {
    let id: UUID

    // Data Collection
    var analyticsEnabled: Bool
    var crashReportingEnabled: Bool
    var usageDataCollection: Bool
    var personalizedAdsEnabled: Bool

    // Health Data Privacy
    var healthDataSharingEnabled: Bool
    var anonymizeHealthData: Bool
    var healthDataRetentionDays: Int

    // AI and Machine Learning
    var aiPersonalizationEnabled: Bool
    var localProcessingOnly: Bool
    var shareAIInsights: Bool

    // Location and Tracking
    var locationTrackingEnabled: Bool
    var preciseLocationEnabled: Bool
    var backgroundLocationEnabled: Bool

    // Communication and Sharing
    var shareProgressWithContacts: Bool
    var allowSocialFeatures: Bool
    var publicProfileEnabled: Bool

    // Data Export and Deletion
    var dataExportEnabled: Bool
    var automaticDataDeletion: Bool
    var dataRetentionPeriod: DataRetentionPeriod

    // Security
    var biometricAuthRequired: Bool
    var appLockEnabled: Bool
    var sessionTimeout: SessionTimeout
    var encryptLocalData: Bool

    // Third-party Integrations
    var allowThirdPartyIntegrations: Bool
    var shareDataWithHealthApps: Bool
    var allowResearchParticipation: Bool

    // Timestamps
    let createdAt: Date
    var updatedAt: Date

    init(
        id: UUID = UUID(),
        analyticsEnabled: Bool = false,
        crashReportingEnabled: Bool = true,
        usageDataCollection: Bool = false,
        personalizedAdsEnabled: Bool = false,
        healthDataSharingEnabled: Bool = false,
        anonymizeHealthData: Bool = true,
        healthDataRetentionDays: Int = 365,
        aiPersonalizationEnabled: Bool = true,
        localProcessingOnly: Bool = true,
        shareAIInsights: Bool = false,
        locationTrackingEnabled: Bool = false,
        preciseLocationEnabled: Bool = false,
        backgroundLocationEnabled: Bool = false,
        shareProgressWithContacts: Bool = false,
        allowSocialFeatures: Bool = false,
        publicProfileEnabled: Bool = false,
        dataExportEnabled: Bool = true,
        automaticDataDeletion: Bool = false,
        dataRetentionPeriod: DataRetentionPeriod = .oneYear,
        biometricAuthRequired: Bool = false,
        appLockEnabled: Bool = false,
        sessionTimeout: SessionTimeout = .thirtyMinutes,
        encryptLocalData: Bool = true,
        allowThirdPartyIntegrations: Bool = false,
        shareDataWithHealthApps: Bool = false,
        allowResearchParticipation: Bool = false,
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.analyticsEnabled = analyticsEnabled
        self.crashReportingEnabled = crashReportingEnabled
        self.usageDataCollection = usageDataCollection
        self.personalizedAdsEnabled = personalizedAdsEnabled
        self.healthDataSharingEnabled = healthDataSharingEnabled
        self.anonymizeHealthData = anonymizeHealthData
        self.healthDataRetentionDays = healthDataRetentionDays
        self.aiPersonalizationEnabled = aiPersonalizationEnabled
        self.localProcessingOnly = localProcessingOnly
        self.shareAIInsights = shareAIInsights
        self.locationTrackingEnabled = locationTrackingEnabled
        self.preciseLocationEnabled = preciseLocationEnabled
        self.backgroundLocationEnabled = backgroundLocationEnabled
        self.shareProgressWithContacts = shareProgressWithContacts
        self.allowSocialFeatures = allowSocialFeatures
        self.publicProfileEnabled = publicProfileEnabled
        self.dataExportEnabled = dataExportEnabled
        self.automaticDataDeletion = automaticDataDeletion
        self.dataRetentionPeriod = dataRetentionPeriod
        self.biometricAuthRequired = biometricAuthRequired
        self.appLockEnabled = appLockEnabled
        self.sessionTimeout = sessionTimeout
        self.encryptLocalData = encryptLocalData
        self.allowThirdPartyIntegrations = allowThirdPartyIntegrations
        self.shareDataWithHealthApps = shareDataWithHealthApps
        self.allowResearchParticipation = allowResearchParticipation
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - Data Retention Period

@available(iOS 18.0, *)
enum DataRetentionPeriod: String, CaseIterable, Codable {
    case oneMonth
    case threeMonths
    case sixMonths
    case oneYear
    case twoYears
    case indefinite

    var displayName: String {
        switch self {
        case .oneMonth: return "1 Month"
        case .threeMonths: return "3 Months"
        case .sixMonths: return "6 Months"
        case .oneYear: return "1 Year"
        case .twoYears: return "2 Years"
        case .indefinite: return "Indefinite"
        }
    }

    var days: Int? {
        switch self {
        case .oneMonth: return 30
        case .threeMonths: return 90
        case .sixMonths: return 180
        case .oneYear: return 365
        case .twoYears: return 730
        case .indefinite: return nil
        }
    }

    var description: String {
        switch self {
        case .oneMonth: return "Data will be automatically deleted after 1 month"
        case .threeMonths: return "Data will be automatically deleted after 3 months"
        case .sixMonths: return "Data will be automatically deleted after 6 months"
        case .oneYear: return "Data will be automatically deleted after 1 year"
        case .twoYears: return "Data will be automatically deleted after 2 years"
        case .indefinite: return "Data will be kept indefinitely until manually deleted"
        }
    }
}

// MARK: - Session Timeout

@available(iOS 18.0, *)
enum SessionTimeout: String, CaseIterable, Codable {
    case never
    case fiveMinutes
    case fifteenMinutes
    case thirtyMinutes
    case oneHour
    case fourHours
    case immediately

    var displayName: String {
        switch self {
        case .never: return "Never"
        case .fiveMinutes: return "5 Minutes"
        case .fifteenMinutes: return "15 Minutes"
        case .thirtyMinutes: return "30 Minutes"
        case .oneHour: return "1 Hour"
        case .fourHours: return "4 Hours"
        case .immediately: return "Immediately"
        }
    }

    var timeInterval: TimeInterval? {
        switch self {
        case .never: return nil
        case .fiveMinutes: return 300
        case .fifteenMinutes: return 900
        case .thirtyMinutes: return 1_800
        case .oneHour: return 3_600
        case .fourHours: return 14_400
        case .immediately: return 0
        }
    }

    var description: String {
        switch self {
        case .never: return "App will never automatically lock"
        case .fiveMinutes: return "App will lock after 5 minutes of inactivity"
        case .fifteenMinutes: return "App will lock after 15 minutes of inactivity"
        case .thirtyMinutes: return "App will lock after 30 minutes of inactivity"
        case .oneHour: return "App will lock after 1 hour of inactivity"
        case .fourHours: return "App will lock after 4 hours of inactivity"
        case .immediately: return "App will lock immediately when backgrounded"
        }
    }
}

// MARK: - Privacy Consent

@available(iOS 18.0, *)
struct PrivacyConsent: Codable, Equatable {
    let id: UUID
    let userId: UUID

    // Consent Types
    var analyticsConsent: ConsentStatus
    var healthDataConsent: ConsentStatus
    var aiPersonalizationConsent: ConsentStatus
    var locationConsent: ConsentStatus
    var marketingConsent: ConsentStatus
    var researchConsent: ConsentStatus

    // Consent Metadata
    let consentVersion: String
    let consentDate: Date
    var lastUpdated: Date
    var ipAddress: String?
    var userAgent: String?

    init(
        userId: UUID,
        analyticsConsent: ConsentStatus = .notAsked,
        healthDataConsent: ConsentStatus = .notAsked,
        aiPersonalizationConsent: ConsentStatus = .notAsked,
        locationConsent: ConsentStatus = .notAsked,
        marketingConsent: ConsentStatus = .notAsked,
        researchConsent: ConsentStatus = .notAsked,
        consentVersion: String = "1.0",
        consentDate: Date = Date(),
        lastUpdated: Date = Date(),
        ipAddress: String? = nil,
        id: UUID = UUID(),
        userAgent: String? = nil
    ) {
        self.id = id
        self.userId = userId
        self.analyticsConsent = analyticsConsent
        self.healthDataConsent = healthDataConsent
        self.aiPersonalizationConsent = aiPersonalizationConsent
        self.locationConsent = locationConsent
        self.marketingConsent = marketingConsent
        self.researchConsent = researchConsent
        self.consentVersion = consentVersion
        self.consentDate = consentDate
        self.lastUpdated = lastUpdated
        self.ipAddress = ipAddress
        self.userAgent = userAgent
    }
}

// MARK: - Consent Status

@available(iOS 18.0, *)
enum ConsentStatus: String, CaseIterable, Codable {
    case notAsked
    case granted
    case denied
    case withdrawn

    var displayName: String {
        switch self {
        case .notAsked: return "Not Asked"
        case .granted: return "Granted"
        case .denied: return "Denied"
        case .withdrawn: return "Withdrawn"
        }
    }

    var isActive: Bool {
        self == .granted
    }
}
