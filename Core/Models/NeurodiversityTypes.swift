import Foundation
import SwiftUI

// MARK: - Enhanced Neurodiversity Types

@available(iOS 18.0, *)
enum FocusMode: String, CaseIterable, Codable {
    case normal
    case enhanced
    case hyperfocus
    case distracted
}

@available(iOS 18.0, *)
enum DistractionLevel: String, CaseIterable, Codable {
    case low
    case moderate
    case high
    case overwhelming
}

@available(iOS 18.0, *)
enum RoutineMode: String, CaseIterable, Codable {
    case flexible
    case structured
    case strict
}

@available(iOS 18.0, *)
enum SensoryOverloadRisk: String, CaseIterable, Codable {
    case low
    case moderate
    case high
    case critical
}

@available(iOS 18.0, *)
enum SocialAnxietyLevel: String, CaseIterable, Codable {
    case minimal
    case mild
    case moderate
    case severe
}

@available(iOS 18.0, *)
enum SocialContext: String, CaseIterable, Codable {
    case workplace
    case social
    case healthcare
}

@available(iOS 18.0, *)
enum MotionSensitivity: String, CaseIterable, Codable {
    case none
    case mild
    case moderate
    case severe
}

@available(iOS 18.0, *)
enum SoundSensitivity: String, CaseIterable, Codable {
    case none
    case mild
    case moderate
    case severe
}

@available(iOS 18.0, *)
struct MotionAdaptation: Codable {
    let reduceAnimations: Bool
    let disableParallax: Bool
    let limitTransitions: Bool
}

@available(iOS 18.0, *)
struct ColorAdaptation: Codable {
    let contrastRatio: Double
    let highContrastMode: Bool
    let customColors: Bool
}

@available(iOS 18.0, *)
struct SoundAdaptation: Codable {
    let volumeReduction: Double
    let disableSystemSounds: Bool
    let enableSubtitles: Bool
}

@available(iOS 18.0, *)
struct ADHDTaskAdaptation: Codable {
    let usePomodoro: Bool
    let breakTaskIntoSteps: Bool
    let addVisualCues: Bool
    let enableFidgetTools: Bool
    let reduceDistractions: Bool
    let provideDopamineRewards: Bool
}

@available(iOS 18.0, *)
struct AutismTaskAdaptation: Codable {
    let maintainRoutine: Bool
    let provideDetailedInstructions: Bool
    let allowExtraTime: Bool
    let minimizeSurprises: Bool
    let offerSensoryBreaks: Bool
    let useVisualSchedules: Bool
}

@available(iOS 18.0, *)
struct SocialSupport: Codable {
    let communicationTemplates: [String]
    let socialScripts: [String]
    let anxietyManagement: [String]
    let safetyPhrases: [String]
}

@available(iOS 18.0, *)
struct CognitiveLoadReading: Codable {
    let level: CognitiveLoadLevel
    let timestamp: Date
    let context: String
}

// MARK: - Enhanced Neurodiversity Services

@available(iOS 18.0, *)
class EnhancedCognitiveLoadService: CognitiveLoadServiceProtocol {
    private var currentLoad: CognitiveLoadLevel = .medium
    private var loadHistory: [CognitiveLoadReading] = []

    init() {}

    func getCurrentCognitiveLoad() async -> CognitiveLoadLevel { currentLoad }

    func adaptUIForCognitiveLoad(_ level: CognitiveLoadLevel) -> CognitiveAdaptation {
        switch level {
        case .low:
            return CognitiveAdaptation(adaptationType: .cognitive, modifications: ["full_features"], reasoning: "Low load allows full interface", expectedBenefit: "Enhanced productivity")
        case .medium:
            return CognitiveAdaptation(adaptationType: .cognitive, modifications: ["simplified_nav"], reasoning: "Medium load requires simplified navigation", expectedBenefit: "Reduced cognitive strain")
        case .high:
            return CognitiveAdaptation(adaptationType: .cognitive, modifications: ["minimal_ui", "reduced_colors"], reasoning: "High load needs minimal interface", expectedBenefit: "Significant cognitive relief")
        case .critical:
            return CognitiveAdaptation(adaptationType: .cognitive, modifications: ["emergency_mode", "single_task"], reasoning: "Critical load requires emergency simplification", expectedBenefit: "Prevents cognitive overload")
        }
    }

    func predictCognitiveOverload() async -> Bool {
        // Enhanced prediction based on multiple factors
        let timeOfDay = Calendar.current.component(.hour, from: Date())
        let isAfternoon = timeOfDay >= 14 && timeOfDay <= 16 // Common fatigue period
        let recentTaskCount = loadHistory.filter { Date().timeIntervalSince($0.timestamp) < 3_600 }.count

        return isAfternoon || recentTaskCount > 5 || currentLoad == .critical
    }

    func suggestCognitiveBreak() async -> BreakSuggestion? {
        if await predictCognitiveOverload() {
            return BreakSuggestion(
                type: .cognitive,
                duration: 300, // 5 minutes
                activity: "Deep breathing or gentle stretching",
                reason: "Cognitive load is elevated"
            )
        }
        return nil
    }
}

@available(iOS 18.0, *)
class EnhancedSensoryAdaptationService: SensoryAdaptationServiceProtocol {
    private var currentPreferences = SensoryPreferences()

    init() {}

    func getCurrentSensoryPreferences() -> SensoryPreferences { currentPreferences }

    func adaptForMotionSensitivity(_ level: MotionSensitivity) -> MotionAdaptation {
        switch level {
        case .none:
            return MotionAdaptation(reduceAnimations: false, disableParallax: false, limitTransitions: false)
        case .mild:
            return MotionAdaptation(reduceAnimations: true, disableParallax: false, limitTransitions: false)
        case .moderate:
            return MotionAdaptation(reduceAnimations: true, disableParallax: true, limitTransitions: true)
        case .severe:
            return MotionAdaptation(reduceAnimations: true, disableParallax: true, limitTransitions: true)
        }
    }

    func adaptForColorContrast(_ level: ColorContrast) -> ColorAdaptation {
        switch level {
        case .normal:
            return ColorAdaptation(contrastRatio: 4.5, highContrastMode: false, customColors: false)
        case .increased:
            return ColorAdaptation(contrastRatio: 7.0, highContrastMode: true, customColors: false)
        case .high:
            return ColorAdaptation(contrastRatio: 10.0, highContrastMode: true, customColors: true)
        case .maximum:
            return ColorAdaptation(contrastRatio: 15.0, highContrastMode: true, customColors: true)
        }
    }

    func adaptForSoundSensitivity(_ level: SoundSensitivity) -> SoundAdaptation {
        switch level {
        case .none:
            return SoundAdaptation(volumeReduction: 0.0, disableSystemSounds: false, enableSubtitles: false)
        case .mild:
            return SoundAdaptation(volumeReduction: 0.2, disableSystemSounds: false, enableSubtitles: true)
        case .moderate:
            return SoundAdaptation(volumeReduction: 0.5, disableSystemSounds: true, enableSubtitles: true)
        case .severe:
            return SoundAdaptation(volumeReduction: 0.8, disableSystemSounds: true, enableSubtitles: true)
        }
    }
}
