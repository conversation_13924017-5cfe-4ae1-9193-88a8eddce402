import Foundation

// MARK: - Task Models

@available(iOS 18.0, *)
struct TaskRecommendation: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let category: TaskRecommendationCategory
    let priority: RecommendationPriority
    let estimatedImpact: Double
    let implementationSteps: [String]
    let timeframe: RecommendationTimeframe

    init(
        title: String,
        description: String,
        category: TaskRecommendationCategory,
        priority: RecommendationPriority = .medium,
        estimatedImpact: Double = 0.5,
        implementationSteps: [String] = [],
        timeframe: RecommendationTimeframe = .shortTerm
    ) {
        self.title = title
        self.description = description
        self.category = category
        self.priority = priority
        self.estimatedImpact = estimatedImpact
        self.implementationSteps = implementationSteps
        self.timeframe = timeframe
    }
}

@available(iOS 18.0, *)
enum TaskRecommendationCategory: String, CaseIterable, Codable {
    case optimization
    case adaptation
    case scheduling
    case support
    case wellness
}

@available(iOS 18.0, *)
struct ScheduledTask: Identifiable, Codable {
    let id = UUID()
    let task: AITask
    let scheduledTime: Date
    let estimatedDuration: TimeInterval
    let priority: TaskPriority
    let adaptations: [CognitiveAdaptation]
    let reasoning: String

    init(
        task: AITask,
        scheduledTime: Date,
        estimatedDuration: TimeInterval,
        priority: TaskPriority,
        adaptations: [CognitiveAdaptation] = [],
        reasoning: String = ""
    ) {
        self.task = task
        self.scheduledTime = scheduledTime
        self.estimatedDuration = estimatedDuration
        self.priority = priority
        self.adaptations = adaptations
        self.reasoning = reasoning
    }
}

@available(iOS 18.0, *)
enum TaskPriority: String, CaseIterable, Codable {
    case low
    case medium
    case high
    case urgent
}

@available(iOS 18.0, *)
struct TaskPerformanceAnalysis: Codable {
    let userId: UUID
    let timeframe: BehaviorTimeframe
    let totalTasksAttempted: Int
    let totalTasksCompleted: Int
    let averageCompletionTime: TimeInterval
    let performanceTrends: [PerformanceTrend]
    let cognitiveLoadImpact: CognitiveLoadImpact
    let recommendations: [PerformanceRecommendation]

    var completionRate: Double {
        guard totalTasksAttempted > 0 else { return 0.0 }
        return Double(totalTasksCompleted) / Double(totalTasksAttempted)
    }

    init(
        userId: UUID,
        timeframe: BehaviorTimeframe,
        totalTasksAttempted: Int,
        totalTasksCompleted: Int,
        averageCompletionTime: TimeInterval,
        performanceTrends: [PerformanceTrend] = [],
        cognitiveLoadImpact: CognitiveLoadImpact = CognitiveLoadImpact(),
        recommendations: [PerformanceRecommendation] = []
    ) {
        self.userId = userId
        self.timeframe = timeframe
        self.totalTasksAttempted = totalTasksAttempted
        self.totalTasksCompleted = totalTasksCompleted
        self.averageCompletionTime = averageCompletionTime
        self.performanceTrends = performanceTrends
        self.cognitiveLoadImpact = cognitiveLoadImpact
        self.recommendations = recommendations
    }
}

@available(iOS 18.0, *)
struct PerformanceTrend: Identifiable, Codable {
    let id = UUID()
    let metric: PerformanceMetric
    let direction: TrendDirection
    let magnitude: Double
    let timeframe: TrendTimeframe
    let significance: Double

    init(
        metric: PerformanceMetric,
        direction: TrendDirection,
        magnitude: Double,
        timeframe: TrendTimeframe,
        significance: Double = 0.5
    ) {
        self.metric = metric
        self.direction = direction
        self.magnitude = magnitude
        self.timeframe = timeframe
        self.significance = significance
    }
}

@available(iOS 18.0, *)
enum PerformanceMetric: String, CaseIterable, Codable {
    case completionRate
    case averageTime
    case qualityScore
    case cognitiveEfficiency
    case userSatisfaction
}

@available(iOS 18.0, *)
struct CognitiveLoadImpact: Codable {
    let lowLoadPerformance: Double
    let moderateLoadPerformance: Double
    let highLoadPerformance: Double
    let overwhelmingLoadPerformance: Double
    let optimalLoadLevel: CognitiveLoadLevel

    init(
        lowLoadPerformance: Double = 0.0,
        moderateLoadPerformance: Double = 0.0,
        highLoadPerformance: Double = 0.0,
        overwhelmingLoadPerformance: Double = 0.0,
        optimalLoadLevel: CognitiveLoadLevel = .moderate
    ) {
        self.lowLoadPerformance = lowLoadPerformance
        self.moderateLoadPerformance = moderateLoadPerformance
        self.highLoadPerformance = highLoadPerformance
        self.overwhelmingLoadPerformance = overwhelmingLoadPerformance
        self.optimalLoadLevel = optimalLoadLevel
    }
}

@available(iOS 18.0, *)
struct PerformanceRecommendation: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let category: PerformanceCategory
    let priority: RecommendationPriority
    let expectedImprovement: Double

    init(
        title: String,
        description: String,
        category: PerformanceCategory,
        priority: RecommendationPriority = .medium,
        expectedImprovement: Double = 0.1
    ) {
        self.title = title
        self.description = description
        self.category = category
        self.priority = priority
        self.expectedImprovement = expectedImprovement
    }
}

@available(iOS 18.0, *)
enum PerformanceCategory: String, CaseIterable, Codable {
    case timeManagement
    case cognitiveOptimization
    case environmentalAdjustment
    case taskStructure
    case motivationalSupport
}
