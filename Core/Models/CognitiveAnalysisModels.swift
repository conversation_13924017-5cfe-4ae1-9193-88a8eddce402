import Foundation

// MARK: - Cognitive Analysis Models

@available(iOS 18.0, *)
struct CognitiveAnalysis: Codable {
    let patterns: [String]
    let insights: [String]
    let recommendations: [String]
    let score: Double
}

@available(iOS 18.0, *)
struct CognitivePatterns: Codable {
    let patterns: [String]
    let frequency: [String: Int]
    let trends: [String]
}

@available(iOS 18.0, *)
struct CognitiveOptimization: Codable {
    let type: String
    let description: String
    let expectedImprovement: Double
    let implementation: [String]
}

@available(iOS 18.0, *)
struct ExecutiveFunctionInsights: Codable {
    let patterns: [String]
    let recommendations: [String]
    let score: Double
}

@available(iOS 18.0, *)
struct BehaviorInsights: Codable {
    let patterns: [String]
    let trends: [String]
    let recommendations: [String]
}

@available(iOS 18.0, *)
struct MotionAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
struct ColorAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
struct SoundAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

struct BiometricTrend: Codable {
    let id: UUID
    let type: String
    let values: [Double]
    let timestamps: [Date]
    let trend: String

    init(type: String, values: [Double], timestamps: [Date], trend: String, id: UUID = UUID()) {
        self.id = id; self.type = type; self.values = values; self.timestamps = timestamps; self.trend = trend
    }

    static let stable = BiometricTrend(type: "stable", values: [], timestamps: [], trend: "stable")
}
