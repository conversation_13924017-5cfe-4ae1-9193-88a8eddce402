import Foundation

// MARK: - Cognitive Analysis Models

@available(iOS 18.0, *)
struct CognitiveAnalysis: Codable, Identifiable {
    let id: UUID
    let taskId: UUID
    let userId: UUID
    let analysisDate: Date
    let cognitiveLoadPattern: String
    let performanceMetrics: [String: Double]
    let attentionSpan: TimeInterval
    let focusQuality: Double
    let distractionLevel: Double
    let optimalWorkingConditions: [String]
    let recommendations: [String]
    let confidenceScore: Double

    // Legacy compatibility
    var patterns: [String] { [cognitiveLoadPattern] }
    var insights: [String] { optimalWorkingConditions }
    var score: Double { confidenceScore }
}

// CognitivePatterns is defined in Core/Models/CognitivePatternModels.swift

@available(iOS 18.0, *)
struct CognitiveOptimization: Codable {
    let type: String
    let description: String
    let expectedImprovement: Double
    let implementation: [String]
}

@available(iOS 18.0, *)
struct ExecutiveFunctionInsights: Codable {
    let patterns: [String]
    let recommendations: [String]
    let score: Double
}

@available(iOS 18.0, *)
struct BehaviorInsights: Codable {
    let patterns: [String]
    let trends: [String]
    let recommendations: [String]
}

@available(iOS 18.0, *)
struct MotionAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
struct ColorAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

@available(iOS 18.0, *)
struct SoundAdaptation: Codable {
    let level: String
    let modifications: [String]
    let reasoning: String
    let expectedBenefit: String
}

struct BiometricTrend: Codable {
    let id: UUID
    let type: String
    let values: [Double]
    let timestamps: [Date]
    let trend: String

    init(type: String, values: [Double], timestamps: [Date], trend: String, id: UUID = UUID()) {
        self.id = id; self.type = type; self.values = values; self.timestamps = timestamps; self.trend = trend
    }

    static let stable = BiometricTrend(type: "stable", values: [], timestamps: [], trend: "stable")
}
