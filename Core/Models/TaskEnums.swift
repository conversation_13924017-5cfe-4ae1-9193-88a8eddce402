import Foundation
import SwiftUI

// MARK: - Task-Related Enums

/// Task priority levels
public enum TaskPriority: String, CaseIterable, Codable, Sendable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case urgent = "Urgent"

    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .urgent: return .red
        }
    }

    var accessibilityLabel: String {
        switch self {
        case .low: return "Low priority task"
        case .medium: return "Medium priority task"
        case .high: return "High priority task"
        case .urgent: return "Urgent priority task"
        }
    }
}

public enum TaskBreakdownLevel: String, CaseIterable, Codable, Sendable {
    case minimal
    case standard
    case detailed
    case extensive = "Extensive"

    var maxSubtasks: Int {
        switch self {
        case .minimal: return 3
        case .standard: return 6
        case .detailed: return 9
        case .extensive: return 12
        }
    }
}

public enum FocusRequirement: String, CaseIterable, Codable, Sendable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case intense = "Intense"

    var description: String {
        switch self {
        case .low: return "Minimal focus required"
        case .medium: return "Moderate focus required"
        case .high: return "High focus required"
        case .intense: return "Intense focus required"
        }
    }
}

/// Task completion methods
enum CompletionMethod: String, CaseIterable, Codable {
    case manual
    case automatic
    case partial
    case assisted
}

/// Reminder types
public enum ReminderType: String, CaseIterable, Codable, Sendable {
    case gentle
    case standard
    case persistent
    case emergency
}

/// Cognitive load levels for adaptive UI
public enum CognitiveLoadLevel: String, CaseIterable, Codable, Sendable {
    case low
    case medium
    case high
    case overload

    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .overload: return .red
        }
    }

    var maxSimultaneousTasks: Int {
        switch self {
        case .low: return 5
        case .medium: return 3
        case .high: return 2
        case .overload: return 1
        }
    }

    var taskDurationLimit: TimeInterval {
        switch self {
        case .low: return 3_600 // 1 hour
        case .medium: return 1_800 // 30 minutes
        case .high: return 900 // 15 minutes
        case .overload: return 300 // 5 minutes
        }
    }

    var uiComplexity: UIComplexity {
        switch self {
        case .low: return .standard
        case .medium: return .simplified
        case .high: return .minimal
        case .overload: return .essential
        }
    }

    var animationSpeed: Double {
        switch self {
        case .low: return 1.0
        case .medium: return 0.8
        case .high: return 0.6
        case .overload: return 0.3
        }
    }

    var textSize: TextSize {
        switch self {
        case .low: return .standard
        case .medium: return .large
        case .high: return .extraLarge
        case .overload: return .accessibility
        }
    }

    var spacing: SpacingLevel {
        switch self {
        case .low: return .compact
        case .medium: return .standard
        case .high: return .comfortable
        case .overload: return .spacious
        }
    }

    var descriptionLineLimit: Int {
        switch self {
        case .low: return 5
        case .medium: return 3
        case .high: return 2
        case .overload: return 1
        }
    }
}

/// UI complexity levels
enum UIComplexity: String, CaseIterable, Codable {
    case essential
    case minimal
    case simplified
    case standard
    case detailed

    var maxElements: Int {
        switch self {
        case .essential: return 3
        case .minimal: return 5
        case .simplified: return 8
        case .standard: return 12
        case .detailed: return 20
        }
    }
}

/// Text size levels
enum TextSize: String, CaseIterable, Codable {
    case small
    case standard
    case large
    case extraLarge = "Extra Large"
    case accessibility

    var scaleFactor: CGFloat {
        switch self {
        case .small: return 0.8
        case .standard: return 1.0
        case .large: return 1.2
        case .extraLarge: return 1.5
        case .accessibility: return 2.0
        }
    }
}

/// Spacing levels
enum SpacingLevel: String, CaseIterable, Codable {
    case compact
    case standard
    case comfortable
    case spacious

    var multiplier: CGFloat {
        switch self {
        case .compact: return 0.5
        case .standard: return 1.0
        case .comfortable: return 1.5
        case .spacious: return 2.0
        }
    }
}

/// Task categories for organization and analysis
public enum TaskCategory: String, CaseIterable, Codable, Sendable {
    case focus
    case creative
    case administrative
    case learning
    case communication
    case planning
    case maintenance
    case wellness
    case social
    case technical

    var displayName: String { rawValue }

    var cognitiveLoadEstimate: CognitiveLoadLevel {
        switch self {
        case .focus, .technical, .learning: return .high
        case .creative, .planning, .communication: return .medium
        case .administrative, .maintenance, .social: return .low
        case .wellness: return .low
        }
    }

    var recommendedDuration: TimeInterval {
        switch self {
        case .focus, .technical: return 1_800 // 30 minutes
        case .creative, .learning: return 2_700 // 45 minutes
        case .planning, .communication: return 1_200 // 20 minutes
        case .administrative, .maintenance: return 900 // 15 minutes
        case .wellness, .social: return 600 // 10 minutes
        }
    }
}
