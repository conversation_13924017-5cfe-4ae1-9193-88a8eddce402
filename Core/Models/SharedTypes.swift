import Foundation
import HealthKit

// MARK: - Shared Types for Cross-Module Dependencies
// This file contains type definitions that need to be accessible across multiple modules
// to resolve compilation dependencies without circular imports.

// MARK: - Behavior Analysis Types

@available(iOS 18.0, *)
public enum BehaviorTimeframe: String, CaseIterable, Codable {
    case hour
    case day
    case week
    case month
    case quarter
    case year
}

// MARK: - Health Tracking Types

@available(iOS 18.0, *)
public struct MoodReading: Identifiable, Codable {
    public let id = UUID()
    public let timestamp: Date
    public let moodLevel: MoodLevel
    public let energyLevel: EnergyLevel
    public let stressLevel: StressLevel
    public let notes: String?
    public let context: String?

    public init(
        moodLevel: MoodLevel,
        timestamp: Date = Date(),
        energyLevel: EnergyLevel = .moderate,
        stressLevel: StressLevel = .low,
        notes: String? = nil,
        context: String? = nil
    ) {
        self.timestamp = timestamp
        self.moodLevel = moodLevel
        self.energyLevel = energyLevel
        self.stressLevel = stressLevel
        self.notes = notes
        self.context = context
    }
}

// MARK: - Breathing Exercise Types

@available(iOS 18.0, *)
public struct BreathingExercise: Codable, Identifiable, Equatable {
    public let id: UUID
    public var name: String
    public var description: String
    public var type: BreathingExerciseType
    public var duration: TimeInterval
    public var inhalePattern: BreathingPattern
    public var exhalePattern: BreathingPattern
    public var holdPattern: BreathingPattern?
    public var difficulty: ExerciseDifficulty
    public var benefits: [String]
    public var instructions: [String]
    public var isCustom: Bool
    public var createdBy: UUID?
    public var usageCount: Int
    public var averageRating: Double
    public var tags: [String]
    public var neurodiversitySupport: [NeurodiversityType]

    public init(
        name: String,
        description: String,
        type: BreathingExerciseType,
        duration: TimeInterval,
        inhalePattern: BreathingPattern,
        exhalePattern: BreathingPattern,
        id: UUID = UUID(),
        holdPattern: BreathingPattern? = nil,
        difficulty: ExerciseDifficulty = .beginner,
        benefits: [String] = [],
        instructions: [String] = [],
        isCustom: Bool = false,
        createdBy: UUID? = nil,
        usageCount: Int = 0,
        averageRating: Double = 0.0,
        tags: [String] = [],
        neurodiversitySupport: [NeurodiversityType] = []
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.type = type
        self.duration = duration
        self.inhalePattern = inhalePattern
        self.exhalePattern = exhalePattern
        self.holdPattern = holdPattern
        self.difficulty = difficulty
        self.benefits = benefits
        self.instructions = instructions
        self.isCustom = isCustom
        self.createdBy = createdBy
        self.usageCount = usageCount
        self.averageRating = averageRating
        self.tags = tags
        self.neurodiversitySupport = neurodiversitySupport
    }
}

@available(iOS 18.0, *)
public enum BreathingExerciseType: String, Codable, CaseIterable {
    case boxBreathing = "box_breathing"
    case deepBreathing = "deep_breathing"
    case fourSevenEight = "4_7_8_breathing"
    case alternateNostril = "alternate_nostril"
    case bellowsBreath = "bellows_breath"
    case coherentBreathing = "coherent_breathing"
    case triangleBreathing = "triangle_breathing"
    case customPattern = "custom_pattern"
}
