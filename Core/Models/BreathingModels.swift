import Foundation
import HealthKit

// MARK: - Type Aliases for Build Success
@available(iOS 18.0, *)
public typealias BreathingExercise = BreathingPattern

@available(iOS 18.0, *)
public typealias BreathingExerciseType = BreathingDifficulty

// MARK: - Breathing Phase Models

@available(iOS 18.0, *)
public enum BreathingPhase: String, CaseIterable, Codable, Sendable {
    case preparation
    case inhale
    case hold
    case exhale
    case pause
    case complete

    var displayName: String {
        switch self {
        case .preparation: return "Prepare"
        case .inhale: return "Breathe In"
        case .hold: return "Hold"
        case .exhale: return "Breathe Out"
        case .pause: return "Pause"
        case .complete: return "Complete"
        }
    }

    var instruction: String {
        switch self {
        case .preparation: return "Get ready to begin your breathing exercise"
        case .inhale: return "Slowly breathe in through your nose"
        case .hold: return "Hold your breath gently"
        case .exhale: return "Slowly breathe out through your mouth"
        case .pause: return "Rest naturally"
        case .complete: return "Well done! Your session is complete"
        }
    }
}

// MARK: - Breathing Pattern Models

@available(iOS 18.0, *)
public struct BreathingPattern: Identifiable, Codable, Hashable, Sendable {
    public let id = UUID()
    public let name: String
    public let description: String
    public let inhaleCount: Int
    public let holdCount: Int
    public let exhaleCount: Int
    public let pauseCount: Int
    public let cycles: Int
    public let difficulty: BreathingDifficulty
    public let neurodiversitySupport: [NeurodiversityType]

    static let boxBreathing = BreathingPattern(
        name: "Box Breathing",
        description: "Equal counts for inhale, hold, exhale, and pause",
        inhaleCount: 4,
        holdCount: 4,
        exhaleCount: 4,
        pauseCount: 4,
        cycles: 8,
        difficulty: .beginner,
        neurodiversitySupport: [.adhd, .anxiety]
    )

    static let deepBreathing = BreathingPattern(
        name: "Deep Breathing",
        description: "Longer exhale for relaxation",
        inhaleCount: 4,
        holdCount: 2,
        exhaleCount: 6,
        pauseCount: 2,
        cycles: 6,
        difficulty: .beginner,
        neurodiversitySupport: [.anxiety, .autism]
    )

    static let energizingBreath = BreathingPattern(
        name: "Energizing Breath",
        description: "Quick breathing to increase alertness",
        inhaleCount: 2,
        holdCount: 1,
        exhaleCount: 2,
        pauseCount: 1,
        cycles: 12,
        difficulty: .intermediate,
        neurodiversitySupport: [.adhd]
    )

    static let defaultPatterns: [BreathingPattern] = [
        .boxBreathing,
        .deepBreathing,
        .energizingBreath
    ]
}

// MARK: - Extension for BreathingExercise compatibility
@available(iOS 18.0, *)
extension BreathingPattern {
    public var duration: TimeInterval {
        let cycleTime = Double(inhaleCount + holdCount + exhaleCount + pauseCount)
        return cycleTime * Double(cycles)
    }

    public var benefits: [String] {
        switch difficulty {
        case .beginner:
            return ["Reduces stress", "Improves focus", "Easy to learn"]
        case .intermediate:
            return ["Enhanced relaxation", "Better emotional regulation", "Improved concentration"]
        case .advanced:
            return ["Deep meditation state", "Advanced stress management", "Enhanced mindfulness"]
        }
    }
}

// MARK: - Biometric Models

@available(iOS 18.0, *)
public enum BiometricTrend: String, CaseIterable, Codable {
    case improving
    case stable
    case declining
    case unknown

    var displayName: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Needs Attention"
        case .unknown: return "Unknown"
        }
    }

    var color: String {
        switch self {
        case .improving: return "green"
        case .stable: return "blue"
        case .declining: return "orange"
        case .unknown: return "gray"
        }
    }
}

@available(iOS 18.0, *)
public struct HRVReading: Identifiable, Codable, Sendable {
    public let id = UUID()
    let timestamp: Date
    let value: Double
    let quality: HRVQuality
    let context: String?

    init(value: Double, timestamp: Date = Date(), quality: HRVQuality = .good, context: String? = nil) {
        self.timestamp = timestamp
        self.value = value
        self.quality = quality
        self.context = context
    }
}

@available(iOS 18.0, *)
public enum HRVQuality: String, CaseIterable, Codable, Sendable {
    case excellent
    case good
    case fair
    case poor

    var threshold: Double {
        switch self {
        case .excellent: return 50.0
        case .good: return 30.0
        case .fair: return 20.0
        case .poor: return 0.0
        }
    }
}

// MARK: - Anxiety Detection Models

@available(iOS 18.0, *)
public struct AnxietyOverwhelmDetection: Identifiable, Codable {
    public let id = UUID()
    let timestamp: Date
    let overwhelmLevel: OverwhelmLevel
    let anxietyScore: Double
    let triggers: [AnxietyTrigger]
    let recommendations: [AnxietyRecommendation]
    let confidence: Double

    init(
        overwhelmLevel: OverwhelmLevel,
        anxietyScore: Double,
        timestamp: Date = Date(),
        triggers: [AnxietyTrigger] = [],
        recommendations: [AnxietyRecommendation] = [],
        confidence: Double = 0.8
    ) {
        self.timestamp = timestamp
        self.overwhelmLevel = overwhelmLevel
        self.anxietyScore = anxietyScore
        self.triggers = triggers
        self.recommendations = recommendations
        self.confidence = confidence
    }
}

@available(iOS 18.0, *)
public enum OverwhelmLevel: String, CaseIterable, Codable {
    case none
    case mild
    case moderate
    case high
    case severe

    var displayName: String {
        switch self {
        case .none: return "Calm"
        case .mild: return "Slightly Overwhelmed"
        case .moderate: return "Moderately Overwhelmed"
        case .high: return "Highly Overwhelmed"
        case .severe: return "Severely Overwhelmed"
        }
    }

    var color: String {
        switch self {
        case .none: return "green"
        case .mild: return "yellow"
        case .moderate: return "orange"
        case .high: return "red"
        case .severe: return "purple"
        }
    }
}

@available(iOS 18.0, *)
public struct AnxietyTrigger: Identifiable, Codable {
    public let id = UUID()
    let type: TriggerType
    let description: String
    let intensity: Double
    let timestamp: Date

    init(type: TriggerType, description: String, intensity: Double, timestamp: Date = Date()) {
        self.type = type
        self.description = description
        self.intensity = intensity
        self.timestamp = timestamp
    }
}

@available(iOS 18.0, *)
public enum TriggerType: String, CaseIterable, Codable {
    case environmental
    case social
    case cognitive
    case physical
    case sensory
    case temporal
}

@available(iOS 18.0, *)
public enum RecommendationType: String, CaseIterable, Codable {
    case breathing
    case grounding
    case movement
    case cognitive
    case environmental
    case social
}

@available(iOS 18.0, *)
public struct AnxietyRecommendation: Identifiable, Codable {
    public let id = UUID()
    let type: RecommendationType
    let title: String
    let description: String
    let priority: RecommendationPriority
    let estimatedDuration: TimeInterval

    init(
        type: RecommendationType,
        title: String,
        description: String,
        priority: RecommendationPriority = .medium,
        estimatedDuration: TimeInterval = 300
    ) {
        self.type = type
        self.title = title
        self.description = description
        self.priority = priority
        self.estimatedDuration = estimatedDuration
    }
}

@available(iOS 18.0, *)
public enum RecommendationPriority: String, CaseIterable, Codable {
    case low
    case medium
    case high
    case urgent
}

// MARK: - Breathing Session Models

@available(iOS 18.0, *)
public struct BreathingSession: Identifiable, Codable, Sendable {
    public let id: UUID
    public let exerciseId: UUID
    public let startTime: Date
    public var endTime: Date?
    public let targetCycles: Int
    public let pattern: BreathingPattern
    public var currentCycle: Int
    public var currentPhase: BreathingPhase
    public var isActive: Bool
    public var isPaused: Bool
    public var pausedAt: Date? // Claude: Adding missing pausedAt property
    public var heartRateReadings: [HeartRateReading]
    public var hrvReadings: [HRVReading]
    public var userProfile: UserProfile?

    // MARK: - Codable Implementation

    private enum CodingKeys: String, CodingKey {
        case id, exerciseId, startTime, endTime, targetCycles, pattern
        case currentCycle, currentPhase, isActive, isPaused, pausedAt
        case heartRateReadings, hrvReadings
        // userProfile is excluded from Codable since UserProfile is not Codable
    }

    public var duration: TimeInterval {
        if let endTime = endTime {
            return endTime.timeIntervalSince(startTime)
        }
        return Date().timeIntervalSince(startTime)
    }

    public var completionRate: Double {
        Double(currentCycle) / Double(targetCycles)
    }

    public init(
        exerciseId: UUID,
        targetCycles: Int,
        pattern: BreathingPattern,
        id: UUID = UUID(),
        startTime: Date = Date(),
        userProfile: UserProfile? = nil
    ) {
        self.id = id
        self.exerciseId = exerciseId
        self.startTime = startTime
        self.endTime = nil
        self.targetCycles = targetCycles
        self.pattern = pattern
        self.currentCycle = 0
        self.currentPhase = .preparation
        self.isActive = false
        self.isPaused = false
        self.pausedAt = nil // Claude: Initialize pausedAt property
        self.heartRateReadings = []
        self.hrvReadings = []
        self.userProfile = userProfile
    }

    // Convenience initializer for compatibility - temporarily disabled
    // public init(exercise: BreathingExercise, id: UUID = UUID(), startTime: Date = Date()) {
    //     self.id = id
    //     self.exerciseId = exercise.id
    //     self.startTime = startTime
    //     self.endTime = nil
    //     self.targetCycles = 8 // Default cycles
    //     self.pattern = exercise.inhalePattern // Use inhale pattern as primary pattern
    //     self.currentCycle = 0
    //     self.currentPhase = .preparation
    //     self.isActive = false
    //     self.isPaused = false
    //     self.pausedAt = nil // Claude: Initialize pausedAt property
    //     self.heartRateReadings = []
    //     self.hrvReadings = []
    //     self.userProfile = nil
    // }
}

// MARK: - Breathing Session Result Models

@available(iOS 18.0, *)
public struct BreathingSessionResult: Identifiable, Codable, Sendable {
    public let id = UUID()
    let sessionId: UUID
    let startTime: Date
    let endTime: Date
    let duration: TimeInterval
    let pattern: BreathingPattern
    let completedCycles: Int
    let targetCycles: Int
    let averageHeartRate: Double?
    let hrvData: [HRVReading]
    let stressReduction: Double
    let anxietyImprovement: Double
    let userRating: Int?
    let notes: String?

    var completionRate: Double {
        Double(completedCycles) / Double(targetCycles)
    }

    var wasSuccessful: Bool {
        completionRate >= 0.7 // 70% completion considered successful
    }

    init(
        sessionId: UUID,
        startTime: Date,
        endTime: Date,
        pattern: BreathingPattern,
        completedCycles: Int,
        targetCycles: Int,
        averageHeartRate: Double? = nil,
        hrvData: [HRVReading] = [],
        stressReduction: Double = 0.0,
        anxietyImprovement: Double = 0.0,
        userRating: Int? = nil,
        notes: String? = nil
    ) {
        self.sessionId = sessionId
        self.startTime = startTime
        self.endTime = endTime
        self.duration = endTime.timeIntervalSince(startTime)
        self.pattern = pattern
        self.completedCycles = completedCycles
        self.targetCycles = targetCycles
        self.averageHeartRate = averageHeartRate
        self.hrvData = hrvData
        self.stressReduction = stressReduction
        self.anxietyImprovement = anxietyImprovement
        self.userRating = userRating
        self.notes = notes
    }
}

// MARK: - Calming Technique Models

@available(iOS 18.0, *)
public struct CalmingTechnique: Identifiable, Codable, Sendable {
    public let id = UUID()
    let name: String
    let description: String
    let instructions: [String]
    let duration: TimeInterval
    let difficulty: BreathingDifficulty
    let neurodiversitySupport: [NeurodiversityType]
    let category: CalmingCategory

    static let defaultTechniques: [CalmingTechnique] = [
        CalmingTechnique(
            name: "5-4-3-2-1 Grounding",
            description: "Use your senses to ground yourself",
            instructions: [
                "Name 5 things you can see",
                "Name 4 things you can touch",
                "Name 3 things you can hear",
                "Name 2 things you can smell",
                "Name 1 thing you can taste"
            ],
            duration: 300,
            difficulty: .beginner,
            neurodiversitySupport: [.anxiety, .autism],
            category: .grounding
        )
    ]
}

@available(iOS 18.0, *)
public enum CalmingCategory: String, CaseIterable, Codable, Sendable {
    case breathing
    case grounding
    case movement
    case visualization
    case mindfulness
}

@available(iOS 18.0, *)
public enum BreathingDifficulty: String, CaseIterable, Codable, Sendable {
    case beginner
    case intermediate
    case advanced

    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
}
