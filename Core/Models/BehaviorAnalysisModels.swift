import Foundation

// Import shared types for cross-module dependencies

// MARK: - User Behavior Analysis Models

@available(iOS 18.0, *)
struct UserBehaviorData: Codable {
    let userId: UUID
    let timeframe: BehaviorTimeframe
    let interactions: [UserInteraction]
    let taskCompletions: [TaskCompletion]
    let sessionData: [AppSession]
    let preferences: [PreferenceChange]
    let cognitiveMetrics: [TimestampedCognitiveMetric]

    init(
        userId: UUID,
        timeframe: BehaviorTimeframe,
        interactions: [UserInteraction] = [],
        taskCompletions: [TaskCompletion] = [],
        sessionData: [AppSession] = [],
        preferences: [PreferenceChange] = [],
        cognitiveMetrics: [TimestampedCognitiveMetric] = []
    ) {
        self.userId = userId
        self.timeframe = timeframe
        self.interactions = interactions
        self.taskCompletions = taskCompletions
        self.sessionData = sessionData
        self.preferences = preferences
        self.cognitiveMetrics = cognitiveMetrics
    }
}

// Note: BehaviorTimeframe is now defined in Core/Models/SharedTypes.swift

@available(iOS 18.0, *)
struct UserInteraction: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let type: InteractionType
    let screen: String
    let element: String?
    let duration: TimeInterval
    let context: InteractionContext

    init(
        type: InteractionType,
        screen: String,
        timestamp: Date = Date(),
        element: String? = nil,
        duration: TimeInterval = 0,
        context: InteractionContext = InteractionContext()
    ) {
        self.timestamp = timestamp
        self.type = type
        self.screen = screen
        self.element = element
        self.duration = duration
        self.context = context
    }
}

@available(iOS 18.0, *)
enum InteractionType: String, CaseIterable, Codable {
    case tap
    case swipe
    case scroll
    case longPress
    case textInput
    case voiceInput
    case navigation
    case backgrounding
    case foregrounding
}

@available(iOS 18.0, *)
struct InteractionContext: Codable {
    let cognitiveLoad: CognitiveLoadLevel
    let timeOfDay: TimeOfDay
    let sessionDuration: TimeInterval
    let previousAction: String?

    init(
        cognitiveLoad: CognitiveLoadLevel = .moderate,
        timeOfDay: TimeOfDay = .morning,
        sessionDuration: TimeInterval = 0,
        previousAction: String? = nil
    ) {
        self.cognitiveLoad = cognitiveLoad
        self.timeOfDay = timeOfDay
        self.sessionDuration = sessionDuration
        self.previousAction = previousAction
    }
}

@available(iOS 18.0, *)
struct AppSession: Identifiable, Codable {
    let id = UUID()
    let startTime: Date
    let endTime: Date?
    let duration: TimeInterval
    let screensVisited: [String]
    let tasksCompleted: Int
    let cognitiveLoadChanges: [CognitiveLoadChange]
    let exitReason: SessionExitReason?

    init(
        startTime: Date = Date(),
        endTime: Date? = nil,
        screensVisited: [String] = [],
        tasksCompleted: Int = 0,
        cognitiveLoadChanges: [CognitiveLoadChange] = [],
        exitReason: SessionExitReason? = nil
    ) {
        self.startTime = startTime
        self.endTime = endTime
        self.duration = endTime?.timeIntervalSince(startTime) ?? 0
        self.screensVisited = screensVisited
        self.tasksCompleted = tasksCompleted
        self.cognitiveLoadChanges = cognitiveLoadChanges
        self.exitReason = exitReason
    }
}

@available(iOS 18.0, *)
enum SessionExitReason: String, CaseIterable, Codable {
    case taskCompleted
    case overwhelmed
    case distracted
    case timeUp
    case interruption
    case userChoice
    case appCrash
}

@available(iOS 18.0, *)
struct CognitiveLoadChange: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let fromLevel: CognitiveLoadLevel
    let toLevel: CognitiveLoadLevel
    let trigger: LoadChangeTrigger
    let context: String?

    init(
        fromLevel: CognitiveLoadLevel,
        toLevel: CognitiveLoadLevel,
        trigger: LoadChangeTrigger,
        timestamp: Date = Date(),
        context: String? = nil
    ) {
        self.timestamp = timestamp
        self.fromLevel = fromLevel
        self.toLevel = toLevel
        self.trigger = trigger
        self.context = context
    }
}

@available(iOS 18.0, *)
enum LoadChangeTrigger: String, CaseIterable, Codable {
    case taskStart
    case taskCompletion
    case interruption
    case break
    case environmentalChange
    case timeOfDay
    case userInput
}

@available(iOS 18.0, *)
struct PreferenceChange: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let category: PreferenceCategory
    let key: String
    let oldValue: String?
    let newValue: String
    let reason: ChangeReason?

    init(
        category: PreferenceCategory,
        key: String,
        newValue: String,
        timestamp: Date = Date(),
        oldValue: String? = nil,
        reason: ChangeReason? = nil
    ) {
        self.timestamp = timestamp
        self.category = category
        self.key = key
        self.oldValue = oldValue
        self.newValue = newValue
        self.reason = reason
    }
}

@available(iOS 18.0, *)
enum PreferenceCategory: String, CaseIterable, Codable {
    case accessibility
    case cognitive
    case sensory
    case interface
    case notification
    case privacy
}

@available(iOS 18.0, *)
enum ChangeReason: String, CaseIterable, Codable {
    case userInitiated
    case systemSuggested
    case adaptiveChange
    case resetToDefault
}

@available(iOS 18.0, *)
struct TimestampedCognitiveMetric: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let metrics: CognitiveMetrics
    let context: CognitiveContext

    init(metrics: CognitiveMetrics, timestamp: Date = Date(), context: CognitiveContext = CognitiveContext()) {
        self.timestamp = timestamp
        self.metrics = metrics
        self.context = context
    }
}

@available(iOS 18.0, *)
struct CognitiveContext: Codable {
    let taskType: String?
    let environmentalFactors: [String]
    let timeOfDay: TimeOfDay
    let sessionDuration: TimeInterval

    init(
        taskType: String? = nil,
        environmentalFactors: [String] = [],
        timeOfDay: TimeOfDay = .morning,
        sessionDuration: TimeInterval = 0
    ) {
        self.taskType = taskType
        self.environmentalFactors = environmentalFactors
        self.timeOfDay = timeOfDay
        self.sessionDuration = sessionDuration
    }
}
