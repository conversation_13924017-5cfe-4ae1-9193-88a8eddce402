import Foundation

// MARK: - Behavior Insights Models

@available(iOS 18.0, *)
struct BehaviorInsights: Codable {
    let userId: UUID
    let analysisDate: Date
    let timeframe: BehaviorTimeframe
    let patterns: [BehaviorPattern]
    let trends: [BehaviorTrend]
    let anomalies: [BehaviorAnomaly]
    let recommendations: [BehaviorRecommendation]
    let confidence: Double

    init(
        userId: UUID,
        timeframe: BehaviorTimeframe,
        analysisDate: Date = Date(),
        patterns: [BehaviorPattern] = [],
        trends: [BehaviorTrend] = [],
        anomalies: [BehaviorAnomaly] = [],
        recommendations: [BehaviorRecommendation] = [],
        confidence: Double = 0.8
    ) {
        self.userId = userId
        self.analysisDate = analysisDate
        self.timeframe = timeframe
        self.patterns = patterns
        self.trends = trends
        self.anomalies = anomalies
        self.recommendations = recommendations
        self.confidence = confidence
    }
}

@available(iOS 18.0, *)
struct BehaviorPattern: Identifiable, Codable {
    let id = UUID()
    let type: BehaviorPatternType
    let description: String
    let frequency: PatternFrequency
    let strength: Double
    let impact: BehaviorImpact
    let triggers: [PatternTrigger]

    init(
        type: BehaviorPatternType,
        description: String,
        frequency: PatternFrequency,
        strength: Double,
        impact: BehaviorImpact,
        triggers: [PatternTrigger] = []
    ) {
        self.type = type
        self.description = description
        self.frequency = frequency
        self.strength = strength
        self.impact = impact
        self.triggers = triggers
    }
}

@available(iOS 18.0, *)
enum BehaviorPatternType: String, CaseIterable, Codable {
    case usageTime
    case taskPreference
    case cognitiveLoadResponse
    case breakTaking
    case adaptationUsage
    case navigationPath
}

@available(iOS 18.0, *)
enum PatternFrequency: String, CaseIterable, Codable {
    case rare
    case occasional
    case regular
    case frequent
    case constant
}

@available(iOS 18.0, *)
enum BehaviorImpact: String, CaseIterable, Codable {
    case veryPositive
    case positive
    case neutral
    case negative
    case veryNegative
}

@available(iOS 18.0, *)
struct PatternTrigger: Identifiable, Codable {
    let id = UUID()
    let type: TriggerType
    let value: String
    let correlation: Double

    init(type: TriggerType, value: String, correlation: Double) {
        self.type = type
        self.value = value
        self.correlation = correlation
    }
}

@available(iOS 18.0, *)
struct BehaviorTrend: Identifiable, Codable {
    let id = UUID()
    let metric: BehaviorMetric
    let direction: TrendDirection
    let magnitude: Double
    let timeframe: TrendTimeframe
    let significance: TrendSignificance

    init(
        metric: BehaviorMetric,
        direction: TrendDirection,
        magnitude: Double,
        timeframe: TrendTimeframe,
        significance: TrendSignificance
    ) {
        self.metric = metric
        self.direction = direction
        self.magnitude = magnitude
        self.timeframe = timeframe
        self.significance = significance
    }
}

@available(iOS 18.0, *)
enum BehaviorMetric: String, CaseIterable, Codable {
    case sessionDuration
    case taskCompletionRate
    case cognitiveLoadStability
    case adaptationUsage
    case breakFrequency
    case userSatisfaction
}

@available(iOS 18.0, *)
enum TrendSignificance: String, CaseIterable, Codable {
    case negligible
    case minor
    case moderate
    case significant
    case major
}

@available(iOS 18.0, *)
struct BehaviorAnomaly: Identifiable, Codable {
    let id = UUID()
    let type: AnomalyType
    let description: String
    let severity: AnomalySeverity
    let timestamp: Date
    let context: AnomalyContext
    let possibleCauses: [String]

    init(
        type: AnomalyType,
        description: String,
        severity: AnomalySeverity,
        context: AnomalyContext,
        timestamp: Date = Date(),
        possibleCauses: [String] = []
    ) {
        self.type = type
        self.description = description
        self.severity = severity
        self.timestamp = timestamp
        self.context = context
        self.possibleCauses = possibleCauses
    }
}

@available(iOS 18.0, *)
enum AnomalyType: String, CaseIterable, Codable {
    case unusualUsagePattern
    case cognitiveLoadSpike
    case taskAbandonmentIncrease
    case adaptationOveruse
    case performanceDecline
}

@available(iOS 18.0, *)
enum AnomalySeverity: String, CaseIterable, Codable {
    case low
    case medium
    case high
    case critical
}

@available(iOS 18.0, *)
struct AnomalyContext: Codable {
    let timeOfDay: TimeOfDay
    let dayOfWeek: DayOfWeek
    let cognitiveLoad: CognitiveLoadLevel
    let environmentalFactors: [String]
    let recentChanges: [String]

    init(
        timeOfDay: TimeOfDay = .morning,
        dayOfWeek: DayOfWeek = .monday,
        cognitiveLoad: CognitiveLoadLevel = .moderate,
        environmentalFactors: [String] = [],
        recentChanges: [String] = []
    ) {
        self.timeOfDay = timeOfDay
        self.dayOfWeek = dayOfWeek
        self.cognitiveLoad = cognitiveLoad
        self.environmentalFactors = environmentalFactors
        self.recentChanges = recentChanges
    }
}

@available(iOS 18.0, *)
struct BehaviorRecommendation: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let category: RecommendationCategory
    let priority: RecommendationPriority
    let expectedImpact: Double
    let implementationDifficulty: ImplementationDifficulty
    let timeframe: RecommendationTimeframe

    init(
        title: String,
        description: String,
        category: RecommendationCategory,
        priority: RecommendationPriority = .medium,
        expectedImpact: Double = 0.5,
        implementationDifficulty: ImplementationDifficulty = .medium,
        timeframe: RecommendationTimeframe = .shortTerm
    ) {
        self.title = title
        self.description = description
        self.category = category
        self.priority = priority
        self.expectedImpact = expectedImpact
        self.implementationDifficulty = implementationDifficulty
        self.timeframe = timeframe
    }
}

@available(iOS 18.0, *)
enum RecommendationCategory: String, CaseIterable, Codable {
    case usageOptimization
    case cognitiveSupport
    case adaptationAdjustment
    case scheduleOptimization
    case environmentalModification
}

@available(iOS 18.0, *)
enum RecommendationTimeframe: String, CaseIterable, Codable {
    case immediate
    case shortTerm
    case mediumTerm
    case longTerm
}
