import Foundation
import SwiftUI

// MARK: - Cognitive Patterns

@available(iOS 18.0, *)
struct CognitivePatterns: Codable, Identifiable, Equatable {
    let id: UUID
    let userId: UUID
    let analysisDate: Date
    var timeOfDayPatterns: TimeOfDayPatterns
    var taskTypePatterns: TaskTypePatterns
    var cognitiveLoadPatterns: [CognitiveLoadPattern]
    var productivityRhythms: ProductivityRhythms
    var breakPatterns: BreakPatterns
    var focusPatterns: FocusPatterns
    var distractionTriggers: [String]
    var optimalConditions: [String]
    var seasonalPatterns: SeasonalPatterns
    var weeklyPatterns: WeeklyPatterns
    var monthlyPatterns: MonthlyPatterns

    init(
        userId: UUID,
        analysisDate: Date = Date(),
        id: UUID = UUID(),
        timeOfDayPatterns: TimeOfDayPatterns = TimeOfDayPatterns(),
        taskTypePatterns: TaskTypePatterns = TaskTypePatterns(),
        cognitiveLoadPatterns: [CognitiveLoadPattern] = [],
        productivityRhythms: ProductivityRhythms = ProductivityRhythms(),
        breakPatterns: BreakPatterns = BreakPatterns(),
        focusPatterns: FocusPatterns = FocusPatterns(),
        distractionTriggers: [String] = [],
        optimalConditions: [String] = [],
        seasonalPatterns: SeasonalPatterns = SeasonalPatterns(),
        weeklyPatterns: WeeklyPatterns = WeeklyPatterns(),
        monthlyPatterns: MonthlyPatterns = MonthlyPatterns()
    ) {
        self.id = id
        self.userId = userId
        self.analysisDate = analysisDate
        self.timeOfDayPatterns = timeOfDayPatterns
        self.taskTypePatterns = taskTypePatterns
        self.cognitiveLoadPatterns = cognitiveLoadPatterns
        self.productivityRhythms = productivityRhythms
        self.breakPatterns = breakPatterns
        self.focusPatterns = focusPatterns
        self.distractionTriggers = distractionTriggers
        self.optimalConditions = optimalConditions
        self.seasonalPatterns = seasonalPatterns
        self.weeklyPatterns = weeklyPatterns
        self.monthlyPatterns = monthlyPatterns
    }
}

// MARK: - Pattern Analysis Types

@available(iOS 18.0, *)
struct TimeOfDayPatterns: Codable, Equatable {
    var optimalHours: [Int] = []
    var peakPerformanceTime: Int = 10
    var lowEnergyPeriods: [Int] = []
}

@available(iOS 18.0, *)
struct TaskTypePatterns: Codable, Equatable {
    var mostEffective: [TaskCategory] = []
    var leastEffective: [TaskCategory] = []
    var preferredComplexity: TaskComplexity = .medium
}

@available(iOS 18.0, *)
struct ProductivityRhythms: Codable, Equatable {
    var peakHours: [Int] = []
    var lowEnergyHours: [Int] = []
    var optimalSessionLength: TimeInterval = 1_800
    var naturalBreakPoints: [TimeInterval] = []
}

@available(iOS 18.0, *)
struct BreakPatterns: Codable, Equatable {
    var optimalFrequency: TimeInterval = 1_800
    var averageBreakDuration: TimeInterval = 300
    var mostEffectiveBreakTypes: [BreakType] = []
}

@available(iOS 18.0, *)
struct FocusPatterns: Codable, Equatable {
    var averageAttentionSpan: TimeInterval = 1_800
    var focusBuildup: TimeInterval = 300
    var focusDecay: TimeInterval = 1_800
    var distractionRecovery: TimeInterval = 120
}

@available(iOS 18.0, *)
struct SeasonalPatterns: Codable, Equatable {
    var springProductivity: Double = 0.0
    var summerProductivity: Double = 0.0
    var fallProductivity: Double = 0.0
    var winterProductivity: Double = 0.0
}

@available(iOS 18.0, *)
struct WeeklyPatterns: Codable, Equatable {
    var mondayProductivity: Double = 0.0
    var tuesdayProductivity: Double = 0.0
    var wednesdayProductivity: Double = 0.0
    var thursdayProductivity: Double = 0.0
    var fridayProductivity: Double = 0.0
    var saturdayProductivity: Double = 0.0
    var sundayProductivity: Double = 0.0
}

@available(iOS 18.0, *)
struct MonthlyPatterns: Codable, Equatable {
    var earlyMonthProductivity: Double = 0.0
    var midMonthProductivity: Double = 0.0
    var lateMonthProductivity: Double = 0.0
}
