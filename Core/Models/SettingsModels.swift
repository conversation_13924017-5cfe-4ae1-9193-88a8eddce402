import Foundation
import SwiftUI

// MARK: - Settings Models

struct AppSettings {
    let userProfile: UserProfile
    let notificationSettings: NeuroNexaNotificationSettings
    let privacySettings: NeuroNexaPrivacySettings
}

struct NeuroNexaNotificationSettings: Codable {
    let taskRemindersEnabled: Bool
    let breakNotificationsEnabled: Bool
    let routineRemindersEnabled: Bool
    let moodCheckInsEnabled: Bool
    let reminderStyle: ReminderStyle
}

struct NeuroNexaPrivacySettings: Codable {
    let healthDataSharingEnabled: Bool
    let analyticsEnabled: Bool
    let crashReportingEnabled: Bool
}

struct CognitiveLoadSettings {
    let defaultLevel: CognitiveLoadLevel
    let autoAdaptInterface: Bool
    let breakRemindersEnabled: Bool
    let breakFrequency: Int
    let monitoringEnabled: Bool

    static let `default` = CognitiveLoadSettings(
        defaultLevel: .medium,
        autoAdaptInterface: true,
        breakRemindersEnabled: true,
        breakFrequency: 45,
        monitoringEnabled: true
    )
}

struct SensorySettings {
    let brightnessLevel: BrightnessLevel
    let colorIntensity: ColorIntensity
    let soundLevel: SoundLevel
    let hapticFeedbackEnabled: Bool
    let reduceAnimations: Bool
    let parallaxEffects: Bool

    static let `default` = SensorySettings(
        brightnessLevel: .normal,
        colorIntensity: .normal,
        soundLevel: .normal,
        hapticFeedbackEnabled: true,
        reduceAnimations: false,
        parallaxEffects: true
    )
}

struct ExecutiveFunctionSettings {
    let defaultTaskBreakdown: TaskBreakdownLevel
    let autoGenerateSteps: Bool
    let showTimeEstimates: Bool
    let defaultFocusDuration: Int
    let focusReminders: Bool
    let distractionBlocking: Bool

    static let `default` = ExecutiveFunctionSettings(
        defaultTaskBreakdown: .standard,
        autoGenerateSteps: true,
        showTimeEstimates: true,
        defaultFocusDuration: 25,
        focusReminders: true,
        distractionBlocking: false
    )
}

// MARK: - Enums for Settings

enum ReminderStyle: String, CaseIterable, Codable {
    case gentle
    case standard
    case persistent
}

enum DynamicTypeSize: String, CaseIterable, Codable {
    case xSmall
    case small
    case medium
    case large
    case xLarge
    case xxLarge
    case xxxLarge
}

enum BrightnessLevel: String, CaseIterable {
    case dim
    case normal
    case bright
}

enum ColorIntensity: String, CaseIterable {
    case muted
    case normal
    case vibrant
}

enum SoundLevel: String, CaseIterable {
    case silent
    case quiet
    case normal
    case loud
}

@available(iOS 18.0, *)
struct UserSettings: Codable {
    let id: UUID
    let userId: UUID
    let theme: NeuroNexaTheme
    let notifications: NeuroNexaNotificationSettings
    let accessibility: AccessibilitySettings
    let privacy: NeuroNexaPrivacySettings
}

@available(iOS 18.0, *)
struct NotificationSettings: Codable {
    let enabled: Bool
    let taskReminders: Bool
    let breathingReminders: Bool
    let cognitiveBreaks: Bool
    let quietHours: QuietHours
}

@available(iOS 18.0, *)
struct QuietHours: Codable {
    let enabled: Bool
    let startTime: Date
    let endTime: Date
}

@available(iOS 18.0, *)
struct PrivacySettings: Codable {
    let dataSharing: Bool
    let analytics: Bool
    let crashReporting: Bool
    let personalizedAds: Bool
}

@available(iOS 18.0, *)
protocol SettingsServiceProtocol {
    func getSettings() async -> UserSettings
    func saveSettings(_ settings: UserSettings) async throws
}
