import CloudKit
import CoreData
import Foundation
import HealthKit
import SwiftUI
import WatchConnectivity

// MARK: - Simple Type Definitions for UserProfile

// MARK: - Simple Type Definitions for UserProfile
@available(iOS 18.0, *)
public struct SimpleUserPreferencesForProfile: Codable, Sendable {
    public var notificationsEnabled: Bool = true
    public var healthKitEnabled: Bool = false
    public var aiCoachingEnabled: Bool = true
    public var language: String = "en"

    public init() {}
}

@available(iOS 18.0, *)
public struct SimpleCognitivePreferencesForProfile: Codable, Sendable {
    public let focusMode: Bool = false
    public let distractionLevel: String = "medium"
    public let processingSpeed: String = "normal"
    public let workingMemorySupport: Bool = true

    public init() {}
}

// MARK: - User Profile Models

/// User profile with neurodiversity-specific preferences and cognitive data
@available(iOS 18.0, *)
public struct UserProfile: Identifiable, Sendable {
    public let id: UUID
    public var name: String
    public var email: String
    public var createdAt: Date
    public var lastActiveAt: Date

    // Neurodiversity Profile
    public var neurodiversityTypes: [NeurodiversityType]
    public var cognitiveStrengths: [CognitiveStrength]
    public var supportNeeds: [CognitiveSupport]
    public var preferredTheme: NeuroNexaTheme
    public var cognitiveProfile: CognitiveProfile
    public var sensoryPreferences: SensoryPreferences
    public var accessibilitySettings: AccessibilitySettings
    public var cognitiveLoadSettings: CognitiveLoadSettings
    public var sensorySettings: SensorySettings
    public var executiveFunctionSettings: ExecutiveFunctionSettings
    public var executiveFunctionLevel: ExecutiveFunctionLevel

    // Health Integration
    var healthKitAuthorized: Bool
    var mentalHealthDataEnabled: Bool
    var baselineHeartRate: Double?

    // User Preferences
    public var preferences: SimpleUserPreferencesForProfile?
    public var cognitivePreferences: SimpleCognitivePreferencesForProfile?
    
    // Claude: Adding neurodiversityProfile property for breathing service compatibility
    public var neurodiversityProfile: UserNeurodiversityProfile { 
        UserNeurodiversityProfile(types: neurodiversityTypes)
    }

    // CloudKit Integration (excluded from Codable)
    private var cloudKitRecordID: CKRecord.ID?

    // MARK: - Initialization

    init(name: String, email: String, id: UUID = UUID(), neurodiversityTypes: [NeurodiversityType] = [], cognitiveStrengths: [CognitiveStrength] = [], supportNeeds: [CognitiveSupport] = [], preferredTheme: NeuroNexaTheme = .adaptive, accessibilitySettings: AccessibilitySettings = .default, cognitiveLoadSettings: CognitiveLoadSettings = .default, sensoryPreferences: SensoryPreferences = .default, executiveFunctionSettings: ExecutiveFunctionSettings = .default) {
        self.id = id; self.name = name; self.email = email; self.createdAt = Date(); self.lastActiveAt = Date(); self.neurodiversityTypes = neurodiversityTypes; self.cognitiveStrengths = cognitiveStrengths; self.supportNeeds = supportNeeds; self.preferredTheme = preferredTheme; self.cognitiveProfile = .default; self.sensoryPreferences = sensoryPreferences; self.accessibilitySettings = accessibilitySettings; self.cognitiveLoadSettings = cognitiveLoadSettings; self.sensorySettings = .default; self.executiveFunctionSettings = executiveFunctionSettings; self.executiveFunctionLevel = .medium; self.healthKitAuthorized = false; self.mentalHealthDataEnabled = false; self.baselineHeartRate = nil; self.preferences = nil; self.cognitivePreferences = nil; self.cloudKitRecordID = nil
    }
}

/// Cognitive profile for personalized AI assistance
public struct CognitiveProfile: Codable, Sendable {
    var neurodiversityType: [NeurodiversityType]
    var cognitiveStrengths: [CognitiveStrength]
    var cognitiveSupports: [CognitiveSupport]
    var preferredLearningStyle: LearningStyle
    var attentionSpan: AttentionSpan
    var processingSpeed: ProcessingSpeed
    var workingMemoryCapacity: WorkingMemoryCapacity

    static let `default` = CognitiveProfile(neurodiversityType: [], cognitiveStrengths: [], cognitiveSupports: [], preferredLearningStyle: .visual, attentionSpan: .medium, processingSpeed: .medium, workingMemoryCapacity: .medium)
}

// Note: SensoryPreferences and AccessibilitySettings are defined in their dedicated files:
// - Core/Models/AccessibilitySettings.swift
// - Core/Models/AccessibilitySettings.swift (includes SensoryPreferences)

@available(iOS 18.0, *)
struct UserContext: Codable {
    let userId: UUID
    let currentState: UserState
    let cognitiveLoad: CognitiveLoadLevel
    let recentTasks: [AITask]
    let preferences: SimpleUserPreferencesForProfile
}

@available(iOS 18.0, *)
enum UserState: String, CaseIterable, Codable {
    case active, focused, overwhelmed, resting, transitioning
}

@available(iOS 18.0, *)
struct UserBehaviorData: Codable {
    let userId: UUID
    let actions: [String]
    let timestamp: Date
    let context: String
}

@available(iOS 18.0, *)
struct UserNeed: Codable {
    let type: String
    let priority: Int
    let description: String
    let suggestedActions: [String]
}

// CognitivePreferences is defined in Core/Models/CognitivePreferencesModels.swift

@available(iOS 18.0, *)
protocol UserRepositoryProtocol {
    func getCurrentUser() async throws -> UserProfile?
    func saveUser(_ user: UserProfile) async throws
    func updateUser(_ user: UserProfile) async throws
    func deleteUser(_ user: UserProfile) async throws
}

@available(iOS 18.0, *)
protocol UserServiceProtocol {
    func getCurrentUser() async throws -> UserProfile?
    func saveUser(_ user: UserProfile) async throws
}

// UserProfileRepositoryProtocol is defined in Core/Repositories/UserProfileRepository.swift

// MARK: - Missing Settings Types

@available(iOS 18.0, *)
public struct CognitiveLoadSettings: Codable, Sendable {
    public var maxSimultaneousTasks: Int
    public var breakFrequency: TimeInterval
    public var complexityThreshold: Double
    public var adaptiveScheduling: Bool

    public init(maxSimultaneousTasks: Int = 3, breakFrequency: TimeInterval = 1_800, complexityThreshold: Double = 0.7, adaptiveScheduling: Bool = true) {
        self.maxSimultaneousTasks = maxSimultaneousTasks
        self.breakFrequency = breakFrequency
        self.complexityThreshold = complexityThreshold
        self.adaptiveScheduling = adaptiveScheduling
    }
}

@available(iOS 18.0, *)
public struct SensorySettings: Codable, Sendable {
    public var soundEnabled: Bool
    public var hapticsEnabled: Bool
    public var visualEffectsEnabled: Bool
    public var brightnessLevel: Double

    public init(soundEnabled: Bool = true, hapticsEnabled: Bool = true, visualEffectsEnabled: Bool = true, brightnessLevel: Double = 0.8) {
        self.soundEnabled = soundEnabled
        self.hapticsEnabled = hapticsEnabled
        self.visualEffectsEnabled = visualEffectsEnabled
        self.brightnessLevel = brightnessLevel
    }
}

@available(iOS 18.0, *)
public struct ExecutiveFunctionSettings: Codable, Sendable {
    public var taskBreakdownEnabled: Bool
    public var reminderFrequency: TimeInterval
    public var scaffoldingLevel: Int
    public var progressTrackingEnabled: Bool

    public init(taskBreakdownEnabled: Bool = true, reminderFrequency: TimeInterval = 900, scaffoldingLevel: Int = 2, progressTrackingEnabled: Bool = true) {
        self.taskBreakdownEnabled = taskBreakdownEnabled
        self.reminderFrequency = reminderFrequency
        self.scaffoldingLevel = scaffoldingLevel
        self.progressTrackingEnabled = progressTrackingEnabled
    }

    public static let `default` = ExecutiveFunctionSettings()
}

@available(iOS 18.0, *)
public extension CognitiveLoadSettings {
    static let `default` = CognitiveLoadSettings()
}

@available(iOS 18.0, *)
public extension SensorySettings {
    static let `default` = SensorySettings()
}

// Claude: Adding UserNeurodiversityProfile struct for breathing service compatibility
@available(iOS 18.0, *)
public struct UserNeurodiversityProfile: Codable, Sendable {
    public let types: [NeurodiversityType]
    
    public init(types: [NeurodiversityType]) {
        self.types = types
    }
}
