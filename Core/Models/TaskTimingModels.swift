import Foundation

// MARK: - Task Timing Models

@available(iOS 18.0, *)
struct TaskTiming: Codable {
    let suggestedStartTime: Date
    let estimatedDuration: TimeInterval
    let optimalTimeSlots: [TimeSlot]
    let reasoning: String
    let confidence: Double

    init(
        suggestedStartTime: Date,
        estimatedDuration: TimeInterval,
        reasoning: String,
        optimalTimeSlots: [TimeSlot] = [],
        confidence: Double = 0.8
    ) {
        self.suggestedStartTime = suggestedStartTime
        self.estimatedDuration = estimatedDuration
        self.optimalTimeSlots = optimalTimeSlots
        self.reasoning = reasoning
        self.confidence = confidence
    }
}

@available(iOS 18.0, *)
struct TimeSlot: Identifiable, Codable {
    let id = UUID()
    let startTime: Date
    let endTime: Date
    let suitabilityScore: Double
    let factors: [TimingFactor]

    init(startTime: Date, endTime: Date, suitabilityScore: Double, factors: [TimingFactor] = []) {
        self.startTime = startTime
        self.endTime = endTime
        self.suitabilityScore = suitabilityScore
        self.factors = factors
    }
}

@available(iOS 18.0, *)
enum TimingFactor: String, CaseIterable, Codable {
    case energyLevel
    case cognitiveCapacity
    case environmentalQuiet
    case scheduleAvailability
    case historicalPerformance
}
