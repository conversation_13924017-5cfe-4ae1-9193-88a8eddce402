// MARK: - Cognitive Models Central Reference
// This file has been refactored to meet SwiftLint file length requirements.
// All cognitive model types have been moved to focused, smaller files:
//
// - CognitivePreferencesModels.swift: User cognitive preferences and settings
// - ExecutiveFunctionModels.swift: Executive function assessments and insights
// - CognitivePatternModels.swift: Cognitive patterns and analysis types
// - CognitiveOptimizationModels.swift: Cognitive optimization strategies
// - CognitiveSupportingTypes.swift: Supporting enums and utility types
//
// All types are available throughout the project via their individual files.
