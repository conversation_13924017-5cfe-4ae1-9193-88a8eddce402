import Foundation

// MARK: - Personalized Content Types

@available(iOS 18.0, *)
struct PersonalizedContent: Identifiable, Codable {
    let id = UUID()
    let type: ContentType
    let title: String
    let content: String
    let priority: ContentPriority
    let targetAudience: [NeurodiversityType]
    let cognitiveLoad: CognitiveLoadLevel
    let estimatedReadTime: TimeInterval
    let interactionStyle: InteractionStyle
    let adaptations: [ContentAdaptation]
    let metadata: ContentMetadata

    init(
        type: ContentType,
        title: String,
        content: String,
        priority: ContentPriority = .medium,
        targetAudience: [NeurodiversityType] = [],
        cognitiveLoad: CognitiveLoadLevel = .moderate,
        estimatedReadTime: TimeInterval = 60,
        interactionStyle: InteractionStyle = .guided,
        adaptations: [ContentAdaptation] = [],
        metadata: ContentMetadata = ContentMetadata()
    ) {
        self.type = type
        self.title = title
        self.content = content
        self.priority = priority
        self.targetAudience = targetAudience
        self.cognitiveLoad = cognitiveLoad
        self.estimatedReadTime = estimatedReadTime
        self.interactionStyle = interactionStyle
        self.adaptations = adaptations
        self.metadata = metadata
    }
}

@available(iOS 18.0, *)
enum ContentType: String, CaseIterable, Codable {
    case taskSuggestion
    case motivationalMessage
    case educationalTip
    case breathingExercise
    case cognitiveStrategy
    case executiveSupport
    case sensoryBreak
    case socialScript
}

@available(iOS 18.0, *)
enum ContentPriority: String, CaseIterable, Codable {
    case low
    case medium
    case high
    case urgent
}

@available(iOS 18.0, *)
enum InteractionStyle: String, CaseIterable, Codable {
    case passive
    case guided
    case interactive
    case immersive
}

@available(iOS 18.0, *)
struct ContentAdaptation: Codable {
    let adaptationType: AdaptationType
    let description: String
    let targetCondition: NeurodiversityType
}

@available(iOS 18.0, *)
enum AdaptationType: String, CaseIterable, Codable {
    case visual
    case auditory
    case cognitive
    case motor
    case sensory
    case temporal
}

@available(iOS 18.0, *)
struct ContentMetadata: Codable {
    let createdAt: Date
    let lastUpdated: Date
    let version: String
    let source: ContentSource
    let effectiveness: Double
    let userFeedback: [UserFeedback]

    init(
        createdAt: Date = Date(),
        lastUpdated: Date = Date(),
        version: String = "1.0",
        source: ContentSource = .ai,
        effectiveness: Double = 0.0,
        userFeedback: [UserFeedback] = []
    ) {
        self.createdAt = createdAt
        self.lastUpdated = lastUpdated
        self.version = version
        self.source = source
        self.effectiveness = effectiveness
        self.userFeedback = userFeedback
    }
}

@available(iOS 18.0, *)
enum ContentSource: String, CaseIterable, Codable {
    case ai
    case expert
    case community
    case research
    case user
}

@available(iOS 18.0, *)
struct UserFeedback: Codable {
    let rating: Int
    let comment: String
    let timestamp: Date

    init(rating: Int, comment: String = "", timestamp: Date = Date()) {
        self.rating = rating
        self.comment = comment
        self.timestamp = timestamp
    }
}
