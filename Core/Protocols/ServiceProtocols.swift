import Foundation
import SwiftUI

// Import shared types for cross-module dependencies

// MARK: - Breathing Exercise Service Protocol

@available(iOS 18.0, *)
protocol BreathingExerciseServiceProtocol {
    func getAvailableExercises() async -> [BreathingExercise]
    func getExercise(by id: UUID) async -> BreathingExercise?
    func getExercisesByType(_ type: BreathingExerciseType) async -> [BreathingExercise]
    func getPersonalizedExercises(for user: UserProfile) async -> [BreathingExercise]
    func createCustomExercise(_ exercise: BreathingExercise) async throws
    func updateExercise(_ exercise: BreathingExercise) async throws
    func deleteExercise(_ exercise: BreathingExercise) async throws
    func getExerciseRecommendations(for cognitiveLoad: CognitiveLoadLevel) async -> [BreathingExercise]
    func trackExerciseUsage(_ exerciseId: UUID, duration: TimeInterval) async
    func getExerciseStatistics(for exerciseId: UUID) async -> ExerciseStatistics
}

// MARK: - User Repository Protocol

@available(iOS 18.0, *)
protocol UserRepositoryProtocol {
    func getCurrentUser() async throws -> UserProfile?
    func saveUser(_ user: UserProfile) async throws
    func updateUser(_ user: UserProfile) async throws
    func deleteUser(_ user: UserProfile) async throws
    func getUserById(_ id: UUID) async throws -> UserProfile?
    func getUserByEmail(_ email: String) async throws -> UserProfile?
    func createUser(_ user: UserProfile) async throws
    func updateUserPreferences(_ preferences: UserPreferences, for userId: UUID) async throws
    func getUserPreferences(for userId: UUID) async throws -> UserPreferences?
    func updateCognitivePreferences(_ preferences: CognitivePreferences, for userId: UUID) async throws
    func getCognitivePreferences(for userId: UUID) async throws -> CognitivePreferences?
    func syncUserData() async throws
    func clearUserData() async throws
}

// MARK: - Task Completion Protocol

@available(iOS 18.0, *)
protocol TaskCompletionProtocol {
    var taskId: UUID { get }
    var userId: UUID { get }
    var completedAt: Date { get }
    var completionTime: TimeInterval { get }
    var estimatedDuration: TimeInterval { get }
    var accuracy: Double { get }
    var qualityScore: Double { get }
    var cognitiveLoadDuring: Double { get }
    var interruptions: Int { get }
    var taskCategory: TaskCategory { get }
}

// MARK: - Cognitive Insights Protocol

@available(iOS 18.0, *)
protocol CognitiveInsightsProtocol {
    var id: UUID { get }
    var userId: UUID { get }
    var generatedAt: Date { get }
    var cognitiveStrengths: [String] { get }
    var cognitiveWeaknesses: [String] { get }
    var optimalWorkingHours: [Int] { get }
    var recommendedTaskTypes: [TaskCategory] { get }
    var suggestedBreakFrequency: TimeInterval { get }
    var personalizedStrategies: [String] { get }
    var progressMetrics: ProgressMetrics { get }
    var nextReviewDate: Date { get }
}

// MARK: - Breathing Exercise Types

// Note: BreathingExercise and BreathingExerciseType are now defined in Core/Models/SharedTypes.swift

@available(iOS 18.0, *)
public enum ExerciseDifficulty: String, Codable, CaseIterable {
    case beginner
    case intermediate
    case advanced
    case expert
}

@available(iOS 18.0, *)
struct ExerciseStatistics: Codable {
    let exerciseId: UUID
    let totalUsage: Int
    let averageDuration: TimeInterval
    let averageRating: Double
    let completionRate: Double
    let userFeedback: [String]
    let effectivenessScore: Double
    let lastUsed: Date?
    let popularityRank: Int
}

// MARK: - Task Completion Implementation

@available(iOS 18.0, *)
struct TaskCompletion: TaskCompletionProtocol, Codable, Identifiable, Equatable {
    let id = UUID()
    let taskId: UUID
    let userId: UUID
    let completedAt: Date
    let completionTime: TimeInterval
    let estimatedDuration: TimeInterval
    let accuracy: Double
    let qualityScore: Double
    let cognitiveLoadDuring: Double
    let interruptions: Int
    let taskCategory: TaskCategory
    var notes: String?
    var mood: MoodLevel?
    var energyLevel: EnergyLevel?
    var satisfactionLevel: SatisfactionLevel?
    var difficultyRating: DifficultyRating?

    init(
        taskId: UUID,
        userId: UUID,
        completionTime: TimeInterval,
        estimatedDuration: TimeInterval,
        accuracy: Double,
        qualityScore: Double,
        cognitiveLoadDuring: Double,
        interruptions: Int,
        taskCategory: TaskCategory,
        completedAt: Date = Date(),
        notes: String? = nil,
        mood: MoodLevel? = nil,
        energyLevel: EnergyLevel? = nil,
        satisfactionLevel: SatisfactionLevel? = nil,
        difficultyRating: DifficultyRating? = nil
    ) {
        self.taskId = taskId
        self.userId = userId
        self.completedAt = completedAt
        self.completionTime = completionTime
        self.estimatedDuration = estimatedDuration
        self.accuracy = accuracy
        self.qualityScore = qualityScore
        self.cognitiveLoadDuring = cognitiveLoadDuring
        self.interruptions = interruptions
        self.taskCategory = taskCategory
        self.notes = notes
        self.mood = mood
        self.energyLevel = energyLevel
        self.satisfactionLevel = satisfactionLevel
        self.difficultyRating = difficultyRating
    }
}

// MARK: - Cognitive Insights Implementation

@available(iOS 18.0, *)
struct CognitiveInsights: CognitiveInsightsProtocol, Codable, Identifiable, Equatable {
    let id: UUID
    let userId: UUID
    let generatedAt: Date
    let cognitiveStrengths: [String]
    let cognitiveWeaknesses: [String]
    let optimalWorkingHours: [Int]
    let recommendedTaskTypes: [TaskCategory]
    let suggestedBreakFrequency: TimeInterval
    let personalizedStrategies: [String]
    let progressMetrics: ProgressMetrics
    let nextReviewDate: Date
    var confidenceScore: Double
    var dataQuality: DataQuality
    var recommendations: [CognitiveRecommendation]
    var trends: [CognitiveTrend]

    init(
        userId: UUID,
        cognitiveStrengths: [String],
        cognitiveWeaknesses: [String],
        optimalWorkingHours: [Int],
        recommendedTaskTypes: [TaskCategory],
        suggestedBreakFrequency: TimeInterval,
        personalizedStrategies: [String],
        progressMetrics: ProgressMetrics,
        nextReviewDate: Date,
        id: UUID = UUID(),
        generatedAt: Date = Date(),
        confidenceScore: Double = 0.0,
        dataQuality: DataQuality = .medium,
        recommendations: [CognitiveRecommendation] = [],
        trends: [CognitiveTrend] = []
    ) {
        self.id = id
        self.userId = userId
        self.generatedAt = generatedAt
        self.cognitiveStrengths = cognitiveStrengths
        self.cognitiveWeaknesses = cognitiveWeaknesses
        self.optimalWorkingHours = optimalWorkingHours
        self.recommendedTaskTypes = recommendedTaskTypes
        self.suggestedBreakFrequency = suggestedBreakFrequency
        self.personalizedStrategies = personalizedStrategies
        self.progressMetrics = progressMetrics
        self.nextReviewDate = nextReviewDate
        self.confidenceScore = confidenceScore
        self.dataQuality = dataQuality
        self.recommendations = recommendations
        self.trends = trends
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct ProgressMetrics: Codable, Equatable {
    let completionRate: Double
    let accuracyTrend: Double
    let efficiencyTrend: Double
    let focusImprovement: Double
    let stressReduction: Double
    let satisfactionImprovement: Double
    let goalAchievementRate: Double

    init(
        completionRate: Double = 0.0,
        accuracyTrend: Double = 0.0,
        efficiencyTrend: Double = 0.0,
        focusImprovement: Double = 0.0,
        stressReduction: Double = 0.0,
        satisfactionImprovement: Double = 0.0,
        goalAchievementRate: Double = 0.0
    ) {
        self.completionRate = completionRate
        self.accuracyTrend = accuracyTrend
        self.efficiencyTrend = efficiencyTrend
        self.focusImprovement = focusImprovement
        self.stressReduction = stressReduction
        self.satisfactionImprovement = satisfactionImprovement
        self.goalAchievementRate = goalAchievementRate
    }
}

@available(iOS 18.0, *)
enum MoodLevel: String, Codable, CaseIterable {
    case veryLow = "very_low"
    case low
    case neutral
    case good
    case excellent
}

@available(iOS 18.0, *)
enum EnergyLevel: String, Codable, CaseIterable {
    case exhausted
    case low
    case moderate
    case high
    case energized
}

@available(iOS 18.0, *)
enum SatisfactionLevel: String, Codable, CaseIterable {
    case veryUnsatisfied = "very_unsatisfied"
    case unsatisfied
    case neutral
    case satisfied
    case verySatisfied = "very_satisfied"
}

@available(iOS 18.0, *)
enum DifficultyRating: String, Codable, CaseIterable {
    case veryEasy = "very_easy"
    case easy
    case moderate
    case difficult
    case veryDifficult = "very_difficult"
}

@available(iOS 18.0, *)
enum DataQuality: String, Codable, CaseIterable {
    case low
    case medium
    case high
    case excellent
}

@available(iOS 18.0, *)
struct CognitiveRecommendation: Codable, Identifiable, Equatable {
    let id = UUID()
    let type: RecommendationType
    let title: String
    let description: String
    let priority: RecommendationPriority
    let expectedBenefit: String
    let implementationSteps: [String]
    let timeframe: TimeInterval
    let category: RecommendationCategory
}

@available(iOS 18.0, *)
struct CognitiveTrend: Codable, Identifiable, Equatable {
    let id = UUID()
    let metric: String
    let direction: TrendDirection
    let magnitude: Double
    let timeframe: TimeInterval
    let significance: TrendSignificance
    let description: String
}

@available(iOS 18.0, *)
enum RecommendationType: String, Codable, CaseIterable {
    case behavioral
    case environmental
    case cognitive
    case technological
    case social
}

@available(iOS 18.0, *)
enum RecommendationCategory: String, Codable, CaseIterable {
    case focus
    case productivity
    case wellbeing
    case learning
    case stress
}

@available(iOS 18.0, *)
enum TrendDirection: String, Codable, CaseIterable {
    case improving
    case declining
    case stable
    case fluctuating
}

@available(iOS 18.0, *)
enum TrendSignificance: String, Codable, CaseIterable {
    case low
    case moderate
    case high
    case critical
}
