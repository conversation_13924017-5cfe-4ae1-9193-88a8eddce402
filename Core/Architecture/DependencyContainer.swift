import CloudKit
import Combine
import CoreData
import Foundation
import HealthKit
import SwiftUI

// MARK: - Required Type Definitions

@available(iOS 18.0, *)
public struct MoodReading: Identifiable, Codable, Sendable {
    public let id = UUID()
    public let timestamp: Date
    public let moodLevel: MoodLevel
    public let energyLevel: EnergyLevel
    public let stressLevel: StressLevel
    public let notes: String?
    public let context: String?

    public init(
        moodLevel: MoodLevel,
        energyLevel: EnergyLevel,
        stressLevel: StressLevel,
        timestamp: Date = Date(),
        notes: String? = nil,
        context: String? = nil
    ) {
        self.timestamp = timestamp
        self.moodLevel = moodLevel
        self.energyLevel = energyLevel
        self.stressLevel = stressLevel
        self.notes = notes
        self.context = context
    }
}

/// NeuroNexa Dependency Container - iOS 26 Enhanced
/// Provides centralized dependency injection for neurodiversity-first architecture
@available(iOS 18.0, *)
@MainActor
class DependencyContainer: ObservableObject {
    static let shared = DependencyContainer()

    // MARK: - Core Services
    lazy var authenticationService: AuthenticationServiceProtocol = AuthenticationService()
    lazy var aiTaskCoachService: AITaskCoachServiceProtocol = AITaskCoachService()
    lazy var healthKitService: HealthKitServiceProtocol = HealthKitService()
    lazy var coreDataService: CoreDataServiceProtocol = CoreDataService()
    lazy var cloudKitService: CloudKitServiceProtocol = CloudKitService()
    lazy var userService: UserServiceProtocol = UserService()
    lazy var settingsService: SettingsServiceProtocol = SettingsService()

    // MARK: - Neurodiversity Services
    lazy var cognitiveLoadService: CognitiveLoadServiceProtocol = CognitiveLoadService()
    lazy var sensoryAdaptationService: SensoryAdaptationServiceProtocol = SensoryAdaptationService()
    lazy var executiveFunctionService: ExecutiveFunctionServiceProtocol = ExecutiveFunctionService()
    lazy var breathingService: BreathingServiceProtocol = BreathingService(
        healthKitService: healthKitService,
        userService: userService,
        coreDataService: coreDataService
    )

    // MARK: - AI Services (OpenAI)
    lazy var personalizedTaskService: PersonalizedTaskServiceProtocol = PersonalizedTaskService()
    lazy var cognitiveAnalysisService: CognitiveAnalysisServiceProtocol = CognitiveAnalysisService()

    // MARK: - Watch Connectivity
    lazy var watchConnectivityService: WatchConnectivityServiceProtocol = WatchConnectivityService()

    // MARK: - Repositories
    lazy var userProfileRepository: UserProfileRepositoryProtocol = DefaultUserProfileRepository()
    lazy var taskRepository: TaskRepositoryProtocol = TaskRepository()
    lazy var routineRepository: RoutineRepositoryProtocol = RoutineRepository()
    lazy var breathingSessionRepository: BreathingSessionRepositoryProtocol =
        BreathingSessionRepository()

    private init() {
        setupDependencies()
    }

    private func setupDependencies() {
        // Configure services with dependencies
        if let aiService = aiTaskCoachService as? AITaskCoachService {
            aiService.configure(
                cognitiveLoad: cognitiveLoadService,
                healthKit: healthKitService
            )
        }

        // BreathingService is configured via dependency injection in its initializer
    }
}

// MARK: - Service Protocols

/// Authentication service for secure user management
public protocol AuthenticationServiceProtocol {
    func signIn() async throws -> UserProfile
    func signOut() async throws
    func getCurrentUser() async -> UserProfile?
    func authenticateWithBiometrics() async throws -> Bool
}

/// AI Task Coach service for personalized task management
public protocol AITaskCoachServiceProtocol {
    func generatePersonalizedTasks(for user: UserProfile) async throws -> [AITask]
    func analyzeTaskCompletion(_ task: AITask, completion: TaskCompletion) async
    func adaptTasksForCognitiveLoad(_ tasks: [AITask], cognitiveLoad: CognitiveLoadLevel) async
    -> [AITask]
    func suggestTaskBreakdown(_ task: AITask) async -> [AITask]
}

/// HealthKit integration for cognitive and mental health monitoring
protocol HealthKitServiceProtocol {
    func requestAuthorization() async throws
    func getCurrentCognitiveLoad() async throws -> CognitiveLoadLevel
    func getHeartRateData() async throws -> [HeartRateReading]
    func getMoodData() async throws -> [MoodReading]
    func saveBreathingSession(_ session: BreathingSession) async throws
}

/// Core Data service for local data persistence
protocol CoreDataServiceProtocol {
    func save() async throws
    func fetch<T: NSManagedObject>(_ type: T.Type, predicate: NSPredicate?) async throws -> [T]
    func delete<T: NSManagedObject>(_ object: T) async throws
    func getAllRoutines() async throws -> [Routine]
    func saveRoutine(_ routine: Routine) async throws
    func updateRoutine(_ routine: Routine) async throws
    func deleteRoutine(_ routine: Routine) async throws
    func getRoutine(by id: UUID) async throws -> Routine?
}

/// CloudKit service for data synchronization
protocol CloudKitServiceProtocol {
    func syncUserProfile(_ profile: UserProfile) async throws
    func syncTasks(_ tasks: [AITask]) async throws
    func syncRoutines(_ routines: [Routine]) async throws
    func getAccountStatus() async throws -> CKAccountStatus
    func getAllRoutines() async throws -> [Routine]
    func saveRoutine(_ routine: Routine) async throws
    func updateRoutine(_ routine: Routine) async throws
    func deleteRoutine(_ routine: Routine) async throws
    func getRoutine(by id: UUID) async throws -> Routine?
}

/// Cognitive load monitoring and adaptation
protocol CognitiveLoadServiceProtocol {
    func getCurrentCognitiveLoad() async -> CognitiveLoadLevel
    func adaptUIForCognitiveLoad(_ level: CognitiveLoadLevel) -> CognitiveAdaptation
    func predictCognitiveOverload() async -> Bool
    func suggestCognitiveBreak() async -> BreakSuggestion?
}

/// Sensory adaptation for neurodiversity support
protocol SensoryAdaptationServiceProtocol {
    func getCurrentSensoryPreferences() -> SensoryPreferences
    func adaptForMotionSensitivity(_ level: MotionSensitivity) -> MotionAdaptation
    func adaptForColorContrast(_ level: ColorContrast) -> ColorAdaptation
    func adaptForSoundSensitivity(_ level: SoundSensitivity) -> SoundAdaptation
}

/// Executive function support service
protocol ExecutiveFunctionServiceProtocol {
    func breakDownTask(_ task: AITask) async -> [AITask]
    func suggestTaskOrder(_ tasks: [AITask]) async -> [AITask]
    func createTaskReminders(_ task: AITask) async -> [TaskReminder]
    func analyzeTaskPatterns(_ completions: [TaskCompletion]) async -> ExecutiveFunctionInsights
}

/// OpenAI Intelligence Service - Replaces Apple AI
/// Provides AI-powered features using OpenAI APIs with neurodiversity-first approach
@available(iOS 18.0, *)
protocol OpenAIIntelligenceServiceProtocol {
    func generatePersonalizedContent(for context: UserContext) async throws -> PersonalizedContent
    func analyzeUserBehavior(_ data: UserBehaviorData) async throws -> BehaviorInsights
    func predictUserNeeds(_ context: UserContext) async throws -> [UserNeed]
    func adaptUIForCognitiveLoad(_ level: CognitiveLoadLevel) -> CognitiveAdaptation
    func suggestCognitiveBreak() async -> BreakSuggestion?
    func isAvailable() async -> Bool
}

/// Personalized task generation
protocol PersonalizedTaskServiceProtocol {
    func generateDailyTasks(for user: UserProfile) async throws -> [AITask]
    func adaptTasksForMood(_ tasks: [AITask], mood: MoodLevel) -> [AITask]
    func suggestTaskTiming(_ task: AITask, for user: UserProfile) async -> TaskTiming
}

/// Cognitive analysis service
protocol CognitiveAnalysisServiceProtocol {
    func analyzeTaskPerformance(_ completion: TaskCompletion) async -> CognitiveAnalysis
    func identifyPatterns(_ data: [TaskCompletion]) async -> CognitivePatterns
    func suggestOptimizations(_ analysis: CognitiveAnalysis) async -> [CognitiveOptimization]
}

/// Apple Watch connectivity
protocol WatchConnectivityServiceProtocol {
    func sendTasksToWatch(_ tasks: [AITask]) async throws
    func sendBreathingSessionToWatch(_ session: BreathingSession) async throws
    func receiveHeartRateFromWatch() async throws -> HeartRateReading?
    func receiveTaskCompletionFromWatch() async throws -> TaskCompletion?
}

// MARK: - Repository Protocols

// UserProfileRepositoryProtocol, TaskRepositoryProtocol and RoutineRepositoryProtocol are defined in their respective repository files

@available(iOS 18.0, *)
protocol UserProfileRepositoryProtocol {
    func getUserProfile(for userId: UUID) async throws -> UserProfile?
    func saveUserProfile(_ profile: UserProfile) async throws
    func updateUserProfile(_ profile: UserProfile) async throws
    func deleteUserProfile(for userId: UUID) async throws
    func getAllUserProfiles() async throws -> [UserProfile]
    func syncWithCloud() async throws
}

protocol BreathingSessionRepositoryProtocol {
    func getAllSessions() async throws -> [BreathingSession]
    func getSessionsForDateRange(_ startDate: Date, _ endDate: Date) async throws
    -> [BreathingSession]
    func saveSession(_ session: BreathingSession) async throws
    func updateSession(_ session: BreathingSession) async throws
}

// MARK: - Default Implementations for DependencyContainer

@available(iOS 18.0, *)
class DefaultUserProfileRepository: UserProfileRepositoryProtocol {
    func getUserProfile(for userId: UUID) async throws -> UserProfile? { nil }
    func saveUserProfile(_ profile: UserProfile) async throws {}
    func updateUserProfile(_ profile: UserProfile) async throws {}
    func deleteUserProfile(for userId: UUID) async throws {}
    func getAllUserProfiles() async throws -> [UserProfile] { [] }
    func syncWithCloud() async throws {}
}
