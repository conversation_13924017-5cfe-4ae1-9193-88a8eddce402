import Combine
import Swift<PERSON>

@available(iOS 18.0, *)
@MainActor
protocol Coordinator: ObservableObject {
    associatedtype Body: View
    var body: Body { get }
}

@available(iOS 18.0, *)
@MainActor
class AppCoordinator: Coordinator {
    @Published var currentView: AppView = .dashboard

    var body: some View {
        NavigationStack {
            switch currentView {
            case .dashboard:
                DashboardView()
            case .aiTaskCoach:
                AITaskCoachView(viewModel: AITaskCoachViewModel(
                    aiTaskCoachService: AITaskCoachService(),
                    cognitiveLoadService: CognitiveLoadService(),
                    userRepository: UserRepository(),
                    taskRepository: TaskRepository()
                ))
            case .breathing:
                AdvancedBreathingView(viewModel: BreathingViewModel(
                    breathingService: BreathingExerciseService(
                        healthKitService: DependencyContainer.shared.healthKitService,
                        watchConnectivityService: DependencyContainer.shared.watchConnectivityService,
                        sensoryAdaptationService: DependencyContainer.shared.sensoryAdaptationService
                    ),
                    userRepository: UserRepository()
                ))
            case .settings:
                SettingsView()
            }
        }
    }
}

enum AppView {
    case dashboard
    case aiTaskCoach
    case breathing
    case settings
}
