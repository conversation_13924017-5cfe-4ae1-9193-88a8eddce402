import Combine
import Swift<PERSON>

@available(iOS 18.0, *)
protocol ViewModel: ObservableObject {
    func onAppear()
    func onDisappear()
}

@available(iOS 18.0, *)
class BaseViewModel: ViewModel {
    @Published var isLoading = false
    @Published var errorMessage: String?

    private var cancellables = Set<AnyCancellable>()

    func onAppear() {
        // Override in subclasses
    }

    func onDisappear() {
        // Override in subclasses
    }

    deinit {
        cancellables.removeAll()
    }
}
