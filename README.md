# NeuroNexa iOS 26

An AI-powered productivity assistant designed specifically for neurodiverse users, built with iOS 26 and Xcode 26 beta.

## 🚨 MANDATORY DEVELOPMENT REQUIREMENTS

**⚠️ CRITICAL: This project EXCLUSIVELY uses iOS 26 and Xcode Beta 26**

- **iOS Target**: iOS 26.0 ONLY - No development on earlier versions
- **Xcode Version**: Xcode Beta 26.0 ONLY - No stable Xcode versions
- **Enforcement**: Automated validation prevents non-compliant builds
- **Validation**: Run `./Scripts/validate-environment.sh` before development

### Environment Validation
```bash
# Verify your development environment meets requirements
./Scripts/validate-environment.sh
```

**All development must pass environment validation before proceeding.**

## Features

- 🧠 **OpenAI Integration**: GPT-4 powered AI for personalized task coaching
- ♿ **Enhanced Accessibility**: iOS 26 neurodiversity-focused accessibility features
- 🏥 **Advanced HealthKit**: Mental health and cognitive load tracking
- 🎨 **SwiftUI 6.0**: Modern, adaptive user interface
- ⌚ **Apple Watch Support**: Companion app for watchOS 26
- 🎯 **Neurodiversity-First Design**: ADHD, Autism, and executive function support

## Requirements

- iOS 26.0+
- Xcode 26.0 Beta
- Swift 6.0
- OpenAI API key for AI features

## Setup

1. Clone the repository
2. Run the setup script: `./Scripts/setup_ios26_project.sh`
3. Open `NeuroNexa.xcodeproj` in Xcode 26 Beta
4. Build and run on iOS 26 simulator or device

## Development

### MCP Tools

Use the MCP (Monitoring, Control, Planning) build tools:

```bash
# Complete setup and build
./Scripts/mcp_build_tools.sh all

# Individual commands
./Scripts/mcp_build_tools.sh setup
./Scripts/mcp_build_tools.sh build
./Scripts/mcp_build_tools.sh test
```

### Code Quality

- **SwiftLint**: Automated code style checking (100% compliance achieved)
- **SwiftFormat**: Consistent code formatting
- **Accessibility Testing**: Comprehensive accessibility validation
- **Augment Rules**: Follow comprehensive development guidelines in `Documentation/NeuroNexa_Augment_Rules.md`

## Architecture

- **MVVM-C**: Model-View-ViewModel-Coordinator pattern
- **Clean Architecture**: Separation of concerns with clear boundaries
- **Dependency Injection**: Comprehensive DI container
- **Reactive Programming**: Combine framework for data flow

## Testing

- **Unit Tests**: 70% coverage target
- **UI Tests**: Critical user journey validation
- **Accessibility Tests**: Neurodiversity-focused testing

## Documentation

### Development Guidelines
- **Augment Rules**: `Documentation/NeuroNexa_Augment_Rules.md` - Comprehensive iOS development guidelines
- **Architecture Guide**: `Documentation/PHASED_DEVELOPMENT_PLAN.md` - Project structure and implementation phases
- **Build Library**: `Documentation/BUILD_LIBRARY.md` - Build system and dependency management
- **iOS 26 Setup**: `Documentation/iOS26_SETUP_COMPLETE.md` - iOS 26 specific configuration

### Project Backups
Regular project backups are created in `/Users/<USER>/Desktop/NeuroNexa_Project_Backup_*` with timestamp.
Latest backup: `NeuroNexa_Project_Backup_20250706_210441`

## Contributing

Please read our development guidelines in the `Documentation/NeuroNexa_Augment_Rules.md` file and follow the established architecture patterns.

## License

Copyright © 2025 NeuroNexa. All rights reserved.
