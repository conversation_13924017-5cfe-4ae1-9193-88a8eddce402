<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>NeuroNexa</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>NeuroNexa Task File</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.neuronexa.task</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.neuronexa.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>neuronexa</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MinimumOSVersion</key>
	<string>26.0</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleIntelligenceUsageDescription</key>
	<string>NeuroNexa uses Apple Intelligence to provide personalized task coaching, cognitive support, and adaptive recommendations tailored to your neurodiversity profile. All processing happens on-device to protect your privacy.</string>
	<key>NSCameraUsageDescription</key>
	<string>NeuroNexa uses the camera to scan documents and provide visual assistance features for task management.</string>
	<key>NSHealthKitUsageDescription</key>
	<string>NeuroNexa tracks mental health metrics, cognitive load, and focus patterns to provide personalized wellness insights and support your neurodiversity journey. Your health data remains private and secure.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>NeuroNexa uses your location to provide context-aware task suggestions and environmental adaptations for your neurodiversity needs.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>NeuroNexa uses the microphone for voice commands and accessibility features to support users with different interaction preferences.</string>
	<key>NSPrivacyManifest</key>
	<dict>
		<key>NSPrivacyAccessedAPITypes</key>
		<array>
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryAppleIntelligence</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>Personalized task coaching</string>
					<string>Cognitive support</string>
				</array>
			</dict>
		</array>
		<key>NSPrivacyCollectedDataTypes</key>
		<array>
			<dict>
				<key>NSPrivacyCollectedDataType</key>
				<string>NSPrivacyCollectedDataTypeHealthAndFitness</string>
				<key>NSPrivacyCollectedDataTypeLinked</key>
				<false/>
				<key>NSPrivacyCollectedDataTypePurposes</key>
				<array>
					<string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
					<string>NSPrivacyCollectedDataTypePurposePersonalization</string>
				</array>
				<key>NSPrivacyCollectedDataTypeTracking</key>
				<false/>
			</dict>
		</array>
	</dict>
	<key>NSUserNotificationsUsageDescription</key>
	<string>NeuroNexa sends personalized reminders and gentle notifications to support your routines and task management.</string>
	<key>UIAccessibilityEnhancedFeatures</key>
	<dict>
		<key>CognitiveSupport</key>
		<true/>
		<key>ExecutiveFunctionSupport</key>
		<true/>
		<key>NeurodiversityOptimized</key>
		<true/>
		<key>SensoryAdaptation</key>
		<true/>
	</dict>
	<key>UIAccessibilityTraits</key>
	<array>
		<string>UIAccessibilityTraitAllowsDirectInteraction</string>
		<string>UIAccessibilityTraitCausesPageTurn</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<true/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>background-fetch</string>
		<string>remote-notification</string>
		<string>background-app-refresh</string>
		<string>fetch</string>
		<string>processing</string>
		<string>external-accessory</string>
		<string>location</string>
		<string>bluetooth-central</string>
		<string>nearby-interaction</string>
	</array>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string>NeuroNexaBackground</string>
		<key>UIImageName</key>
		<string>LaunchIcon</string>
	</dict>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>apple-intelligence</string>
		<string>healthkit</string>
		<string>accessibility-enhanced</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>iOS26Features</key>
	<dict>
		<key>AdvancedHealthKitEnabled</key>
		<true/>
		<key>AppleIntelligenceEnabled</key>
		<true/>
		<key>EnhancedAccessibilityEnabled</key>
		<true/>
		<key>NeurodiversityOptimizationsEnabled</key>
		<true/>
		<key>SwiftUI6Enabled</key>
		<true/>
	</dict>
</dict>
</plist>
