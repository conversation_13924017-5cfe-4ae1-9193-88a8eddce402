import SwiftUI

// MARK: - AI Task Coach View

/// Main view for the AI Task Coach feature with neurodiversity-optimized interface
/// Provides personalized task management with cognitive load awareness
@available(iOS 26.0, *)
struct AITaskCoachView: View {
    
    // MARK: - Properties
    @StateObject private var viewModel: AITaskCoachViewModel
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings
    
    // UI State
    @State private var showingFilters = false
    @State private var showingNewTaskSheet = false
    @State private var selectedTaskForBreakdown: AITask?
    
    // MARK: - Initialization
    init(viewModel: AITaskCoachViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    var body: some View {
        NavigationStack {
            ZStack {
                // Background
                NeuroNexaDesignSystem.Colors.backgroundColor(for: sensoryPreferences.colorContrast)
                    .ignoresSafeArea()
                
                // Main content
                VStack(spacing: adaptiveSpacing) {
                    // Header section
                    headerSection
                    
                    // Cognitive load indicator
                    cognitiveLoadIndicator
                    
                    // Adaptive recommendations
                    if !viewModel.adaptiveRecommendations.isEmpty {
                        adaptiveRecommendationsSection
                    }
                    
                    // Task list
                    taskListSection
                }
                .padding(.horizontal, adaptivePadding)
                
                // Loading overlay
                if viewModel.isGeneratingTasks {
                    loadingOverlay
                }
            }
            .navigationTitle("AI Task Coach")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    toolbarButtons
                }
            }
            .sheet(isPresented: $showingFilters) {
                Text("Filters - Implementation pending")
            }
            .sheet(isPresented: $showingNewTaskSheet) {
                Text("New Task - Implementation pending")
            }
            .sheet(item: $selectedTaskForBreakdown) { task in
                Text("Task Breakdown - Implementation pending")
            }
            .alert("Error", isPresented: $viewModel.showingError) {
                Button("OK") { }
            } message: {
                Text(viewModel.lastError?.localizedDescription ?? "An unknown error occurred")
            }
            .task {
                await viewModel.loadInitialData()
            }
            .refreshable {
                await viewModel.refreshTasks()
            }
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Welcome message
            Text(welcomeMessage)
                .font(adaptiveHeaderFont)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // Task statistics
            taskStatsView
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    private var cognitiveLoadIndicator: some View {
        HStack(spacing: 12) {
            // Cognitive load icon
            Image(systemName: cognitiveLoadIcon)
                .foregroundColor(cognitiveLoadColor)
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Current Cognitive Load")
                    .font(adaptiveCaptionFont)
                    .foregroundColor(.secondary)
                
                Text(viewModel.currentCognitiveLoad.rawValue)
                    .font(adaptiveBodyFont)
                    .fontWeight(.medium)
                    .foregroundColor(cognitiveLoadColor)
            }
            
            Spacer()
            
            // Cognitive load meter
            CognitiveLoadMeter(level: viewModel.currentCognitiveLoad)
        }
        .padding(adaptivePadding)
        .background(
            RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                .fill(cognitiveLoadColor.opacity(0.1))
        )
        .overlay(
            RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                .stroke(cognitiveLoadColor.opacity(0.3), lineWidth: 1)
        )
    }
    
    private var adaptiveRecommendationsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recommendations")
                .font(adaptiveSubheadlineFont)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            ForEach(viewModel.adaptiveRecommendations, id: \.self) { recommendation in
                HStack(spacing: 8) {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    
                    Text(recommendation)
                        .font(adaptiveCaptionFont)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
        }
        .padding(adaptivePadding)
        .background(
            RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                .fill(Color.orange.opacity(0.1))
        )
    }
    
    private var taskListSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section header
            HStack {
                Text("Your Tasks")
                    .font(adaptiveSubheadlineFont)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if !viewModel.filteredAndSortedTasks.isEmpty {
                    Text("\(viewModel.filteredAndSortedTasks.count) tasks")
                        .font(adaptiveCaptionFont)
                        .foregroundColor(.secondary)
                }
            }
            
            // Task list
            if viewModel.isLoadingTasks {
                taskListSkeleton
            } else if viewModel.filteredAndSortedTasks.isEmpty {
                emptyStateView
            } else {
                taskList
            }
        }
    }
    
    private var taskList: some View {
        LazyVStack(spacing: adaptiveSpacing) {
            ForEach(viewModel.filteredAndSortedTasks) { task in
                TaskCard(
                    task: task,
                    onTap: {
                        viewModel.startCoachingSession(for: task)
                    },
                    onComplete: {
                        Task {
                            await viewModel.completeTask(task)
                        }
                    },
                    onEdit: {
                        selectedTaskForBreakdown = task
                    }
                )
                .contextMenu {
                    taskContextMenu(for: task)
                }
            }
        }
    }
    
    private var taskStatsView: some View {
        HStack(spacing: 20) {
            StatItem(
                title: "Total",
                value: "\(viewModel.taskStats.total)",
                color: .blue
            )
            
            StatItem(
                title: "Completed",
                value: "\(viewModel.taskStats.completed)",
                color: .green
            )
            
            StatItem(
                title: "Pending",
                value: "\(viewModel.taskStats.pending)",
                color: .orange
            )
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No tasks yet")
                .font(adaptiveHeadlineFont)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("Let AI generate personalized tasks for you")
                .font(adaptiveBodyFont)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            CognitiveButton(
                "Generate Tasks",
                style: .primary,
                priority: .medium
            ) {
                Task {
                    await viewModel.generatePersonalizedTasks()
                }
            }
        }
        .padding(adaptivePadding * 2)
    }
    
    private var taskListSkeleton: some View {
        VStack(spacing: adaptiveSpacing) {
            ForEach(0..<3, id: \.self) { _ in
                TaskCardSkeleton()
            }
        }
    }
    
    private var loadingOverlay: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            VStack(spacing: 16) {
                ProgressView(value: viewModel.generationProgress)
                    .progressViewStyle(CognitiveProgressViewStyle())
                    .frame(width: 200)
                
                Text("Generating personalized tasks...")
                    .font(adaptiveBodyFont)
                    .foregroundColor(.white)
                
                Text("\(Int(viewModel.generationProgress * 100))%")
                    .font(adaptiveCaptionFont)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    private var toolbarButtons: some View {
        HStack {
            // Filter button
            Button {
                showingFilters = true
            } label: {
                Image(systemName: "line.3.horizontal.decrease.circle")
                    .foregroundColor(.primary)
            }
            .accessibilityLabel("Filter tasks")
            
            // Add task button
            Button {
                showingNewTaskSheet = true
            } label: {
                Image(systemName: "plus.circle")
                    .foregroundColor(.primary)
            }
            .accessibilityLabel("Add new task")
            
            // Refresh button
            Button {
                Task {
                    await viewModel.generatePersonalizedTasks()
                }
            } label: {
                Image(systemName: "arrow.clockwise.circle")
                    .foregroundColor(.primary)
            }
            .accessibilityLabel("Generate new tasks")
        }
    }
    
    @ViewBuilder
    private func taskContextMenu(for task: AITask) -> some View {
        Button {
            Task {
                await viewModel.completeTask(task)
            }
        } label: {
            Label("Complete", systemImage: "checkmark.circle")
        }
        .disabled(task.isCompleted)
        
        Button {
            selectedTaskForBreakdown = task
        } label: {
            Label("Break Down", systemImage: "list.bullet.indent")
        }
        
        Menu("Change Priority") {
            ForEach(TaskPriority.allCases, id: \.self) { priority in
                Button(priority.rawValue) {
                    Task {
                        await viewModel.updateTaskPriority(task, priority: priority)
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var welcomeMessage: String {
        let hour = Calendar.current.component(.hour, from: Date())
        let greeting = switch hour {
        case 5..<12: "Good morning"
        case 12..<17: "Good afternoon"
        case 17..<22: "Good evening"
        default: "Hello"
        }
        
        return "\(greeting)! Ready to tackle your tasks?"
    }
    
    private var cognitiveLoadIcon: String {
        switch viewModel.currentCognitiveLoad {
        case .low: return "brain"
        case .medium: return "brain.head.profile"
        case .high: return "exclamationmark.triangle"
        case .overload: return "exclamationmark.octagon"
        }
    }
    
    private var cognitiveLoadColor: Color {
        switch viewModel.currentCognitiveLoad {
        case .low: return .green
        case .medium: return .blue
        case .high: return .orange
        case .overload: return .red
        }
    }
    
    private var adaptiveSpacing: CGFloat {
        NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: NeuroNexaDesignSystem.Spacing.md,
            cognitiveLoad: cognitiveLoad
        )
    }
    
    private var adaptivePadding: CGFloat {
        NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: 16,
            cognitiveLoad: cognitiveLoad
        )
    }
    
    private var adaptiveCornerRadius: CGFloat {
        NeuroNexaDesignSystem.CornerRadius.adaptive(for: cognitiveLoad)
    }
    
    private var adaptiveHeaderFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: NeuroNexaDesignSystem.Typography.title2,
            cognitiveLoad: cognitiveLoad
        )
    }
    
    private var adaptiveHeadlineFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: NeuroNexaDesignSystem.Typography.headline,
            cognitiveLoad: cognitiveLoad
        )
    }
    
    private var adaptiveSubheadlineFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: .subheadline,
            cognitiveLoad: cognitiveLoad
        )
    }
    
    private var adaptiveBodyFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: NeuroNexaDesignSystem.Typography.body,
            cognitiveLoad: cognitiveLoad
        )
    }
    
    private var adaptiveCaptionFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: NeuroNexaDesignSystem.Typography.caption,
            cognitiveLoad: cognitiveLoad
        )
    }
}

// MARK: - Supporting Views

struct StatItem: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct CognitiveLoadMeter: View {
    let level: CognitiveLoadLevel
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<4, id: \.self) { index in
                RoundedRectangle(cornerRadius: 2)
                    .fill(index < levelValue ? levelColor : Color.secondary.opacity(0.3))
                    .frame(width: 6, height: 20)
            }
        }
    }
    
    private var levelValue: Int {
        switch level {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        case .overload: return 4
        }
    }
    
    private var levelColor: Color {
        switch level {
        case .low: return .green
        case .medium: return .blue
        case .high: return .orange
        case .overload: return .red
        }
    }
}

struct TaskCardSkeleton: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.secondary.opacity(0.3))
                    .frame(width: 4, height: 40)
                
                VStack(alignment: .leading, spacing: 8) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.secondary.opacity(isAnimating ? 0.3 : 0.1))
                        .frame(height: 20)
                    
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.secondary.opacity(isAnimating ? 0.2 : 0.05))
                        .frame(height: 16)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .scaleEffect(x: 0.7, anchor: .leading)
                }
                
                Spacer()
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondary.opacity(0.05))
        )
        .onAppear {
            withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                isAnimating = true
            }
        }
    }
}

// MARK: - Preview

#if DEBUG
@available(iOS 26.0, *)
struct AITaskCoachView_Previews: PreviewProvider {
    static var previews: some View {
        let mockViewModel = AITaskCoachViewModel(
            aiTaskCoachService: AITaskCoachService(),
            cognitiveLoadService: CognitiveLoadService(),
            userRepository: UserRepository(),
            taskRepository: TaskRepository()
        )
        
        AITaskCoachView(viewModel: mockViewModel)
            .environment(\.cognitiveLoadLevel, .medium)
            .environment(\.sensoryPreferences, .default)
            .environment(\.accessibilitySettings, .default)
    }
}
#endif
