warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/34)
Linting 'NeuroNexaApp.swift' (2/34)
Linting 'AppDelegate.swift' (3/34)
Linting 'SceneDelegate.swift' (4/34)
Linting 'NeurodiversityModifiers.swift' (5/34)
Linting 'iOS26Extensions.swift' (6/34)
Linting 'CognitiveProgressViewStyle.swift' (8/34)
Linting 'CognitiveButton.swift' (9/34)
Linting 'TaskCardConfiguration.swift' (10/34)
Linting 'NeuroNexaDesignSystem.swift' (7/34)
Linting 'TaskCard.swift' (11/34)
Linting 'SettingsView.swift' (12/34)
Linting 'BreathingView.swift' (13/34)
Linting 'DashboardView.swift' (14/34)
Linting 'ContentView.swift' (15/34)
Linting 'NeuroNexaModels.swift' (16/34)
Linting 'NeurodiversityEnums.swift' (17/34)
Linting 'TaskEnums.swift' (18/34)
Linting 'User.swift' (19/34)
Linting 'SensoryEnums.swift' (20/34)
Linting 'BreathingEnums.swift' (21/34)
Linting 'WellnessEnums.swift' (22/34)
Linting 'DependencyContainer.swift' (23/34)
Linting 'ViewModel.swift' (24/34)
Linting 'Coordinator.swift' (25/34)
Linting 'OpenAITaskCoach.swift' (26/34)
Linting 'AuthenticationService.swift' (27/34)
Linting 'NeuroNexaUITests.swift' (28/34)
Linting 'NeuroNexaTests.swift' (29/34)
Linting 'NeurodiversityAccessibilityTests.swift' (30/34)
Linting 'AccessibilityAuditTests.swift' (31/34)
Linting 'BreathingExerciseServiceTests.swift' (32/34)
Linting 'BreathingSessionTests.swift' (33/34)
Linting 'AITaskCoachServiceTests.swift' (34/34)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/User/User.swift",
    "line" : 36,
    "reason" : "Limit vertical whitespace to a single empty line; currently 5",
    "rule_id" : "vertical_whitespace",
    "severity" : "Warning",
    "type" : "Vertical Whitespace"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 249,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 249,
    "reason" : "Limit vertical whitespace to a single empty line; currently 2",
    "rule_id" : "vertical_whitespace",
    "severity" : "Warning",
    "type" : "Vertical Whitespace"
  },
  {
    "character" : 18,
    "file" : "/Users/<USER>/Neuronexa/Tests/NeuroNexaTests/NeuroNexaTests.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Architecture/DependencyContainer.swift",
    "line" : 1,
    "reason" : "Files should not contain leading whitespace",
    "rule_id" : "leading_whitespace",
    "severity" : "Warning",
    "type" : "Leading Whitespace"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 290,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 609,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 374,
    "reason" : "All views with tap gestures added should include the .isButton or the .isLink accessibility traits",
    "rule_id" : "accessibility_trait_for_button",
    "severity" : "Warning",
    "type" : "Accessibility Trait for Button"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 659,
    "reason" : "File should contain 500 lines or less: currently contains 659",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 8,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 476 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 9,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 61,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 296,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 271,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 276,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 62,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 295,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 18,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 4,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  }
]
Done linting! Found 16 violations, 0 serious in 34 files.
