warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/27)
Linting 'NeuroNexaApp.swift' (2/27)
Linting 'SceneDelegate.swift' (3/27)
Linting 'AppDelegate.swift' (4/27)
Linting 'NeurodiversityModifiers.swift' (5/27)
Linting 'iOS26Extensions.swift' (6/27)
Linting 'NeuroNexaDesignSystem.swift' (7/27)
Linting 'CognitiveButton.swift' (8/27)
Linting 'TaskCard.swift' (9/27)
Linting 'SettingsView.swift' (10/27)
Linting 'BreathingView.swift' (11/27)
Linting 'DashboardView.swift' (12/27)
Linting 'ContentView.swift' (13/27)
Linting 'AITaskCoachView.swift' (14/27)
Linting 'User.swift' (16/27)
Linting 'NeuroNexaModels.swift' (15/27)
Linting 'NeuroNexaEnums.swift' (17/27)
Linting 'NeuroNexaUITests.swift' (19/27)
Linting 'DependencyContainer.swift' (18/27)
Linting 'AppleIntelligenceIntegration.swift' (20/27)
Linting 'AuthenticationService.swift' (21/27)
Linting 'Coordinator.swift' (22/27)
Linting 'NeuroNexaTests.swift' (23/27)
Linting 'AccessibilityAuditTests.swift' (24/27)
Linting 'BreathingExerciseServiceTests.swift' (25/27)
Linting 'ViewModel.swift' (27/27)
Linting 'AITaskCoachServiceTests.swift' (26/27)
[
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/App/SceneDelegate.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/UI/Styles/NeuroNexaDesignSystem.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Styles/NeuroNexaDesignSystem.swift",
    "line" : 9,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 181,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 192,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 196,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 294,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 299,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 307,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 311,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 315,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 319,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 261,
    "reason" : "Combine multiple pattern matching bindings by moving keywords out of tuples",
    "rule_id" : "pattern_matching_keywords",
    "severity" : "Warning",
    "type" : "Pattern Matching Keywords"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 261,
    "reason" : "Combine multiple pattern matching bindings by moving keywords out of tuples",
    "rule_id" : "pattern_matching_keywords",
    "severity" : "Warning",
    "type" : "Pattern Matching Keywords"
  },
  {
    "character" : 57,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 261,
    "reason" : "Combine multiple pattern matching bindings by moving keywords out of tuples",
    "rule_id" : "pattern_matching_keywords",
    "severity" : "Warning",
    "type" : "Pattern Matching Keywords"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/iOS26Extensions.swift",
    "line" : 3,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 25,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 165,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 240,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 244,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 86,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 212,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 504,
    "reason" : "File should contain 500 lines or less: currently contains 504",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 32,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 485,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard.swift",
    "line" : 7,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 331 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : 128,
    "file" : "/Users/<USER>/Neuronexa/App/AppDelegate.swift",
    "line" : 5,
    "reason" : "Colons should be next to the identifier when specifying a type and next to the key in dictionary literals",
    "rule_id" : "colon",
    "severity" : "Warning",
    "type" : "Colon Spacing"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/App/AppDelegate.swift",
    "line" : 7,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 42,
    "file" : "/Users/<USER>/Neuronexa/UI/Modifiers/NeurodiversityModifiers.swift",
    "line" : 34,
    "reason" : "Unused parameter in a closure should be replaced with _",
    "rule_id" : "unused_closure_parameter",
    "severity" : "Warning",
    "type" : "Unused Closure Parameter"
  },
  {
    "character" : 69,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 182,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 78,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 183,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 71,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 184,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 66,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 185,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 75,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 196,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/App/NeuroNexaApp.swift",
    "line" : 4,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 27,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 204,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 30,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 205,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 28,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 206,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 30,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 207,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 67,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 178,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 193,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 198,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 227,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 53,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 62,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 71,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 80,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 17,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 89,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 21,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 244,
    "reason" : "All interactive elements should have accessibility labels",
    "rule_id" : "accessibility_label_required",
    "severity" : "Warning",
    "type" : "Accessibility Label Required"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/AppleIntelligenceIntegration.swift",
    "line" : 131,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/AppleIntelligenceIntegration.swift",
    "line" : 142,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/AppleIntelligenceIntegration.swift",
    "line" : 248,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/AppleIntelligenceIntegration.swift",
    "line" : 313,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 94,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/AppleIntelligenceIntegration.swift",
    "line" : 250,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 32,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/AppleIntelligenceIntegration.swift",
    "line" : 251,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/AppleIntelligenceIntegration.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 31,
    "reason" : "Prefer to locate parameters with defaults toward the end of the parameter list",
    "rule_id" : "function_default_parameter_at_end",
    "severity" : "Warning",
    "type" : "Function Default Parameter at End"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 147,
    "reason" : "Prefer to locate parameters with defaults toward the end of the parameter list",
    "rule_id" : "function_default_parameter_at_end",
    "severity" : "Warning",
    "type" : "Function Default Parameter at End"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 182,
    "reason" : "Prefer to locate parameters with defaults toward the end of the parameter list",
    "rule_id" : "function_default_parameter_at_end",
    "severity" : "Warning",
    "type" : "Function Default Parameter at End"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 215,
    "reason" : "Prefer to locate parameters with defaults toward the end of the parameter list",
    "rule_id" : "function_default_parameter_at_end",
    "severity" : "Warning",
    "type" : "Function Default Parameter at End"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 258,
    "reason" : "Prefer to locate parameters with defaults toward the end of the parameter list",
    "rule_id" : "function_default_parameter_at_end",
    "severity" : "Warning",
    "type" : "Function Default Parameter at End"
  },
  {
    "character" : 43,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 151,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 3,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaModels.swift",
    "line" : 4,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 18,
    "file" : "/Users/<USER>/Neuronexa/Tests/NeuroNexaTests/NeuroNexaTests.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Tests/NeuroNexaTests/NeuroNexaTests.swift",
    "line" : 6,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/AITaskCoach/AITaskCoachView.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 20,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaEnums.swift",
    "line" : 220,
    "reason" : "All elements in a collection literal should be vertically aligned",
    "rule_id" : "collection_alignment",
    "severity" : "Warning",
    "type" : "Collection Element Alignment"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaEnums.swift",
    "line" : 597,
    "reason" : "File should contain 500 lines or less: currently contains 597",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaEnums.swift",
    "line" : 144,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 28,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaEnums.swift",
    "line" : 145,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 35,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaEnums.swift",
    "line" : 145,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 38,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/NeuroNexaEnums.swift",
    "line" : 146,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Tests/NeuroNexaUITests/NeuroNexaUITests.swift",
    "line" : 5,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 531,
    "reason" : "File should contain 500 lines or less: currently contains 531",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 419,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 437,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 449,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 453,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 457,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 461,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 466,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 471,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 475,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 480,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 484,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 488,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 492,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 496,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 500,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 504,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 517,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 524,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 529,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 32,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 423,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 3,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 50,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 52,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 54,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 71,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 73,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 94,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 112,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 114,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 116,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 134,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 136,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 138,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 157,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 159,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 161,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 178,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 180,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 197,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 199,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 214,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 216,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 221,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 226,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 29,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 241,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 243,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 29,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 259,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 261,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 271,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 273,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 288,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 290,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 304,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 36,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 306,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 326,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 328,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 351,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 353,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 368,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 370,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 393,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 24,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 407,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 411,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 413,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Tests/Accessibility/AccessibilityAuditTests.swift",
    "line" : 12,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Core/Architecture/Coordinator.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 27,
    "file" : "/Users/<USER>/Neuronexa/Core/Models/User/User.swift",
    "line" : 30,
    "reason" : "Variables should not have redundant type annotation",
    "rule_id" : "redundant_type_annotation",
    "severity" : "Warning",
    "type" : "Redundant Type Annotation"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 503,
    "reason" : "File should contain 500 lines or less: currently contains 503",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 71,
    "reason" : "Force tries should be avoided",
    "rule_id" : "force_try",
    "severity" : "Error",
    "type" : "Force Try"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 104,
    "reason" : "Force tries should be avoided",
    "rule_id" : "force_try",
    "severity" : "Error",
    "type" : "Force Try"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 121,
    "reason" : "Force tries should be avoided",
    "rule_id" : "force_try",
    "severity" : "Error",
    "type" : "Force Try"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 250,
    "reason" : "Force tries should be avoided",
    "rule_id" : "force_try",
    "severity" : "Error",
    "type" : "Force Try"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 276,
    "reason" : "Force tries should be avoided",
    "rule_id" : "force_try",
    "severity" : "Error",
    "type" : "Force Try"
  },
  {
    "character" : 65,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 258,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 52,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 284,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 345,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 360,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 501,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 60,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 62,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 95,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 97,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 136,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 151,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 153,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 166,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 168,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 181,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 183,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 239,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 241,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 316,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 318,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/BreathingExerciseServiceTests.swift",
    "line" : 12,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 92,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 131,
    "reason" : "Force unwrapping should be avoided",
    "rule_id" : "force_unwrapping",
    "severity" : "Warning",
    "type" : "Force Unwrapping"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 304,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 319,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 343,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 356,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 373,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 384,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 419,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 431,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 435,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 447,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 66,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 63,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 54,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 171,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 32,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 191,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 43,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 353,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 62,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 64,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 82,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 84,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 98,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 100,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 116,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 130,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 132,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 145,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 147,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 162,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 164,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 166,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 202,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 23,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 204,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 33,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 209,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 226,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 26,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 269,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 22,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 295,
    "reason" : "Function parameters should be aligned vertically if they're in multiple lines in a method call",
    "rule_id" : "vertical_parameter_alignment_on_call",
    "severity" : "Warning",
    "type" : "Vertical Parameter Alignment on Call"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Tests/Services/AITaskCoachServiceTests.swift",
    "line" : 11,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/Core/Architecture/ViewModel.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  }
]
Done linting! Found 212 violations, 5 serious in 27 files.
