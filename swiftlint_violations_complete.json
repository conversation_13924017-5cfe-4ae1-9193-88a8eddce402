warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppDelegate.swift' (3/38)
Linting 'AppConfiguration.swift' (1/38)
Linting 'NeuroNexaApp.swift' (2/38)
Linting 'SceneDelegate.swift' (4/38)
Linting 'NeurodiversityModifiers.swift' (5/38)
Linting 'iOS26Extensions.swift' (6/38)
Linting 'CognitiveProgressViewStyle.swift' (7/38)
Linting 'NeuroNexaDesignSystem.swift' (8/38)
Linting 'TaskCardConfiguration.swift' (9/38)
Linting 'CognitiveButton.swift' (10/38)
Linting 'TaskCard.swift' (11/38)
Linting 'SettingsView.swift' (12/38)
Linting 'BreathingHelpers.swift' (14/38)
Linting 'ContentView.swift' (13/38)
Linting 'BreathingOverlayViews.swift' (16/38)
Linting 'DashboardView.swift' (17/38)
Linting 'BreathingView.swift' (15/38)
Linting 'AnxietyDetectionSheet.swift' (18/38)
Linting 'BreathingSupportingViews.swift' (19/38)
Linting 'NeurodiversityEnums.swift' (20/38)
Linting 'NeuroNexaModels.swift' (21/38)
Linting 'User.swift' (23/38)
Linting 'TaskEnums.swift' (22/38)
Linting 'SensoryEnums.swift' (24/38)
Linting 'BreathingEnums.swift' (25/38)
Linting 'WellnessEnums.swift' (26/38)
Linting 'Coordinator.swift' (29/38)
Linting 'DependencyContainer.swift' (27/38)
Linting 'OpenAITaskCoach.swift' (30/38)
Linting 'ViewModel.swift' (28/38)
Linting 'NeuroNexaTests.swift' (32/38)
Linting 'AuthenticationService.swift' (33/38)
Linting 'NeuroNexaUITests.swift' (31/38)
Linting 'AccessibilityAuditTests.swift' (34/38)
Linting 'BreathingExerciseServiceTests.swift' (36/38)
Linting 'NeurodiversityAccessibilityTests.swift' (35/38)
Linting 'BreathingSessionTests.swift' (37/38)
Linting 'AITaskCoachServiceTests.swift' (38/38)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Architecture/DependencyContainer.swift",
    "line" : 1,
    "reason" : "Files should not contain leading whitespace",
    "rule_id" : "leading_whitespace",
    "severity" : "Warning",
    "type" : "Leading Whitespace"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 8,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 319 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  }
]
Done linting! Found 2 violations, 0 serious in 38 files.
