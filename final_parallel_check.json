warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/120)
Linting 'NeuroNexaApp.swift' (2/120)
Linting 'AppDelegate.swift' (3/120)
Linting 'SceneDelegate.swift' (4/120)
Linting 'SettingsViewModel.swift' (5/120)
Linting 'BreathingViewModel.swift' (6/120)
Linting 'AITaskCoachViewModel.swift' (7/120)
Linting 'DashboardViewModel.swift' (8/120)
Linting 'iOS26Extensions.swift' (9/120)
Linting 'NeuroNexaDesignSystem.swift' (10/120)
Linting 'CognitiveProgressViewStyle.swift' (11/120)
Linting 'TaskCardConfiguration.swift' (12/120)
Linting 'BreathingSupportingViews.swift' (15/120)
Linting 'TaskCard.swift' (13/120)
Linting 'CognitiveButton.swift' (14/120)
Linting 'BreathingHelpers.swift' (16/120)
Linting 'AnxietyDetectionSheet.swift' (18/120)
Linting 'BreathingOverlayViews.swift' (17/120)
Linting 'BreathingContentViews.swift' (19/120)
Linting 'UI/Views/DashboardView.swift' (20/120)
Linting 'UI/Views/Settings/SettingsView.swift' (21/120)
Linting 'UI/Views/SettingsView.swift' (22/120)
Linting 'UI/Views/BreathingView.swift' (23/120)
Linting 'AITaskCoachView.swift' (24/120)
Linting 'UI/Views/Breathing/BreathingView.swift' (25/120)
Linting 'ContentView.swift' (26/120)
Linting 'TaskRepository.swift' (27/120)
Linting 'UserRepository.swift' (28/120)
Linting 'UI/Views/Dashboard/DashboardView.swift' (29/120)
Linting 'RoutineRepository.swift' (30/120)
Linting 'UserProfileRepository.swift' (31/120)
Linting 'BreathingSessionRepository.swift' (32/120)
Linting 'NeurodiversityEnums.swift' (33/120)
Linting 'BehaviorModels.swift' (34/120)
Linting 'OpenAIModels.swift' (35/120)
Linting 'CognitivePatternModels.swift' (36/120)
Linting 'SettingsModels.swift' (37/120)
Linting 'TaskEnums.swift' (39/120)
Linting 'BehaviorPredictionModels.swift' (40/120)
Linting 'NeuroNexaModels.swift' (38/120)
Linting 'UserPreferences.swift' (41/120)
Linting 'TaskTimingModels.swift' (42/120)
Linting 'BreathingModels.swift' (43/120)
Linting 'ViewPlaceholders.swift' (44/120)
Linting 'OpenAIUserContextModels.swift' (45/120)
Linting 'NeurodiversityServices.swift' (46/120)
Linting 'OpenAITaskModels.swift' (47/120)
Linting 'ExecutiveFunctionModels.swift' (48/120)
Linting 'AccessibilitySettings.swift' (49/120)
Linting 'OpenAIBreakModels.swift' (50/120)
Linting 'OpenAITypes.swift' (51/120)
Linting 'SensoryOptimizationModels.swift' (52/120)
Linting 'OpenAICoachModels.swift' (53/120)
Linting 'User.swift' (54/120)
Linting 'CognitiveOptimizationModels.swift' (55/120)
Linting 'SensoryEnums.swift' (56/120)
Linting 'OpenAICognitiveAdaptationModels.swift' (57/120)
Linting 'PersonalizedContentTypes.swift' (58/120)
Linting 'CognitivePreferencesModels.swift' (59/120)
Linting 'NeuroNexaServices.swift' (60/120)
Linting 'NeuroNexaTheme.swift' (61/120)
Linting 'NeurodiversityTypes.swift' (62/120)
Linting 'CognitiveSupportingTypes.swift' (63/120)
Linting 'SensoryModels.swift' (64/120)
Linting 'SharedTypes.swift' (65/120)
Linting 'UserProfileModels.swift' (66/120)
Linting 'CognitiveModels.swift' (67/120)
Linting 'OpenAIContentModels.swift' (68/120)
Linting 'OpenAITaskCoachModels.swift' (69/120)
Linting 'BreakSuggestionTypes.swift' (70/120)
Linting 'SensoryCognitiveModels.swift' (71/120)
Linting 'CognitiveAnalysisModels.swift' (72/120)
Linting 'CognitiveAdaptationTypes.swift' (73/120)
Linting 'SensoryAdaptationModels.swift' (74/120)
Linting 'TaskModels.swift' (75/120)
Linting 'BreathingEnums.swift' (76/120)
Linting 'PrivacySettings.swift' (77/120)
Linting 'BehaviorInsightsModels.swift' (79/120)
Linting 'BehaviorAnalysisModels.swift' (80/120)
Linting 'DependencyContainer.swift' (81/120)
Linting 'ViewModel.swift' (82/120)
Linting 'Coordinator.swift' (83/120)
Linting 'ServiceProtocols.swift' (84/120)
Linting 'WellnessEnums.swift' (78/120)
Linting 'CognitiveLoadService.swift' (85/120)
Linting 'CognitiveAnalysisServiceHelpers.swift' (86/120)
Linting 'CognitiveAnalysisServiceExtensions.swift' (87/120)
Linting 'CoreDataService.swift' (88/120)
Linting 'ExecutiveFunctionService.swift' (89/120)
Linting 'UserService.swift' (90/120)
Linting 'SensoryAdaptationService.swift' (91/120)
Linting 'PersonalizedTaskServiceExtensions.swift' (92/120)
Linting 'BreathingServiceHelpers.swift' (93/120)
Linting 'WatchConnectivityService.swift' (94/120)
Linting 'HealthKitService.swift' (95/120)
Linting 'BasicServiceImplementations.swift' (96/120)
Linting 'OpenAIErrors.swift' (97/120)
Linting 'OpenAIService.swift' (98/120)
Linting 'OpenAITaskCoachParsing.swift' (99/120)
Linting 'OpenAITaskCoach.swift' (100/120)
Linting 'OpenAITaskCoachHelpers.swift' (101/120)
Linting 'OpenAITaskCoachPrompts.swift' (102/120)
Linting 'OpenAITaskCoachExtensions.swift' (103/120)
Linting 'PersonalizedTaskHelpers.swift' (104/120)
Linting 'CognitiveAnalysisService.swift' (105/120)
Linting 'CognitiveAnalysisHelpers.swift' (106/120)
Linting 'PersonalizedTaskService.swift' (107/120)
Linting 'BreathingService.swift' (108/120)
Linting 'SettingsService.swift' (109/120)
Linting 'AuthenticationService.swift' (110/120)
Linting 'CloudKitService.swift' (111/120)
Linting 'PersonalizedTaskServiceGeneration.swift' (112/120)
Linting 'NeuroNexaUITests.swift' (113/120)
Linting 'NeuroNexaTests.swift' (114/120)
Linting 'NeurodiversityAccessibilityTests.swift' (115/120)
Linting 'AccessibilityAuditTests.swift' (116/120)
Linting 'BreathingExerciseServiceTests.swift' (117/120)
Linting 'AITaskCoachServiceTestsExtensions.swift' (118/120)
Linting 'BreathingSessionTests.swift' (119/120)
Linting 'AITaskCoachServiceTests.swift' (120/120)
[
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 216,
    "reason" : "Variable name 'n' should be between 2 and 50 characters long",
    "rule_id" : "identifier_name",
    "severity" : "Warning",
    "type" : "Identifier Name"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 227,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 49,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 18,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 54,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 33,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 8,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 423,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "File should contain 500 lines or less: currently contains 501",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 320,
    "reason" : "`where` clauses are preferred over a single `if` inside a `for`",
    "rule_id" : "for_where",
    "severity" : "Warning",
    "type" : "Prefer For-Where"
  },
  {
    "character" : 67,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 202,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 37,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 262,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 41,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 264,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  }
]
Done linting! Found 12 violations, 0 serious in 120 files.
