import SwiftUI
import XCTest

@testable import NeuroNexa

/// Enterprise-level UI testing suite for NeuroNexa iOS 26
/// Comprehensive testing across all user interfaces and interactions
@available(iOS 26.0, *)
class EnterpriseUITestSuite: XCTestCase {
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        // Put setup code here. This method is called before the invocation of each test method in the class.
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launchEnvironment = [
            "UITEST_DISABLE_ANIMATIONS": "1",
            "UITEST_MOCK_DATA": "1",
            "UITEST_ACCESSIBILITY_ENABLED": "1"
        ]
        app.launch()
    }
    
    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
        app.terminate()
        app = nil
    }
    
    // MARK: - Dashboard UI Tests
    
    func testDashboardLoadingAndNavigation() throws {
        // Test dashboard loads properly
        let dashboardTitle = app.navigationBars["Dashboard"]
        XCTAssertTrue(dashboardTitle.waitForExistence(timeout: 5))
        
        // Test cognitive load indicator is visible
        let cognitiveLoadIndicator = app.staticTexts["Cognitive Load"]
        XCTAssertTrue(cognitiveLoadIndicator.exists)
        
        // Test quick actions are accessible
        let aiCoachButton = app.buttons["AI Coach"]
        XCTAssertTrue(aiCoachButton.exists)
        XCTAssertTrue(aiCoachButton.isEnabled)
        
        let breathingButton = app.buttons["Breathe"]
        XCTAssertTrue(breathingButton.exists)
        XCTAssertTrue(breathingButton.isEnabled)
        
        // Test accessibility labels
        XCTAssertEqual(aiCoachButton.label, "AI Coach")
        XCTAssertEqual(breathingButton.label, "Breathe")
    }
    
    func testDashboardInteractions() throws {
        // Test AI Coach navigation
        let aiCoachButton = app.buttons["AI Coach"]
        aiCoachButton.tap()
        
        let aiCoachView = app.navigationBars["AI Task Coach"]
        XCTAssertTrue(aiCoachView.waitForExistence(timeout: 3))
        
        // Navigate back
        let backButton = app.navigationBars.buttons.element(boundBy: 0)
        backButton.tap()
        
        // Test breathing exercise navigation
        let breathingButton = app.buttons["Breathe"]
        breathingButton.tap()
        
        let breathingView = app.navigationBars["Breathing"]
        XCTAssertTrue(breathingView.waitForExistence(timeout: 3))
    }
    
    // MARK: - AI Task Coach UI Tests
    
    func testAITaskCoachInterface() throws {
        // Navigate to AI Task Coach
        app.buttons["AI Coach"].tap()
        
        // Test task list is visible
        let taskList = app.tables.firstMatch
        XCTAssertTrue(taskList.exists)
        
        // Test task creation
        let addTaskButton = app.buttons["Add Task"]
        if addTaskButton.exists {
            addTaskButton.tap()
            
            // Test task creation form
            let titleField = app.textFields["Task Title"]
            XCTAssertTrue(titleField.waitForExistence(timeout: 2))
            
            titleField.tap()
            titleField.typeText("Test Task")
            
            let saveButton = app.buttons["Save"]
            if saveButton.exists {
                saveButton.tap()
                
                // Verify task was created
                let newTask = app.staticTexts["Test Task"]
                XCTAssertTrue(newTask.waitForExistence(timeout: 3))
            }
        }
    }
    
    func testTaskManagement() throws {
        // Navigate to AI Task Coach
        app.buttons["AI Coach"].tap()
        
        // Test task completion
        let firstTask = app.tables.cells.element(boundBy: 0)
        if firstTask.exists {
            let completeButton = firstTask.buttons["Complete"]
            if completeButton.exists {
                completeButton.tap()
                
                // Verify task completion feedback
                let completionAlert = app.alerts.firstMatch
                if completionAlert.exists {
                    XCTAssertTrue(completionAlert.waitForExistence(timeout: 2))
                    app.buttons["OK"].tap()
                }
            }
        }
    }
    
    // MARK: - Breathing Exercise UI Tests
    
    func testBreathingExerciseInterface() throws {
        // Navigate to breathing exercises
        app.buttons["Breathe"].tap()
        
        // Test breathing exercise selection
        let exerciseList = app.collectionViews.firstMatch
        XCTAssertTrue(exerciseList.exists)
        
        // Test selecting an exercise
        let firstExercise = exerciseList.cells.element(boundBy: 0)
        if firstExercise.exists {
            firstExercise.tap()
            
            // Test exercise details view
            let startButton = app.buttons["Start Exercise"]
            XCTAssertTrue(startButton.waitForExistence(timeout: 2))
            
            startButton.tap()
            
            // Test breathing session UI
            let breathingGuidance = app.staticTexts["Breathe In"]
            XCTAssertTrue(breathingGuidance.waitForExistence(timeout: 3))
            
            // Test pause functionality
            let pauseButton = app.buttons["Pause"]
            if pauseButton.exists {
                pauseButton.tap()
                
                let resumeButton = app.buttons["Resume"]
                XCTAssertTrue(resumeButton.waitForExistence(timeout: 2))
            }
        }
    }
    
    func testBreathingSessionFlow() throws {
        // Navigate to breathing exercises
        app.buttons["Breathe"].tap()
        
        // Select quick start exercise
        let quickStartButton = app.buttons["Quick Start"]
        if quickStartButton.exists {
            quickStartButton.tap()
            
            // Test session progress
            let progressView = app.progressIndicators.firstMatch
            XCTAssertTrue(progressView.exists)
            
            // Test session completion
            let endButton = app.buttons["End Session"]
            if endButton.exists {
                endButton.tap()
                
                // Verify session results
                let resultsView = app.staticTexts["Session Complete"]
                XCTAssertTrue(resultsView.waitForExistence(timeout: 3))
            }
        }
    }
    
    // MARK: - Settings UI Tests
    
    func testSettingsInterface() throws {
        // Navigate to settings
        let settingsButton = app.buttons["Settings"]
        if settingsButton.exists {
            settingsButton.tap()
            
            // Test settings categories
            let accessibilitySettings = app.cells["Accessibility"]
            XCTAssertTrue(accessibilitySettings.exists)
            
            let privacySettings = app.cells["Privacy"]
            XCTAssertTrue(privacySettings.exists)
            
            let neurodiversitySettings = app.cells["Neurodiversity"]
            XCTAssertTrue(neurodiversitySettings.exists)
        }
    }
    
    func testAccessibilitySettings() throws {
        // Navigate to settings
        app.buttons["Settings"].tap()
        
        // Test accessibility settings
        let accessibilityCell = app.cells["Accessibility"]
        accessibilityCell.tap()
        
        // Test VoiceOver toggle
        let voiceOverToggle = app.switches["VoiceOver Support"]
        if voiceOverToggle.exists {
            let initialState = voiceOverToggle.value as? String
            voiceOverToggle.tap()
            
            // Verify toggle state changed
            XCTAssertNotEqual(voiceOverToggle.value as? String, initialState)
        }
        
        // Test Dynamic Type slider
        let dynamicTypeSlider = app.sliders["Text Size"]
        if dynamicTypeSlider.exists {
            dynamicTypeSlider.adjust(toNormalizedSliderPosition: 0.8)
            
            // Verify text size changed
            let sampleText = app.staticTexts.firstMatch
            XCTAssertTrue(sampleText.exists)
        }
    }
    
    // MARK: - Cross-Device Compatibility Tests
    
    func testIPadInterface() throws {
        // Test iPad-specific UI elements
        if UIDevice.current.userInterfaceIdiom == .pad {
            // Test split view compatibility
            let splitView = app.otherElements["Split View"]
            if splitView.exists {
                XCTAssertTrue(splitView.exists)
            }
            
            // Test sidebar navigation
            let sidebar = app.otherElements["Sidebar"]
            if sidebar.exists {
                XCTAssertTrue(sidebar.exists)
            }
        }
    }
    
    func testLandscapeOrientation() throws {
        // Test landscape mode
        XCUIDevice.shared.orientation = .landscapeLeft
        
        // Wait for orientation change
        let expectation = XCTestExpectation(description: "Orientation change")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 2.0)
        
        // Test UI elements are still accessible
        let dashboardTitle = app.navigationBars["Dashboard"]
        XCTAssertTrue(dashboardTitle.exists)
        
        let aiCoachButton = app.buttons["AI Coach"]
        XCTAssertTrue(aiCoachButton.exists)
        
        // Reset orientation
        XCUIDevice.shared.orientation = .portrait
    }
    
    // MARK: - Accessibility Testing
    
    func testVoiceOverNavigation() throws {
        // Enable VoiceOver for testing
        app.launchEnvironment["UITEST_VOICEOVER_ENABLED"] = "1"
        
        // Test VoiceOver navigation
        let firstElement = app.otherElements.element(boundBy: 0)
        if firstElement.exists {
            // Test accessibility focus
            XCTAssertTrue(firstElement.isAccessibilityElement)
            
            // Test accessibility labels
            XCTAssertNotNil(firstElement.label)
            XCTAssertFalse(firstElement.label.isEmpty)
        }
    }
    
    func testAccessibilityTraits() throws {
        // Test button accessibility traits
        let aiCoachButton = app.buttons["AI Coach"]
        XCTAssertTrue(aiCoachButton.exists)
        
        // Verify button traits
        let traits = aiCoachButton.accessibilityTraits
        XCTAssertTrue(traits.contains(.button))
        
        // Test image accessibility
        let images = app.images
        for image in images.allElementsBoundByIndex {
            // All images should have accessibility labels or be hidden
            XCTAssertTrue(
                !image.label.isEmpty || 
                image.accessibilityTraits.contains(.notEnabled)
            )
        }
    }
    
    // MARK: - Performance Testing
    
    func testAppLaunchPerformance() throws {
        // Test app launch performance
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            let app = XCUIApplication()
            app.launch()
            app.terminate()
        }
    }
    
    func testScrollPerformance() throws {
        // Navigate to a scrollable view
        app.buttons["AI Coach"].tap()
        
        let taskList = app.tables.firstMatch
        if taskList.exists {
            // Test scroll performance
            measure(metrics: [XCTOSSignpostMetric.scrollingAndDecelerationMetric]) {
                taskList.swipeUp()
                taskList.swipeDown()
            }
        }
    }
    
    func testNavigationPerformance() throws {
        // Test navigation performance
        measure(metrics: [XCTOSSignpostMetric.navigationTransitionMetric]) {
            app.buttons["AI Coach"].tap()
            app.navigationBars.buttons.element(boundBy: 0).tap()
            
            app.buttons["Breathe"].tap()
            app.navigationBars.buttons.element(boundBy: 0).tap()
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testNetworkErrorHandling() throws {
        // Simulate network error
        app.launchEnvironment["UITEST_NETWORK_ERROR"] = "1"
        
        // Test error handling in AI Coach
        app.buttons["AI Coach"].tap()
        
        let errorAlert = app.alerts.firstMatch
        if errorAlert.exists {
            XCTAssertTrue(errorAlert.waitForExistence(timeout: 5))
            
            // Test error message is user-friendly
            let errorMessage = errorAlert.staticTexts.element(boundBy: 1)
            XCTAssertTrue(errorMessage.exists)
            XCTAssertFalse(errorMessage.label.contains("Error"))
            
            // Test retry functionality
            let retryButton = app.buttons["Retry"]
            if retryButton.exists {
                retryButton.tap()
            }
        }
    }
    
    func testOfflineMode() throws {
        // Test offline functionality
        app.launchEnvironment["UITEST_OFFLINE_MODE"] = "1"
        
        // Test basic functionality still works
        let dashboardTitle = app.navigationBars["Dashboard"]
        XCTAssertTrue(dashboardTitle.exists)
        
        // Test offline breathing exercises
        app.buttons["Breathe"].tap()
        
        let exerciseList = app.collectionViews.firstMatch
        XCTAssertTrue(exerciseList.exists)
    }
    
    // MARK: - Memory and Resource Tests
    
    func testMemoryUsage() throws {
        // Test memory usage during normal operation
        let memoryMetric = XCTMemoryMetric()
        
        measure(metrics: [memoryMetric]) {
            // Perform typical user actions
            app.buttons["AI Coach"].tap()
            app.navigationBars.buttons.element(boundBy: 0).tap()
            
            app.buttons["Breathe"].tap()
            app.navigationBars.buttons.element(boundBy: 0).tap()
        }
    }
    
    func testResourceCleanup() throws {
        // Test resource cleanup when navigating away
        app.buttons["Breathe"].tap()
        
        let firstExercise = app.collectionViews.firstMatch.cells.element(boundBy: 0)
        if firstExercise.exists {
            firstExercise.tap()
            
            let startButton = app.buttons["Start Exercise"]
            if startButton.exists {
                startButton.tap()
                
                // Navigate away during exercise
                app.navigationBars.buttons.element(boundBy: 0).tap()
                
                // Verify no resource leaks
                let dashboardTitle = app.navigationBars["Dashboard"]
                XCTAssertTrue(dashboardTitle.exists)
            }
        }
    }
    
    // MARK: - Security Tests
    
    func testScreenshotPrevention() throws {
        // Test screenshot prevention in sensitive areas
        app.launchEnvironment["UITEST_SENSITIVE_MODE"] = "1"
        
        // Navigate to sensitive area (e.g., health data)
        app.buttons["Settings"].tap()
        
        let privacyCell = app.cells["Privacy"]
        if privacyCell.exists {
            privacyCell.tap()
            
            // Test screenshot prevention is active
            let screenshotTest = app.otherElements["Screenshot Protected"]
            if screenshotTest.exists {
                XCTAssertTrue(screenshotTest.exists)
            }
        }
    }
    
    func testDataProtection() throws {
        // Test data protection measures
        app.launchEnvironment["UITEST_DATA_PROTECTION"] = "1"
        
        // Test sensitive data is not exposed in UI
        let sensitiveElements = app.otherElements.matching(identifier: "sensitive_data")
        XCTAssertEqual(sensitiveElements.count, 0)
    }
    
    // MARK: - Integration Tests
    
    func testHealthKitIntegration() throws {
        // Test HealthKit integration
        app.buttons["Breathe"].tap()
        
        let healthKitButton = app.buttons["Connect HealthKit"]
        if healthKitButton.exists {
            healthKitButton.tap()
            
            // Test HealthKit permission dialog
            let permissionDialog = app.alerts.firstMatch
            if permissionDialog.exists {
                XCTAssertTrue(permissionDialog.waitForExistence(timeout: 3))
                
                let allowButton = app.buttons["Allow"]
                if allowButton.exists {
                    allowButton.tap()
                }
            }
        }
    }
    
    func testWatchConnectivity() throws {
        // Test Apple Watch connectivity
        app.launchEnvironment["UITEST_WATCH_CONNECTED"] = "1"
        
        app.buttons["Breathe"].tap()
        
        let watchIndicator = app.staticTexts["Apple Watch Connected"]
        if watchIndicator.exists {
            XCTAssertTrue(watchIndicator.exists)
        }
    }
    
    // MARK: - Stress Testing
    
    func testRapidNavigation() throws {
        // Test rapid navigation between views
        for _ in 0..<10 {
            app.buttons["AI Coach"].tap()
            app.navigationBars.buttons.element(boundBy: 0).tap()
            
            app.buttons["Breathe"].tap()
            app.navigationBars.buttons.element(boundBy: 0).tap()
        }
        
        // Verify app is still responsive
        let dashboardTitle = app.navigationBars["Dashboard"]
        XCTAssertTrue(dashboardTitle.exists)
    }
    
    func testLongRunningSession() throws {
        // Test long-running breathing session
        app.buttons["Breathe"].tap()
        
        let firstExercise = app.collectionViews.firstMatch.cells.element(boundBy: 0)
        if firstExercise.exists {
            firstExercise.tap()
            
            let startButton = app.buttons["Start Exercise"]
            if startButton.exists {
                startButton.tap()
                
                // Let session run for extended period
                let expectation = XCTestExpectation(description: "Long session")
                DispatchQueue.main.asyncAfter(deadline: .now() + 30.0) {
                    expectation.fulfill()
                }
                wait(for: [expectation], timeout: 35.0)
                
                // Verify session is still active
                let progressView = app.progressIndicators.firstMatch
                XCTAssertTrue(progressView.exists)
            }
        }
    }
}

// MARK: - Helper Extensions

extension XCUIApplication {
    func waitForElementToAppear(_ element: XCUIElement, timeout: TimeInterval = 5.0) -> Bool {
        let predicate = NSPredicate(format: "exists == true")
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: element)
        
        let result = XCTWaiter().wait(for: [expectation], timeout: timeout)
        return result == .completed
    }
    
    func waitForElementToDisappear(_ element: XCUIElement, timeout: TimeInterval = 5.0) -> Bool {
        let predicate = NSPredicate(format: "exists == false")
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: element)
        
        let result = XCTWaiter().wait(for: [expectation], timeout: timeout)
        return result == .completed
    }
}
