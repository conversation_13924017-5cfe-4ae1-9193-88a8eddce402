name: NeuroNexa Environment Validation

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  validate-environment:
    name: Validate iOS 26 / Xcode Beta 26 Requirements
    runs-on: macos-latest
    
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
      
    - name: Setup Xcode Beta 26
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: '16.2-beta'  # Xcode Beta 26
        
    - name: Verify Xcode Version
      run: |
        echo "Checking Xcode version..."
        xcodebuild -version
        XCODE_VERSION=$(xcodebuild -version | head -n 1 | awk '{print $2}')
        if [[ ! $XCODE_VERSION == 16.2* ]]; then
          echo "❌ ERROR: Xcode Beta 26 (16.2.x) required, found: $XCODE_VERSION"
          exit 1
        fi
        echo "✅ Xcode Beta 26 verified: $XCODE_VERSION"
        
    - name: Verify iOS 26 SDK
      run: |
        echo "Checking iOS SDK availability..."
        xcodebuild -showsdks | grep iphoneos
        if ! xcodebuild -showsdks | grep -q "iphoneos18."; then
          echo "❌ ERROR: iOS 26 SDK not found"
          exit 1
        fi
        echo "✅ iOS 26 SDK available"
        
    - name: Validate Project Configuration
      run: |
        echo "Validating project deployment targets..."
        if [ ! -f "NeuroNexa.xcodeproj/project.pbxproj" ]; then
          echo "❌ ERROR: NeuroNexa.xcodeproj not found"
          exit 1
        fi
        
        # Check deployment targets
        DEPLOYMENT_TARGETS=$(grep "IPHONEOS_DEPLOYMENT_TARGET" NeuroNexa.xcodeproj/project.pbxproj | grep -o "[0-9]\+\.[0-9]\+" | sort -u)
        echo "Found deployment targets: $DEPLOYMENT_TARGETS"
        
        for target in $DEPLOYMENT_TARGETS; do
          if [[ $target != "26.0" ]]; then
            echo "❌ ERROR: Deployment target $target is not iOS 26.0"
            exit 1
          fi
        done
        echo "✅ All deployment targets set to iOS 26.0"
        
    - name: Run Environment Validation Script
      run: |
        chmod +x Scripts/validate-environment.sh
        ./Scripts/validate-environment.sh
        
    - name: Validate Build Configuration
      run: |
        echo "Testing build configuration..."
        xcodebuild -project NeuroNexa.xcodeproj -scheme NeuroNexa -destination 'platform=iOS Simulator,name=iPhone 16,OS=latest' clean build-for-testing
        echo "✅ Build configuration validated"
        
    - name: Environment Compliance Report
      run: |
        echo "📋 NeuroNexa Environment Compliance Report"
        echo "=========================================="
        echo "✅ Xcode Beta 26: $(xcodebuild -version | head -n 1)"
        echo "✅ iOS 26 SDK: Available"
        echo "✅ Deployment Target: iOS 26.0"
        echo "✅ Build Configuration: Valid"
        echo "✅ Environment Validation: PASSED"
        echo ""
        echo "🎉 Ready for NeuroNexa iOS 26 development!"

  enforce-compliance:
    name: Enforce Development Environment Compliance
    runs-on: macos-latest
    needs: validate-environment
    if: failure()
    
    steps:
    - name: Environment Compliance Failure
      run: |
        echo "❌ ENVIRONMENT COMPLIANCE FAILURE"
        echo "================================="
        echo ""
        echo "The NeuroNexa project requires:"
        echo "• Xcode Beta 26.0 EXCLUSIVELY"
        echo "• iOS 26.0 deployment target EXCLUSIVELY"
        echo "• No development on earlier iOS versions"
        echo ""
        echo "Please fix environment issues and retry."
        echo "Run './Scripts/validate-environment.sh' locally for detailed diagnostics."
        exit 1
