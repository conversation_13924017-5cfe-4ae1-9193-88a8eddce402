name: iOS CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: macos-14
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Xcode 26 Beta
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: '26.0-beta'
    
    - name: Install Dependencies
      run: |
        brew install swiftlint swiftformat
    
    - name: SwiftLint
      run: swiftlint
    
    - name: Build
      run: |
        xcodebuild build -scheme NeuroNexa -destination 'platform=iOS Simulator,name=iPhone 16 Pro,OS=26.0'
    
    - name: Test
      run: |
        xcodebuild test -scheme NeuroNexa -destination 'platform=iOS Simulator,name=iPhone 16 Pro,OS=26.0'
