{"project": {"name": "NeuroNexa", "description": "Neurodiversity-first iOS 26 SwiftUI application with MVVM-C architecture", "version": "1.0.0", "platform": "iOS 26", "framework": "SwiftUI", "architecture": "MVVM-C"}, "claude_role": {"type": "secondary_agent_controlled_write", "permissions": "controlled_write", "supervisor": "augment_code", "responsibilities": ["code_review", "controlled_file_modifications", "swiftui_optimization_implementation", "accessibility_enhancement_implementation", "code_style_fixes", "performance_analysis"]}, "controlled_write_permissions": {"swiftui_view_optimizations": true, "accessibility_enhancements": true, "code_style_fixes": true, "comment_additions": true, "documentation_improvements": true, "test_coverage_enhancements": true, "performance_optimizations": true}, "strict_restrictions": {"git_operations": false, "dependency_management": false, "core_architecture_files": false, "design_system_changes": false, "project_configuration": false, "ci_cd_modifications": false, "service_layer_architecture": false}, "allowed_operations": ["code_analysis", "swiftui_optimization_implementation", "accessibility_enhancement_implementation", "code_style_fixes_implementation", "comment_additions_with_prefix", "documentation_improvements", "test_coverage_enhancements", "performance_optimization_implementation", "diff_preview_generation", "change_proposal_creation"], "communication_protocol": {"comment_prefix": "// <PERSON>:", "suggestion_format": "inline_comments", "escalation_required": ["architectural_decisions", "security_concerns", "performance_critical_changes", "neurodiversity_design_questions"]}, "focus_areas": {"swiftui_views": {"path": "UI/Views/**/*.swift", "priority": "high", "checks": ["performance", "accessibility", "neurodiversity_compliance"]}, "viewmodels": {"path": "UI/ViewModels/**/*.swift", "priority": "high", "checks": ["logic_optimization", "state_management", "memory_efficiency"]}, "services": {"path": "Services/**/*.swift", "priority": "medium", "checks": ["pattern_compliance", "error_handling", "async_optimization"], "restrictions": ["read_only_analysis"]}, "accessibility": {"path": "**/*Accessibility*.swift", "priority": "critical", "checks": ["wcag_compliance", "neurodiversity_support", "voice_over_support"]}}, "quality_standards": {"swiftlint_compliance": "100%", "test_coverage": "minimum_80%", "accessibility_score": "AAA", "performance_grade": "A", "neurodiversity_compliance": "required"}, "integration_workflow": {"initiation": "augment_code_triggered", "analysis_scope": "specified_by_augment", "modification_process": {"1_diff_preview": "required_before_changes", "2_category_validation": "must_match_approved_categories", "3_augment_approval": "explicit_approval_required", "4_implementation": "claude_code_executes", "5_validation": "augment_code_validates", "6_logging": "all_changes_logged", "7_rollback": "git_state_preserved"}, "approval_workflow": {"preview_required": true, "approval_timeout": "5_minutes", "auto_reject_on_timeout": true, "rollback_capability": "always_available"}}}