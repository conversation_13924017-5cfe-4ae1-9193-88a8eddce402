#!/bin/bash

# NeuroNexa Development Environment Validation Script
# Enforces iOS 26 and Xcode Beta 26 exclusive usage

set -e

echo "🔍 NeuroNexa Environment Validation Starting..."
echo "================================================"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation flags
VALIDATION_PASSED=true

# Function to print status
print_status() {
    if [ "$2" = "PASS" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    elif [ "$2" = "FAIL" ]; then
        echo -e "${RED}❌ $1${NC}"
        VALIDATION_PASSED=false
    elif [ "$2" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
    else
        echo -e "${BLUE}ℹ️  $1${NC}"
    fi
}

echo -e "${BLUE}🔧 Checking Xcode Version...${NC}"
XCODE_VERSION=$(xcodebuild -version | head -n 1 | awk '{print $2}')
XCODE_BUILD=$(xcodebuild -version | tail -n 1 | awk '{print $3}')

if [[ $XCODE_VERSION == 16.* ]]; then
    if [[ $XCODE_BUILD == *"Beta"* ]] && [[ $XCODE_VERSION == "16.2"* ]]; then
        print_status "Xcode Beta 26.0 detected: $XCODE_VERSION ($XCODE_BUILD)" "PASS"
    else
        print_status "Xcode version $XCODE_VERSION is not Beta 26.0" "FAIL"
        echo -e "${RED}   Required: Xcode Beta 26.0${NC}"
        echo -e "${RED}   Found: $XCODE_VERSION ($XCODE_BUILD)${NC}"
    fi
else
    print_status "Xcode version $XCODE_VERSION is not compatible with iOS 26" "FAIL"
    echo -e "${RED}   Required: Xcode Beta 26.0${NC}"
    echo -e "${RED}   Found: $XCODE_VERSION${NC}"
fi

echo -e "${BLUE}📱 Checking iOS SDK Availability...${NC}"
IOS_SDKS=$(xcodebuild -showsdks | grep iphoneos)
if echo "$IOS_SDKS" | grep -q "iphoneos18."; then
    # Note: iOS 26 may still show as 18.x in beta builds
    print_status "iOS 26 SDK available (may show as 18.x in beta)" "PASS"
elif echo "$IOS_SDKS" | grep -q "iphoneos26."; then
    print_status "iOS 26 SDK available" "PASS"
else
    print_status "iOS 26 SDK not found" "FAIL"
    echo -e "${RED}   Available SDKs:${NC}"
    echo "$IOS_SDKS" | sed 's/^/   /'
fi

echo -e "${BLUE}🎯 Checking Project Deployment Target...${NC}"
if [ -f "NeuroNexa.xcodeproj/project.pbxproj" ]; then
    DEPLOYMENT_TARGETS=$(grep "IPHONEOS_DEPLOYMENT_TARGET" NeuroNexa.xcodeproj/project.pbxproj | grep -o "[0-9]\+\.[0-9]\+" | sort -u)
    
    ALL_26=true
    for target in $DEPLOYMENT_TARGETS; do
        if [[ $target != "26.0" ]]; then
            ALL_26=false
            break
        fi
    done
    
    if $ALL_26; then
        print_status "All deployment targets set to iOS 26.0" "PASS"
    else
        print_status "Some deployment targets are not iOS 26.0" "FAIL"
        echo -e "${RED}   Found targets: $DEPLOYMENT_TARGETS${NC}"
        echo -e "${RED}   Required: 26.0 for all targets${NC}"
    fi
else
    print_status "NeuroNexa.xcodeproj not found" "FAIL"
fi

echo -e "${BLUE}📋 Checking Swift Version Compatibility...${NC}"
if [ -f "NeuroNexa.xcodeproj/project.pbxproj" ]; then
    SWIFT_VERSIONS=$(grep "SWIFT_VERSION" NeuroNexa.xcodeproj/project.pbxproj | grep -o "[0-9]\+\.[0-9]\+" | sort -u)
    
    if echo "$SWIFT_VERSIONS" | grep -q "6.0"; then
        print_status "Swift 6.0 configured (iOS 26 compatible)" "PASS"
    elif echo "$SWIFT_VERSIONS" | grep -q "5."; then
        print_status "Swift 5.x detected - consider upgrading to Swift 6.0" "WARN"
    else
        print_status "Swift version configuration not found or invalid" "FAIL"
    fi
fi

echo -e "${BLUE}🧪 Checking Available Simulators...${NC}"
IOS_26_SIMS=$(xcrun simctl list devices | grep -E "(iPhone|iPad)" | grep -E "(18\.|26\.)" | wc -l)
if [ "$IOS_26_SIMS" -gt 0 ]; then
    print_status "$IOS_26_SIMS iOS 26 simulators available" "PASS"
else
    print_status "No iOS 26 simulators found" "FAIL"
    echo -e "${RED}   Please install iOS 26 simulators via Xcode${NC}"
fi

echo -e "${BLUE}⌚ Checking watchOS 26 Support...${NC}"
WATCHOS_SDKS=$(xcodebuild -showsdks | grep watchos)
if echo "$WATCHOS_SDKS" | grep -q -E "(watchos11\.|watchos26\.)"; then
    print_status "watchOS 26 SDK available" "PASS"
else
    print_status "watchOS 26 SDK not found" "WARN"
    echo -e "${YELLOW}   This may affect Apple Watch integration${NC}"
fi

echo -e "${BLUE}📁 Checking Project Structure...${NC}"
REQUIRED_DIRS=("UI" "Core" "Services" "Documentation")
for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        print_status "Directory $dir exists" "PASS"
    else
        print_status "Directory $dir missing" "FAIL"
    fi
done

if [ -f "Documentation/Development-Environment-Rules.md" ]; then
    print_status "Development Environment Rules documented" "PASS"
else
    print_status "Development Environment Rules documentation missing" "FAIL"
fi

echo ""
echo "================================================"
if $VALIDATION_PASSED; then
    echo -e "${GREEN}🎉 VALIDATION PASSED: Environment complies with iOS 26/Xcode Beta 26 requirements${NC}"
    echo -e "${GREEN}✅ Ready for NeuroNexa development${NC}"
    exit 0
else
    echo -e "${RED}❌ VALIDATION FAILED: Environment does not meet requirements${NC}"
    echo -e "${RED}🚫 Please fix the issues above before proceeding${NC}"
    echo ""
    echo -e "${YELLOW}📋 Required Actions:${NC}"
    echo -e "${YELLOW}   1. Install Xcode Beta 26.0${NC}"
    echo -e "${YELLOW}   2. Ensure iOS 26 SDK is available${NC}"
    echo -e "${YELLOW}   3. Update project deployment targets to 26.0${NC}"
    echo -e "${YELLOW}   4. Install iOS 26 simulators${NC}"
    exit 1
fi
