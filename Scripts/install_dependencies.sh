#!/bin/bash
# NeuroNexa Dependency Installation Script
# Updated: July 2, 2025

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Xcode
    if ! command -v xcodebuild &> /dev/null; then
        error "Xcode not found. Please install Xcode 26 Beta."
        exit 1
    fi
    
    # Check Xcode version
    XCODE_VERSION=$(xcodebuild -version | head -n 1 | awk '{print $2}')
    log "Found Xcode version: $XCODE_VERSION"
    
    # Verify iOS 26 SDK
    if ! xcodebuild -showsdks | grep -q "iphoneos26"; then
        warning "iOS 26 SDK not found. Please ensure Xcode 26 Beta is properly installed."
    fi
    
    # Check Homebrew
    if ! command -v brew &> /dev/null; then
        log "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    success "Prerequisites check complete"
}

# Install development tools
install_dev_tools() {
    log "Installing development tools..."
    
    # Update Homebrew
    brew update
    
    # Install CocoaPods
    if ! command -v pod &> /dev/null; then
        log "Installing CocoaPods..."
        sudo gem install cocoapods
    else
        log "CocoaPods already installed: $(pod --version)"
    fi
    
    # Install SwiftLint
    if ! command -v swiftlint &> /dev/null; then
        log "Installing SwiftLint..."
        brew install swiftlint
    else
        log "SwiftLint already installed: $(swiftlint version)"
    fi
    
    # Install SwiftFormat
    if ! command -v swiftformat &> /dev/null; then
        log "Installing SwiftFormat..."
        brew install swiftformat
    else
        log "SwiftFormat already installed: $(swiftformat --version)"
    fi
    
    # Install Fastlane
    if ! command -v fastlane &> /dev/null; then
        log "Installing Fastlane..."
        brew install fastlane
    else
        log "Fastlane already installed: $(fastlane --version | head -n 1)"
    fi
    
    success "Development tools installed"
}

# Install CocoaPods dependencies
install_cocoapods() {
    log "Installing CocoaPods dependencies..."
    
    if [ -f "Podfile" ]; then
        # Update CocoaPods repo
        log "Updating CocoaPods repository..."
        pod repo update
        
        # Install pods
        log "Installing pods..."
        pod install --verbose
        
        success "CocoaPods dependencies installed"
    else
        warning "Podfile not found, skipping CocoaPods installation"
    fi
}

# Setup Swift Package Manager
setup_spm() {
    log "Setting up Swift Package Manager..."
    
    # SPM dependencies are managed through Xcode
    # This will be handled when opening the project
    log "Swift Package Manager dependencies will be resolved when opening the Xcode project"
    success "Swift Package Manager ready"
}

# Verify installation
verify_installation() {
    log "Verifying installation..."
    
    # Check if workspace was created
    if [ -f "NeuroNexa.xcworkspace" ]; then
        success "Xcode workspace created successfully"
    else
        warning "Xcode workspace not found. CocoaPods may not have run correctly."
    fi
    
    # Check development tools
    local tools=("xcodebuild" "pod" "swiftlint" "swiftformat" "fastlane")
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            success "$tool is available"
        else
            error "$tool is not available"
        fi
    done
}

# Create build scripts
create_build_scripts() {
    log "Creating build scripts..."
    
    # Make scripts executable
    chmod +x Scripts/*.sh
    
    success "Build scripts are ready"
}

# Main installation process
main() {
    log "Starting NeuroNexa dependency installation..."
    
    # Change to project directory
    cd "$(dirname "$0")/.."
    
    check_prerequisites
    install_dev_tools
    install_cocoapods
    setup_spm
    create_build_scripts
    verify_installation
    
    success "All dependencies installed successfully!"
    
    echo ""
    log "Next steps:"
    echo "1. Open NeuroNexa.xcworkspace in Xcode 26 Beta"
    echo "2. Add Swift Package Manager dependencies through Xcode:"
    echo "   - File > Add Package Dependencies"
    echo "   - Add the packages listed in DEPENDENCIES_CONFIGURATION.md"
    echo "3. Build and run the project (⌘+R)"
    echo ""
    log "For more information, see Documentation/BUILD_LIBRARY.md"
}

main "$@"
