#!/bin/bash

# NeuroNexa Pre-Commit Environment Validation Hook
# Ensures all commits comply with iOS 26/Xcode Beta 26 requirements

set -e

echo "🔒 NeuroNexa Pre-Commit Validation"
echo "=================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Run environment validation
if [ -f "Scripts/validate-environment.sh" ]; then
    echo -e "${YELLOW}Running environment validation...${NC}"
    if ./Scripts/validate-environment.sh; then
        echo -e "${GREEN}✅ Environment validation passed${NC}"
    else
        echo -e "${RED}❌ Environment validation failed${NC}"
        echo -e "${RED}🚫 Commit blocked - fix environment issues first${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Environment validation script not found${NC}"
    exit 1
fi

# Check for iOS version references in code
echo -e "${YELLOW}Checking for legacy iOS version references...${NC}"
LEGACY_REFS=$(git diff --cached --name-only | xargs grep -l -E "(iOS [0-9]{1,2}\.[0-9]|iOS[0-9]{1,2})" 2>/dev/null || true)

if [ ! -z "$LEGACY_REFS" ]; then
    echo -e "${RED}❌ Legacy iOS version references found in:${NC}"
    echo "$LEGACY_REFS" | sed 's/^/   /'
    echo -e "${RED}🚫 Remove legacy iOS version references before committing${NC}"
    exit 1
fi

# Check deployment target in project files
if git diff --cached --name-only | grep -q "\.pbxproj$"; then
    echo -e "${YELLOW}Validating Xcode project deployment targets...${NC}"
    
    # Check staged changes for deployment target
    STAGED_TARGETS=$(git diff --cached | grep "IPHONEOS_DEPLOYMENT_TARGET" | grep -o "[0-9]\+\.[0-9]\+" || true)
    
    if [ ! -z "$STAGED_TARGETS" ]; then
        for target in $STAGED_TARGETS; do
            if [[ $target != "26.0" ]]; then
                echo -e "${RED}❌ Invalid deployment target in staged changes: $target${NC}"
                echo -e "${RED}🚫 Only iOS 26.0 deployment target allowed${NC}"
                exit 1
            fi
        done
    fi
fi

# Check for Xcode version requirements in documentation
if git diff --cached --name-only | grep -q -E "\.(md|txt)$"; then
    echo -e "${YELLOW}Validating documentation for Xcode version requirements...${NC}"
    
    # Ensure documentation mentions iOS 26/Xcode Beta 26 requirements
    DOC_FILES=$(git diff --cached --name-only | grep -E "\.(md|txt)$")
    for file in $DOC_FILES; do
        if git show ":$file" | grep -q -i "ios.*26\|xcode.*beta.*26"; then
            echo -e "${GREEN}✅ $file contains iOS 26/Xcode Beta 26 references${NC}"
        else
            echo -e "${YELLOW}⚠️  $file may need iOS 26/Xcode Beta 26 requirement documentation${NC}"
        fi
    done
fi

echo -e "${GREEN}🎉 Pre-commit validation passed - ready to commit${NC}"
echo -e "${GREEN}✅ All changes comply with iOS 26/Xcode Beta 26 requirements${NC}"
