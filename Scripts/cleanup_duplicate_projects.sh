#!/bin/bash

# NeuroNexa Project Cleanup Script
# Removes duplicate projects and consolidates assets

set -e

# Configuration
PROJECT_ROOT="/Users/<USER>/NeuroNexa"
MAIN_PROJECT="$PROJECT_ROOT/NeuroNexa-iOS26"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Backup important assets before cleanup
backup_assets() {
    log "Backing up assets from standalone assets folder..."
    
    if [ -d "$PROJECT_ROOT/assets" ]; then
        # Copy useful assets to main project
        cp -r "$PROJECT_ROOT/assets/fonts" "$MAIN_PROJECT/Resources/" 2>/dev/null || true
        cp -r "$PROJECT_ROOT/assets/sounds" "$MAIN_PROJECT/Resources/" 2>/dev/null || true
        
        # Copy icons and images to Assets.xcassets
        if [ -d "$PROJECT_ROOT/assets/icons" ]; then
            mkdir -p "$MAIN_PROJECT/Resources/Assets.xcassets/Icons"
            cp -r "$PROJECT_ROOT/assets/icons/"* "$MAIN_PROJECT/Resources/Assets.xcassets/Icons/" 2>/dev/null || true
        fi
        
        if [ -d "$PROJECT_ROOT/assets/images" ]; then
            mkdir -p "$MAIN_PROJECT/Resources/Assets.xcassets/Images"
            cp -r "$PROJECT_ROOT/assets/images/"* "$MAIN_PROJECT/Resources/Assets.xcassets/Images/" 2>/dev/null || true
        fi
        
        success "Assets backed up to main project"
    else
        warning "No assets folder found to backup"
    fi
}

# Remove duplicate projects
cleanup_duplicates() {
    log "Removing duplicate and unnecessary project folders..."
    
    cd "$PROJECT_ROOT"
    
    # Remove old Neuronexa project (incomplete iOS project)
    if [ -d "Neuronexa" ]; then
        log "Removing old Neuronexa iOS project..."
        rm -rf "Neuronexa"
        success "Removed Neuronexa/ (old iOS project)"
    fi
    
    # Remove Flutter lib structure (not needed for iOS native)
    if [ -d "lib" ]; then
        log "Removing Flutter lib structure..."
        rm -rf "lib"
        success "Removed lib/ (Flutter structure)"
    fi
    
    # Remove standalone assets folder (after backing up)
    if [ -d "assets" ]; then
        log "Removing standalone assets folder..."
        rm -rf "assets"
        success "Removed assets/ (consolidated into main project)"
    fi
}

# Organize documentation files
organize_docs() {
    log "Organizing documentation files..."
    
    cd "$PROJECT_ROOT"
    
    # Move iOS development documentation to main project
    if [ -f "iOS_26_DEVELOPMENT_LIBRARY.md" ]; then
        mv "iOS_26_DEVELOPMENT_LIBRARY.md" "$MAIN_PROJECT/Documentation/"
        success "Moved iOS_26_DEVELOPMENT_LIBRARY.md to Documentation/"
    fi
    
    if [ -f "iOS_26_ENHANCED_LIBRARY.md" ]; then
        mv "iOS_26_ENHANCED_LIBRARY.md" "$MAIN_PROJECT/Documentation/"
        success "Moved iOS_26_ENHANCED_LIBRARY.md to Documentation/"
    fi
    
    if [ -f "iOS_DEVELOPMENT_LIBRARY.md" ]; then
        mv "iOS_DEVELOPMENT_LIBRARY.md" "$MAIN_PROJECT/Documentation/"
        success "Moved iOS_DEVELOPMENT_LIBRARY.md to Documentation/"
    fi
    
    if [ -f "iOS_DEVELOPMENT_PLAN.md" ]; then
        mv "iOS_DEVELOPMENT_PLAN.md" "$MAIN_PROJECT/Documentation/"
        success "Moved iOS_DEVELOPMENT_PLAN.md to Documentation/"
    fi
    
    if [ -f "iOS_DEVELOPMENT_RULES.md" ]; then
        mv "iOS_DEVELOPMENT_RULES.md" "$MAIN_PROJECT/Documentation/"
        success "Moved iOS_DEVELOPMENT_RULES.md to Documentation/"
    fi
}

# Create final project summary
create_summary() {
    log "Creating cleanup summary..."
    
    cat > "$MAIN_PROJECT/Documentation/PROJECT_CONSOLIDATION_SUMMARY.md" << 'EOF'
# 🧹 NeuroNexa Project Consolidation Summary

**Date:** July 2, 2025  
**Action:** Consolidated multiple project folders into single iOS 26 project  

## ✅ **Projects Consolidated**

### 🎯 **Main Project (KEPT)**
- **`NeuroNexa-iOS26/`** - Complete iOS 26 native project with proper architecture

### 🗑️ **Removed Projects**
- **`Neuronexa/`** - Old/incomplete iOS project (removed)
- **`lib/`** - Flutter project structure (removed - not needed for iOS native)
- **`assets/`** - Standalone assets folder (consolidated into main project)

### 📚 **Documentation Consolidated**
- All iOS development documentation moved to `NeuroNexa-iOS26/Documentation/`
- Project libraries and development guides centralized

## 🎯 **Final Structure**
```
/Users/<USER>/NeuroNexa/
└── NeuroNexa-iOS26/          # ✅ SINGLE iOS 26 PROJECT
    ├── App/                  # Core app files
    ├── UI/                   # SwiftUI views and components
    ├── Core/                 # Business logic and services
    ├── Resources/            # Assets, fonts, sounds (consolidated)
    ├── Documentation/        # All project documentation
    ├── Tests/                # Comprehensive testing
    └── [Other directories]   # Complete project structure
```

## ✅ **Benefits**
- **Single Source of Truth**: One complete iOS 26 project
- **No Confusion**: Eliminated duplicate and conflicting structures
- **Consolidated Assets**: All resources in proper iOS project structure
- **Centralized Documentation**: All guides and libraries in one place
- **Clean Development**: No conflicting project files or structures

**Ready for focused iOS 26 development!** 🚀
EOF

    success "Created consolidation summary"
}

# Main execution
main() {
    log "Starting NeuroNexa project consolidation..."
    
    # Confirm main project exists
    if [ ! -d "$MAIN_PROJECT" ]; then
        error "Main project NeuroNexa-iOS26 not found!"
        exit 1
    fi
    
    backup_assets
    organize_docs
    cleanup_duplicates
    create_summary
    
    success "Project consolidation complete!"
    
    log "Final project structure:"
    echo "📱 NeuroNexa-iOS26/ - Your complete iOS 26 project"
    echo "📚 All documentation consolidated"
    echo "🎨 All assets integrated"
    echo "🧹 Duplicate projects removed"
    
    log "You now have a single, clean iOS 26 project ready for development!"
}

main "$@"
