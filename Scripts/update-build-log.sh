#!/bin/bash

# NeuroNexa Build Log Update Script
# Automatically updates the build log with daily progress and metrics

set -e

BUILD_LOG="NeuroNexa/Documentation/BUILD_LOG.md"
REPORTS_DIR="reports"
DATE=$(date '+%Y-%m-%d')
TIME=$(date '+%H:%M:%S')
DATETIME="$DATE $TIME"

echo "📝 Updating NeuroNexa Build Log..."
echo "=================================="

# Create reports directory if it doesn't exist
mkdir -p "$REPORTS_DIR"

# Generate daily metrics
echo "📊 Collecting build metrics..."

# Code statistics
SWIFT_FILES=$(find NeuroNexa -name "*.swift" 2>/dev/null | wc -l | tr -d ' ')
TEST_FILES=$(find NeuroNexa -name "*Test*.swift" 2>/dev/null | wc -l | tr -d ' ')
TOTAL_LINES=$(find NeuroNexa -name "*.swift" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")

# Git statistics
COMMITS_TODAY=$(git log --oneline --since="1 day ago" | wc -l | tr -d ' ')
TOTAL_COMMITS=$(git rev-list --count HEAD 2>/dev/null || echo "0")

# Quality metrics
LINT_ISSUES="0"
if command -v swiftlint &> /dev/null; then
    LINT_ISSUES=$(swiftlint --reporter json 2>/dev/null | jq -r '. | length' 2>/dev/null || echo "0")
fi

# TODO/FIXME tracking
TODO_COUNT=$(grep -r "TODO\|FIXME" NeuroNexa/ 2>/dev/null | wc -l | tr -d ' ')

# Create daily entry
DAILY_ENTRY="
## 📅 **Daily Build Log Entry - $DATE**

### **Development Session: $DATETIME**

#### **Daily Metrics:**
- **Swift Files**: $SWIFT_FILES (+$(($SWIFT_FILES - ${PREV_SWIFT_FILES:-0})) from yesterday)
- **Test Files**: $TEST_FILES
- **Total Lines of Code**: $TOTAL_LINES
- **Commits Today**: $COMMITS_TODAY
- **Total Commits**: $TOTAL_COMMITS
- **SwiftLint Issues**: $LINT_ISSUES
- **TODO/FIXME Items**: $TODO_COUNT

#### **Today's Progress:**"

# Get recent commits for progress tracking
if [ "$COMMITS_TODAY" -gt 0 ]; then
    DAILY_ENTRY="$DAILY_ENTRY
**Recent Commits:**
\`\`\`
$(git log --oneline --since="1 day ago" | head -5)
\`\`\`"
else
    DAILY_ENTRY="$DAILY_ENTRY
- No commits made today"
fi

# Add current work status
DAILY_ENTRY="$DAILY_ENTRY

#### **Current Work Status:**
- **Active Phase**: Phase 2 - Authentication & User Management
- **Current Sprint**: Week $((($(date +%j) - 1) / 7 + 1))
- **Working Directory**: $(pwd)
- **Git Branch**: $(git branch --show-current 2>/dev/null || echo "main")

#### **Quality Assurance:**"

# Quality checks
if [ "$LINT_ISSUES" -eq 0 ]; then
    DAILY_ENTRY="$DAILY_ENTRY
- ✅ **Code Quality**: No SwiftLint violations"
else
    DAILY_ENTRY="$DAILY_ENTRY
- ⚠️  **Code Quality**: $LINT_ISSUES SwiftLint violations need attention"
fi

# Test status
if [ "$TEST_FILES" -gt 0 ]; then
    DAILY_ENTRY="$DAILY_ENTRY
- ✅ **Testing**: $TEST_FILES test files maintained"
else
    DAILY_ENTRY="$DAILY_ENTRY
- ⚠️  **Testing**: No test files found"
fi

# Technical debt tracking
if [ "$TODO_COUNT" -eq 0 ]; then
    DAILY_ENTRY="$DAILY_ENTRY
- ✅ **Technical Debt**: No TODO/FIXME items"
else
    DAILY_ENTRY="$DAILY_ENTRY
- 📋 **Technical Debt**: $TODO_COUNT TODO/FIXME items tracked"
fi

DAILY_ENTRY="$DAILY_ENTRY

#### **Next Steps:**
- Continue Phase 2 implementation
- Address any quality issues identified
- Maintain test coverage above 90%

---"

# Check if build log exists
if [ ! -f "$BUILD_LOG" ]; then
    echo "❌ Build log not found: $BUILD_LOG"
    exit 1
fi

# Find insertion point (before the last "Build Log Status" section)
TEMP_FILE=$(mktemp)

# Read the build log and insert the daily entry before the final status section
awk -v daily_entry="$DAILY_ENTRY" '
/^---$/ && /Build Log Status/ {
    print daily_entry
}
{ print }
' "$BUILD_LOG" > "$TEMP_FILE"

# If the insertion didn't work (no status section found), append to end
if ! grep -q "Daily Build Log Entry - $DATE" "$TEMP_FILE"; then
    echo "$DAILY_ENTRY" >> "$BUILD_LOG"
else
    mv "$TEMP_FILE" "$BUILD_LOG"
fi

# Clean up temp file if it exists
[ -f "$TEMP_FILE" ] && rm "$TEMP_FILE"

# Update the "Last Updated" timestamp
sed -i.bak "s/\*\*Last Updated\*\*:.*/\*\*Last Updated\*\*: $DATETIME/" "$BUILD_LOG"
rm -f "${BUILD_LOG}.bak"

# Generate summary report
echo "
📊 **Build Log Update Summary**
==============================
- Date: $DATE
- Time: $TIME
- Swift Files: $SWIFT_FILES
- Test Files: $TEST_FILES
- Lines of Code: $TOTAL_LINES
- Commits Today: $COMMITS_TODAY
- Quality Issues: $LINT_ISSUES
- Technical Debt: $TODO_COUNT items

✅ Build log updated successfully!
📄 Location: $BUILD_LOG
" > "$REPORTS_DIR/build_log_update_$DATE.txt"

echo "✅ Build log updated successfully!"
echo "📄 Daily entry added for $DATE"
echo "📊 Summary report: $REPORTS_DIR/build_log_update_$DATE.txt"

# Optional: Show recent entries
echo ""
echo "📖 Recent build log entries:"
echo "----------------------------"
tail -20 "$BUILD_LOG" | head -15

echo ""
echo "🎯 Ready to continue with Phase 2 implementation!"
