#!/bin/bash

# NeuroNexa iOS 26 Project Setup Script
# This script creates a complete iOS 26 project structure with Xcode 26 beta

set -e

# Configuration
PROJECT_NAME="NeuroNexa"
BUNDLE_ID="com.neuronexa.app"
PROJECT_PATH="/Users/<USER>/NeuroNexa/NeuroNexa-iOS26"
XCODE_PATH="/Applications/Xcode-beta.app"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create project structure
create_project_structure() {
    log "Creating iOS 26 project structure..."
    
    cd "$PROJECT_PATH"
    
    # Create main directories
    mkdir -p Sources/NeuroNexa
    mkdir -p Sources/NeuroNexaWatch
    mkdir -p Tests/NeuroNexaTests
    mkdir -p Tests/NeuroNexaUITests
    mkdir -p Resources/Assets.xcassets
    mkdir -p Resources/Colors
    mkdir -p Scripts
    mkdir -p Documentation
    
    # Move our Swift files to the correct location
    if [ -f "NeuroNexaApp.swift" ]; then
        mv NeuroNexaApp.swift Sources/NeuroNexa/
    fi
    
    if [ -f "ContentView.swift" ]; then
        mv ContentView.swift Sources/NeuroNexa/
    fi
    
    if [ -f "iOS26Extensions.swift" ]; then
        mv iOS26Extensions.swift Sources/NeuroNexa/
    fi
    
    if [ -f "AppleIntelligenceIntegration.swift" ]; then
        mv AppleIntelligenceIntegration.swift Sources/NeuroNexa/
    fi
    
    success "Project structure created"
}

# Create Package.swift for Swift Package Manager
create_package_swift() {
    log "Creating Package.swift..."
    
    cat > Package.swift << 'EOF'
// swift-tools-version: 6.0
import PackageDescription

let package = Package(
    name: "NeuroNexa",
    platforms: [
        .iOS(.v26),
        .watchOS(.v26)
    ],
    products: [
        .library(
            name: "NeuroNexa",
            targets: ["NeuroNexa"]
        ),
    ],
    dependencies: [
        // Add external dependencies here
    ],
    targets: [
        .target(
            name: "NeuroNexa",
            dependencies: [],
            path: "Sources/NeuroNexa",
            resources: [
                .process("Resources")
            ]
        ),
        .target(
            name: "NeuroNexaWatch",
            dependencies: ["NeuroNexa"],
            path: "Sources/NeuroNexaWatch"
        ),
        .testTarget(
            name: "NeuroNexaTests",
            dependencies: ["NeuroNexa"],
            path: "Tests/NeuroNexaTests"
        ),
    ]
)
EOF
    
    success "Package.swift created"
}

# Create SwiftLint configuration
create_swiftlint_config() {
    log "Creating SwiftLint configuration..."
    
    cat > .swiftlint.yml << 'EOF'
# NeuroNexa SwiftLint Configuration for iOS 26

# Paths to include/exclude
included:
  - Sources
  - Tests

excluded:
  - Build
  - Logs
  - Reports
  - Scripts

# Rules
disabled_rules:
  - trailing_whitespace
  - line_length

opt_in_rules:
  - accessibility_label_for_image
  - accessibility_trait_for_button
  - array_init
  - closure_spacing
  - collection_alignment
  - contains_over_filter_count
  - empty_collection_literal
  - empty_count
  - empty_string
  - enum_case_associated_values_count
  - explicit_init
  - extension_access_modifier
  - fallthrough
  - fatal_error_message
  - file_header
  - first_where
  - force_unwrapping
  - function_default_parameter_at_end
  - identical_operands
  - implicit_return
  - joined_default_parameter
  - last_where
  - legacy_random
  - literal_expression_end_indentation
  - lower_acl_than_parent
  - modifier_order
  - nimble_operator
  - nslocalizedstring_key
  - number_separator
  - object_literal
  - operator_usage_whitespace
  - overridden_super_call
  - override_in_extension
  - pattern_matching_keywords
  - prefer_self_type_over_type_of_self
  - private_action
  - private_outlet
  - prohibited_super_call
  - quick_discouraged_call
  - quick_discouraged_focused_test
  - quick_discouraged_pending_test
  - reduce_into
  - redundant_nil_coalescing
  - redundant_type_annotation
  - single_test_class
  - sorted_first_last
  - sorted_imports
  - static_operator
  - strong_iboutlet
  - toggle_bool
  - unavailable_function
  - unneeded_parentheses_in_closure_argument
  - unowned_variable_capture
  - untyped_error_in_catch
  - vertical_parameter_alignment_on_call
  - vertical_whitespace_closing_braces
  - vertical_whitespace_opening_braces
  - xct_specific_matcher
  - yoda_condition

# Custom rules for neurodiversity-focused development
custom_rules:
  accessibility_label_required:
    name: "Accessibility Label Required"
    regex: '\.accessibilityLabel\('
    message: "All interactive elements should have accessibility labels"
    severity: warning
  
  cognitive_load_consideration:
    name: "Cognitive Load Consideration"
    regex: 'cognitiveLoad'
    message: "Good practice: considering cognitive load in UI design"
    severity: info

# Configuration
line_length:
  warning: 120
  error: 150

function_body_length:
  warning: 50
  error: 100

type_body_length:
  warning: 300
  error: 500

file_length:
  warning: 500
  error: 1000

cyclomatic_complexity:
  warning: 10
  error: 20

nesting:
  type_level:
    warning: 2
    error: 3
  function_level:
    warning: 5
    error: 10

identifier_name:
  min_length:
    warning: 2
    error: 1
  max_length:
    warning: 50
    error: 100
  excluded:
    - id
    - x
    - y
    - z

reporter: "xcode"
EOF
    
    success "SwiftLint configuration created"
}

# Create SwiftFormat configuration
create_swiftformat_config() {
    log "Creating SwiftFormat configuration..."
    
    cat > .swiftformat << 'EOF'
# NeuroNexa SwiftFormat Configuration for iOS 26

# Version
--swiftversion 6.0

# Rules
--rules indent,linebreaks,braces,semicolons,redundantParens,redundantGet,redundantSelf,sortedImports,duplicateImports,unusedArguments,trailingSpace,consecutiveSpaces,blankLinesAtEndOfScope,blankLinesAtStartOfScope,blankLinesBetweenScopes,spaceAroundBraces,spaceAroundBrackets,spaceAroundComments,spaceAroundGenerics,spaceAroundOperators,spaceAroundParens,spaceInsideBraces,spaceInsideBrackets,spaceInsideComments,spaceInsideGenerics,spaceInsideParens,todos,typeSugar,wrap,wrapArguments,wrapAttributes,yodaConditions

# Options
--indent 4
--tabwidth 4
--maxwidth 120
--wraparguments before-first
--wrapparameters before-first
--wrapcollections before-first
--closingparen balanced
--commas inline
--trimwhitespace always
--insertlines enabled
--removelines enabled
--allman false
--stripunusedargs closure-only
--self remove
--importgrouping testable-bottom
--ifdef no-indent
--redundanttype inferred
--nospaceoperators ...,..<
--ranges no-space
--typeattributes prev-line
--varattributes prev-line
--funcattributes prev-line
--modifierorder private,fileprivate,internal,public,open,override,convenience,required,static,class,final,lazy,weak,unowned,@objc,@nonobjc,@available,@IBAction,@IBOutlet,@IBDesignable,@IBInspectable,@GKInspectable,@NSManaged,@UIApplicationMain,@NSApplicationMain
EOF
    
    success "SwiftFormat configuration created"
}

# Create basic test files
create_test_files() {
    log "Creating test files..."
    
    # Unit test file
    cat > Tests/NeuroNexaTests/NeuroNexaTests.swift << 'EOF'
import XCTest
@testable import NeuroNexa

@available(iOS 26.0, *)
final class NeuroNexaTests: XCTestCase {
    
    func testAppStateInitialization() {
        let appState = AppState.shared
        XCTAssertNotNil(appState)
        XCTAssertFalse(appState.isAuthenticated)
        XCTAssertEqual(appState.cognitiveLoadLevel, .normal)
    }
    
    func testDependencyContainerInitialization() {
        let container = DependencyContainer.shared
        XCTAssertNotNil(container)
        XCTAssertNotNil(container.authenticationService)
        XCTAssertNotNil(container.aiTaskCoach)
    }
    
    func testCognitiveLoadLevels() {
        let levels = CognitiveLoadLevel.allCases
        XCTAssertEqual(levels.count, 3)
        XCTAssertTrue(levels.contains(.low))
        XCTAssertTrue(levels.contains(.normal))
        XCTAssertTrue(levels.contains(.high))
    }
}
EOF
    
    # UI test file
    cat > Tests/NeuroNexaUITests/NeuroNexaUITests.swift << 'EOF'
import XCTest

@available(iOS 26.0, *)
final class NeuroNexaUITests: XCTestCase {
    
    override func setUpWithError() throws {
        continueAfterFailure = false
    }
    
    func testAppLaunch() throws {
        let app = XCUIApplication()
        app.launch()
        
        // Test that the app launches successfully
        XCTAssertTrue(app.exists)
    }
    
    func testAccessibilityLabels() throws {
        let app = XCUIApplication()
        app.launch()
        
        // Test that main navigation elements have accessibility labels
        let dashboardTab = app.tabBars.buttons["Dashboard tab"]
        XCTAssertTrue(dashboardTab.exists)
        
        let aiCoachTab = app.tabBars.buttons["AI Task Coach tab"]
        XCTAssertTrue(aiCoachTab.exists)
        
        let breathingTab = app.tabBars.buttons["Breathing exercises tab"]
        XCTAssertTrue(breathingTab.exists)
    }
    
    func testCognitiveLoadAdaptation() throws {
        let app = XCUIApplication()
        app.launch()
        
        // Test that UI adapts to different cognitive load levels
        // This would require implementing cognitive load simulation
        XCTAssertTrue(app.exists)
    }
}
EOF
    
    success "Test files created"
}

# Create README
create_readme() {
    log "Creating README.md..."
    
    cat > README.md << 'EOF'
# NeuroNexa iOS 26

An AI-powered productivity assistant designed specifically for neurodiverse users, built with iOS 26 and Xcode 26 beta.

## Features

- 🧠 **Apple Intelligence Integration**: On-device AI for personalized task coaching
- ♿ **Enhanced Accessibility**: iOS 26 neurodiversity-focused accessibility features
- 🏥 **Advanced HealthKit**: Mental health and cognitive load tracking
- 🎨 **SwiftUI 6.0**: Modern, adaptive user interface
- ⌚ **Apple Watch Support**: Companion app for watchOS 26

## Requirements

- iOS 26.0+
- Xcode 26.0 Beta
- Swift 6.0
- Apple Intelligence capable device

## Setup

1. Clone the repository
2. Run the setup script: `./Scripts/setup_ios26_project.sh`
3. Open `NeuroNexa.xcodeproj` in Xcode 26 Beta
4. Build and run on iOS 26 simulator or device

## Development

### MCP Tools

Use the MCP (Monitoring, Control, Planning) build tools:

```bash
# Complete setup and build
./Scripts/mcp_build_tools.sh all

# Individual commands
./Scripts/mcp_build_tools.sh setup
./Scripts/mcp_build_tools.sh build
./Scripts/mcp_build_tools.sh test
```

### Code Quality

- **SwiftLint**: Automated code style checking
- **SwiftFormat**: Consistent code formatting
- **Accessibility Testing**: Comprehensive accessibility validation

## Architecture

- **MVVM-C**: Model-View-ViewModel-Coordinator pattern
- **Clean Architecture**: Separation of concerns with clear boundaries
- **Dependency Injection**: Comprehensive DI container
- **Reactive Programming**: Combine framework for data flow

## Testing

- **Unit Tests**: 70% coverage target
- **UI Tests**: Critical user journey validation
- **Accessibility Tests**: Neurodiversity-focused testing

## Contributing

Please read our development guidelines in the `iOS_DEVELOPMENT_RULES.md` file.

## License

Copyright © 2025 NeuroNexa. All rights reserved.
EOF
    
    success "README.md created"
}

# Main execution
main() {
    log "Setting up NeuroNexa iOS 26 project..."
    
    # Verify Xcode 26 Beta
    if [ ! -d "$XCODE_PATH" ]; then
        echo "Error: Xcode 26 Beta not found at $XCODE_PATH"
        exit 1
    fi
    
    # Set active developer directory (skip if no sudo access)
    # sudo xcode-select -s "$XCODE_PATH/Contents/Developer"
    warning "Skipping xcode-select (requires sudo). Run manually if needed."
    
    # Create project structure
    create_project_structure
    create_package_swift
    create_swiftlint_config
    create_swiftformat_config
    create_test_files
    create_readme
    
    success "NeuroNexa iOS 26 project setup completed!"
    
    log "Next steps:"
    echo "1. Open Xcode 26 Beta"
    echo "2. Create new iOS project with existing files"
    echo "3. Configure project settings for iOS 26"
    echo "4. Run: ./Scripts/mcp_build_tools.sh setup"
    echo "5. Start development!"
}

main "$@"
