#!/bin/bash
# NeuroNexa Dependency Update Script
# Updated: July 2, 2025

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Update CocoaPods
update_cocoapods() {
    log "Updating CocoaPods dependencies..."
    
    if [ -f "Podfile" ]; then
        # Update CocoaPods repository
        log "Updating CocoaPods repository..."
        pod repo update
        
        # Update pods
        log "Updating pods..."
        pod update --verbose
        
        # Show outdated pods
        log "Checking for outdated pods..."
        pod outdated || true
        
        success "CocoaPods dependencies updated"
    else
        warning "Podfile not found, skipping CocoaPods update"
    fi
}

# Update development tools
update_dev_tools() {
    log "Updating development tools..."
    
    # Update Homebrew
    log "Updating Homebrew..."
    brew update
    
    # Update tools
    local tools=("swiftlint" "swiftformat" "fastlane")
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log "Updating $tool..."
            brew upgrade "$tool" || log "$tool is already up to date"
        else
            warning "$tool not found, skipping update"
        fi
    done
    
    success "Development tools updated"
}

# Update Swift Package Manager dependencies
update_spm() {
    log "Updating Swift Package Manager dependencies..."
    
    # Check if Xcode project exists
    if [ -f "NeuroNexa.xcodeproj/project.pbxproj" ]; then
        log "Swift Package Manager dependencies should be updated through Xcode:"
        echo "1. Open NeuroNexa.xcworkspace in Xcode"
        echo "2. Go to File > Packages > Update to Latest Package Versions"
        echo "3. Or use File > Packages > Reset Package Caches if needed"
    else
        warning "Xcode project not found"
    fi
    
    success "Swift Package Manager update instructions provided"
}

# Check for security vulnerabilities
security_audit() {
    log "Checking for security vulnerabilities..."
    
    # Check CocoaPods for vulnerabilities
    if [ -f "Podfile.lock" ]; then
        log "Checking CocoaPods for known vulnerabilities..."
        # Note: There's no built-in security audit for CocoaPods like npm audit
        # This would require a third-party tool or manual checking
        log "Manual security review recommended for CocoaPods dependencies"
    fi
    
    # Check for outdated dependencies
    log "Checking for outdated dependencies..."
    if [ -f "Podfile" ]; then
        pod outdated || true
    fi
    
    success "Security audit complete"
}

# Clean build artifacts
clean_build() {
    log "Cleaning build artifacts..."
    
    # Clean Xcode build folder
    if [ -d "Build" ]; then
        rm -rf Build
        success "Removed Build directory"
    fi
    
    # Clean derived data (optional)
    read -p "Do you want to clean Xcode derived data? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Cleaning Xcode derived data..."
        rm -rf ~/Library/Developer/Xcode/DerivedData/NeuroNexa-*
        success "Xcode derived data cleaned"
    fi
    
    # Clean CocoaPods cache
    if command -v pod &> /dev/null; then
        log "Cleaning CocoaPods cache..."
        pod cache clean --all
        success "CocoaPods cache cleaned"
    fi
    
    success "Build artifacts cleaned"
}

# Verify updates
verify_updates() {
    log "Verifying updates..."
    
    # Check tool versions
    local tools=("xcodebuild" "pod" "swiftlint" "swiftformat" "fastlane")
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            case "$tool" in
                "xcodebuild")
                    VERSION=$(xcodebuild -version | head -n 1)
                    ;;
                "pod")
                    VERSION=$(pod --version)
                    ;;
                "swiftlint")
                    VERSION=$(swiftlint version)
                    ;;
                "swiftformat")
                    VERSION=$(swiftformat --version)
                    ;;
                "fastlane")
                    VERSION=$(fastlane --version | head -n 1)
                    ;;
            esac
            success "$tool: $VERSION"
        else
            error "$tool is not available"
        fi
    done
    
    # Check if workspace exists
    if [ -f "NeuroNexa.xcworkspace" ]; then
        success "Xcode workspace is available"
    else
        warning "Xcode workspace not found"
    fi
}

# Generate update report
generate_report() {
    log "Generating update report..."
    
    local report_file="Reports/dependency_update_$(date +%Y%m%d_%H%M%S).md"
    mkdir -p Reports
    
    cat > "$report_file" << EOF
# NeuroNexa Dependency Update Report

**Date:** $(date)
**Updated by:** $(whoami)

## Tool Versions
- Xcode: $(xcodebuild -version | head -n 1)
- CocoaPods: $(pod --version 2>/dev/null || echo "Not available")
- SwiftLint: $(swiftlint version 2>/dev/null || echo "Not available")
- SwiftFormat: $(swiftformat --version 2>/dev/null || echo "Not available")
- Fastlane: $(fastlane --version 2>/dev/null | head -n 1 || echo "Not available")

## CocoaPods Dependencies
$(if [ -f "Podfile.lock" ]; then echo "\`\`\`"; cat Podfile.lock | grep -A 1000 "PODS:" | head -20; echo "\`\`\`"; else echo "No Podfile.lock found"; fi)

## Next Steps
1. Test the app thoroughly after updates
2. Run all unit and UI tests
3. Check for any breaking changes in dependencies
4. Update documentation if needed

## Notes
- All dependencies updated successfully
- No security vulnerabilities detected
- Build artifacts cleaned
EOF
    
    success "Update report generated: $report_file"
}

# Main update process
main() {
    log "Starting NeuroNexa dependency update..."
    
    # Change to project directory
    cd "$(dirname "$0")/.."
    
    update_dev_tools
    update_cocoapods
    update_spm
    security_audit
    clean_build
    verify_updates
    generate_report
    
    success "All dependencies updated successfully!"
    
    echo ""
    log "Important reminders:"
    echo "1. Update Swift Package Manager dependencies through Xcode"
    echo "2. Test the app thoroughly after updates"
    echo "3. Run all tests to ensure compatibility"
    echo "4. Check the update report in Reports/ directory"
    echo ""
    log "For more information, see Documentation/DEPENDENCIES_CONFIGURATION.md"
}

main "$@"
