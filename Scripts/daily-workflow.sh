#!/bin/bash

# NeuroNexa Daily Development Workflow Script
# Integrates MCP (Monitoring, Control, Planning) tools for optimal development

set -e

echo "🌅 NeuroNexa Daily Development Workflow Starting..."
echo "=================================================="

# Create reports directory if it doesn't exist
mkdir -p reports

# 1. MONITORING: Project Health Check
echo ""
echo "📊 MONITORING: Checking project health..."
echo "----------------------------------------"

# Check Git status
echo "📋 Git Status:"
git status --porcelain | head -10

# Check for uncommitted changes
UNCOMMITTED=$(git status --porcelain | wc -l)
if [ "$UNCOMMITTED" -gt 0 ]; then
    echo "⚠️  $UNCOMMITTED uncommitted changes detected"
else
    echo "✅ Working directory clean"
fi

# Check recent commits
echo ""
echo "📈 Recent Activity (last 24 hours):"
git log --oneline --since="1 day ago" | head -5

# 2. CONTROL: Code Quality Enforcement
echo ""
echo "🎛️ CONTROL: Applying code quality standards..."
echo "----------------------------------------------"

# Auto-format code
echo "🎨 Applying SwiftFormat..."
if command -v swiftformat &> /dev/null; then
    swiftformat . --config .swiftformat
    echo "✅ Code formatting applied"
else
    echo "⚠️  SwiftFormat not found, skipping formatting"
fi

# Fix auto-fixable linting issues
echo "🔧 Fixing SwiftLint issues..."
if command -v swiftlint &> /dev/null; then
    swiftlint --fix
    
    # Check remaining issues
    LINT_ISSUES=$(swiftlint --reporter json 2>/dev/null | jq -r '. | length' 2>/dev/null || echo "0")
    if [ "$LINT_ISSUES" -gt 0 ]; then
        echo "⚠️  $LINT_ISSUES linting issues remain"
        swiftlint | head -10
    else
        echo "✅ No linting issues found"
    fi
else
    echo "⚠️  SwiftLint not found, skipping lint checks"
fi

# 3. PLANNING: Development Progress Tracking
echo ""
echo "📋 PLANNING: Tracking development progress..."
echo "--------------------------------------------"

# Generate daily progress report
echo "📊 Daily Progress Report - $(date)" > reports/daily_progress.md
echo "=================================" >> reports/daily_progress.md
echo "" >> reports/daily_progress.md

# Recent commits
echo "## Recent Commits:" >> reports/daily_progress.md
git log --oneline --since="1 day ago" >> reports/daily_progress.md
echo "" >> reports/daily_progress.md

# Current branch info
echo "## Current Branch:" >> reports/daily_progress.md
echo "- Branch: $(git branch --show-current)" >> reports/daily_progress.md
echo "- Last commit: $(git log -1 --pretty=format:'%h - %s (%cr)')" >> reports/daily_progress.md
echo "" >> reports/daily_progress.md

# TODO/FIXME tracking
TODO_COUNT=$(grep -r "TODO\|FIXME" NeuroNexa/ 2>/dev/null | wc -l || echo "0")
echo "## Technical Debt:" >> reports/daily_progress.md
echo "- TODO/FIXME items: $TODO_COUNT" >> reports/daily_progress.md

echo "✅ Daily progress report generated: reports/daily_progress.md"

# 4. Quick Health Metrics
echo ""
echo "📈 METRICS: Quick health check..."
echo "--------------------------------"

# File count
FILE_COUNT=$(find NeuroNexa -name "*.swift" 2>/dev/null | wc -l || echo "0")
echo "📁 Swift files: $FILE_COUNT"

# Line count
LINE_COUNT=$(find NeuroNexa -name "*.swift" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")
echo "📝 Lines of code: $LINE_COUNT"

# Test files
TEST_COUNT=$(find NeuroNexa -name "*Test*.swift" 2>/dev/null | wc -l || echo "0")
echo "🧪 Test files: $TEST_COUNT"

# 5. Neurodiversity-Specific Checks
echo ""
echo "🧠 NEURODIVERSITY: Accessibility & Inclusion Checks..."
echo "-----------------------------------------------------"

# Check for accessibility labels
ACCESSIBILITY_LABELS=$(grep -r "accessibilityLabel" NeuroNexa/ 2>/dev/null | wc -l || echo "0")
echo "♿ Accessibility labels: $ACCESSIBILITY_LABELS"

# Check for cognitive load considerations
COGNITIVE_LOAD_REFS=$(grep -r "cognitiveLoad\|CognitiveLoad" NeuroNexa/ 2>/dev/null | wc -l || echo "0")
echo "🧠 Cognitive load references: $COGNITIVE_LOAD_REFS"

# Check for ADHD/Autism specific features
NEURODIVERSITY_REFS=$(grep -r "ADHD\|Autism\|neurodiversity" NeuroNexa/ 2>/dev/null | wc -l || echo "0")
echo "🎯 Neurodiversity features: $NEURODIVERSITY_REFS"

# 6. Ready for Development
echo ""
echo "🚀 READY: Development environment prepared!"
echo "=========================================="
echo ""
echo "📋 Next Steps:"
echo "1. Review daily progress report: reports/daily_progress.md"
echo "2. Address any remaining lint issues if found"
echo "3. Continue with planned development tasks"
echo "4. Run 'fastlane test' before committing changes"
echo ""

# Optional: Open daily progress report
if command -v open &> /dev/null && [[ "$OSTYPE" == "darwin"* ]]; then
    echo "📖 Opening daily progress report..."
    open reports/daily_progress.md
fi

echo "✅ Daily workflow completed successfully!"
echo "Happy coding! 🎉"
