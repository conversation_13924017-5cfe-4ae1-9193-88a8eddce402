#!/bin/bash

# NeuroNexa - Apple Intelligence Prohibition Validator
# This script ensures no Apple Intelligence code exists in the codebase

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}🚫 Validating Apple Intelligence Prohibition Policy${NC}"
echo "=================================================="

# Function to check for Apple Intelligence references
check_apple_intelligence_references() {
    echo -e "\n${BLUE}🔍 Scanning for Apple Intelligence References${NC}"
    echo "----------------------------------------------"
    
    local violations=0
    
    # Check for Apple Intelligence in Swift files (excluding comments)
    echo "Checking Swift files for Apple Intelligence references..."

    # Look for actual code references, not comments
    if grep -r "Apple.*Intelligence\|AppleIntelligence" --include="*.swift" "$PROJECT_ROOT" 2>/dev/null | grep -v "//.*Apple.*Intelligence" | grep -v "///.*Apple.*Intelligence" | grep -v "\*.*Apple.*Intelligence"; then
        echo -e "${RED}❌ Apple Intelligence code references found in Swift files${NC}"
        violations=$((violations + 1))
    else
        echo -e "${GREEN}✅ No Apple Intelligence code references in Swift files${NC}"
    fi
    
    # Check for Intelligence imports
    echo "Checking for Intelligence framework imports..."
    
    if grep -r "import.*Intelligence" --include="*.swift" "$PROJECT_ROOT" 2>/dev/null; then
        echo -e "${RED}❌ Intelligence framework imports found${NC}"
        violations=$((violations + 1))
    else
        echo -e "${GREEN}✅ No Intelligence framework imports found${NC}"
    fi
    
    # Check for AppleIntelligenceService references
    echo "Checking for AppleIntelligenceService references..."
    
    if grep -r "AppleIntelligenceService" --include="*.swift" "$PROJECT_ROOT" 2>/dev/null; then
        echo -e "${RED}❌ AppleIntelligenceService references found${NC}"
        violations=$((violations + 1))
    else
        echo -e "${GREEN}✅ No AppleIntelligenceService references found${NC}"
    fi
    
    return $violations
}

# Function to check for proper OpenAI implementation
check_openai_implementation() {
    echo -e "\n${BLUE}🤖 Validating OpenAI Implementation${NC}"
    echo "-----------------------------------"
    
    local issues=0
    
    # Check if OpenAI service exists
    if [[ -f "$PROJECT_ROOT/Core/Services/AI/OpenAITaskCoach.swift" ]]; then
        echo -e "${GREEN}✅ OpenAI service file exists${NC}"
    else
        echo -e "${RED}❌ OpenAI service file missing${NC}"
        issues=$((issues + 1))
    fi
    
    # Check if OpenAI protocol exists
    if grep -q "OpenAIIntelligenceServiceProtocol" "$PROJECT_ROOT/Core/Services/AI/OpenAITaskCoach.swift" 2>/dev/null; then
        echo -e "${GREEN}✅ OpenAI protocol defined${NC}"
    else
        echo -e "${RED}❌ OpenAI protocol missing${NC}"
        issues=$((issues + 1))
    fi
    
    # Check if DependencyContainer uses OpenAI
    if grep -q "OpenAIIntelligenceServiceProtocol" "$PROJECT_ROOT/Core/Architecture/DependencyContainer.swift" 2>/dev/null; then
        echo -e "${GREEN}✅ DependencyContainer uses OpenAI protocol${NC}"
    else
        echo -e "${RED}❌ DependencyContainer not updated for OpenAI${NC}"
        issues=$((issues + 1))
    fi
    
    return $issues
}

# Function to check SwiftLint custom rules
check_swiftlint_rules() {
    echo -e "\n${BLUE}📋 Validating SwiftLint Custom Rules${NC}"
    echo "------------------------------------"
    
    local issues=0
    
    # Check if SwiftLint config exists
    if [[ -f "$PROJECT_ROOT/.swiftlint.yml" ]]; then
        echo -e "${GREEN}✅ SwiftLint configuration exists${NC}"
        
        # Check for Apple Intelligence prohibition rules
        if grep -q "no_apple_intelligence" "$PROJECT_ROOT/.swiftlint.yml"; then
            echo -e "${GREEN}✅ Apple Intelligence prohibition rules configured${NC}"
        else
            echo -e "${RED}❌ Apple Intelligence prohibition rules missing${NC}"
            issues=$((issues + 1))
        fi
    else
        echo -e "${RED}❌ SwiftLint configuration missing${NC}"
        issues=$((issues + 1))
    fi
    
    return $issues
}

# Function to check policy documentation
check_policy_documentation() {
    echo -e "\n${BLUE}📚 Validating Policy Documentation${NC}"
    echo "----------------------------------"
    
    local issues=0
    
    # Check if policy document exists
    if [[ -f "$PROJECT_ROOT/Documentation/APPLE_INTELLIGENCE_PROHIBITION_POLICY.md" ]]; then
        echo -e "${GREEN}✅ Apple Intelligence prohibition policy documented${NC}"
        
        # Check if policy contains key sections
        local required_sections=(
            "STRICT PROHIBITION"
            "Enforcement Mechanisms"
            "Approved AI Implementation"
            "Migration Completed"
            "Violation Response Protocol"
        )
        
        for section in "${required_sections[@]}"; do
            if grep -q "$section" "$PROJECT_ROOT/Documentation/APPLE_INTELLIGENCE_PROHIBITION_POLICY.md"; then
                echo -e "${GREEN}  ✅ Section found: $section${NC}"
            else
                echo -e "${RED}  ❌ Missing section: $section${NC}"
                issues=$((issues + 1))
            fi
        done
    else
        echo -e "${RED}❌ Apple Intelligence prohibition policy documentation missing${NC}"
        issues=$((issues + 1))
    fi
    
    return $issues
}

# Function to run SwiftLint validation
run_swiftlint_validation() {
    echo -e "\n${BLUE}🔧 Running SwiftLint Validation${NC}"
    echo "-------------------------------"
    
    if command -v swiftlint >/dev/null 2>&1; then
        echo "Running SwiftLint with Apple Intelligence prohibition rules..."
        
        cd "$PROJECT_ROOT"
        
        # Run SwiftLint and capture output
        if swiftlint --quiet --reporter json > /tmp/swiftlint_output.json 2>/dev/null; then
            # Check for Apple Intelligence violations
            if command -v jq >/dev/null 2>&1; then
                local ai_violations=$(jq '[.[] | select(.rule | contains("apple_intelligence"))] | length' /tmp/swiftlint_output.json 2>/dev/null || echo "0")
                
                if [[ "$ai_violations" -eq 0 ]]; then
                    echo -e "${GREEN}✅ No Apple Intelligence violations found by SwiftLint${NC}"
                    return 0
                else
                    echo -e "${RED}❌ $ai_violations Apple Intelligence violations found by SwiftLint${NC}"
                    return 1
                fi
            else
                echo -e "${YELLOW}⚠️  jq not available, cannot parse SwiftLint JSON output${NC}"
                return 0
            fi
        else
            echo -e "${YELLOW}⚠️  SwiftLint execution failed, skipping validation${NC}"
            return 0
        fi
    else
        echo -e "${YELLOW}⚠️  SwiftLint not installed, skipping validation${NC}"
        return 0
    fi
}

# Function to check test files
check_test_files() {
    echo -e "\n${BLUE}🧪 Validating Test Files${NC}"
    echo "------------------------"
    
    local issues=0
    
    # Check if tests use OpenAI mocks instead of Apple Intelligence
    if grep -r "MockAppleIntelligence" --include="*.swift" "$PROJECT_ROOT/Tests" 2>/dev/null; then
        echo -e "${RED}❌ Apple Intelligence mocks found in tests${NC}"
        issues=$((issues + 1))
    else
        echo -e "${GREEN}✅ No Apple Intelligence mocks in tests${NC}"
    fi
    
    # Check if OpenAI mocks exist
    if grep -r "MockOpenAI" --include="*.swift" "$PROJECT_ROOT/Tests" 2>/dev/null; then
        echo -e "${GREEN}✅ OpenAI mocks found in tests${NC}"
    else
        echo -e "${YELLOW}⚠️  No OpenAI mocks found in tests${NC}"
    fi
    
    return $issues
}

# Main validation function
main() {
    local total_violations=0
    local total_issues=0
    
    echo -e "Project Root: $PROJECT_ROOT\n"
    
    # Run all validation checks
    check_apple_intelligence_references || total_violations=$((total_violations + $?))
    check_openai_implementation || total_issues=$((total_issues + $?))
    check_swiftlint_rules || total_issues=$((total_issues + $?))
    check_policy_documentation || total_issues=$((total_issues + $?))
    run_swiftlint_validation || total_violations=$((total_violations + 1))
    check_test_files || total_issues=$((total_issues + $?))
    
    # Summary
    echo -e "\n=================================================="
    
    if [[ $total_violations -eq 0 && $total_issues -eq 0 ]]; then
        echo -e "${GREEN}🎉 Apple Intelligence Prohibition Policy: FULLY COMPLIANT${NC}"
        echo -e "${GREEN}✅ No Apple Intelligence code detected${NC}"
        echo -e "${GREEN}✅ OpenAI implementation properly configured${NC}"
        echo -e "${GREEN}✅ All enforcement mechanisms in place${NC}"
        return 0
    elif [[ $total_violations -eq 0 ]]; then
        echo -e "${YELLOW}⚠️  Apple Intelligence Prohibition Policy: MOSTLY COMPLIANT${NC}"
        echo -e "${GREEN}✅ No Apple Intelligence code detected${NC}"
        echo -e "${YELLOW}⚠️  $total_issues configuration issues found${NC}"
        return 1
    else
        echo -e "${RED}❌ Apple Intelligence Prohibition Policy: VIOLATION DETECTED${NC}"
        echo -e "${RED}❌ $total_violations Apple Intelligence violations found${NC}"
        echo -e "${RED}❌ $total_issues configuration issues found${NC}"
        echo -e "\n${RED}IMMEDIATE ACTION REQUIRED:${NC}"
        echo -e "1. Remove all Apple Intelligence references"
        echo -e "2. Replace with OpenAI implementation"
        echo -e "3. Update tests to use OpenAI mocks"
        echo -e "4. See Documentation/APPLE_INTELLIGENCE_PROHIBITION_POLICY.md"
        return 2
    fi
}

# Run main function
main "$@"
