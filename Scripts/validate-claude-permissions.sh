#!/bin/bash

# NeuroNexa - Claude Code Controlled Write Permission Validator
# This script validates that Claude Code's controlled write permissions are properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/.claude-config.json"
RULES_FILE="$PROJECT_ROOT/AUGMENT_RULES.md"
CLAUDE_FILE="$PROJECT_ROOT/CLAUDE.md"
LOG_FILE="$PROJECT_ROOT/Logs/claude_modifications.json"

echo -e "${BLUE}🔍 Validating Claude Code Controlled Write Permissions${NC}"
echo "=================================================="

# Function to check if file exists
check_file_exists() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        echo -e "${GREEN}✅ $description exists${NC}"
        return 0
    else
        echo -e "${RED}❌ $description missing: $file${NC}"
        return 1
    fi
}

# Function to validate JSON file
validate_json() {
    local file="$1"
    local description="$2"
    
    if command -v jq >/dev/null 2>&1; then
        if jq empty "$file" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ $description is valid JSON${NC}"
            return 0
        else
            echo -e "${RED}❌ $description contains invalid JSON${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  jq not installed, skipping JSON validation for $description${NC}"
        return 0
    fi
}

# Function to check configuration values
check_config_values() {
    local config_file="$1"
    
    if command -v jq >/dev/null 2>&1; then
        # Check Claude role type
        local role_type=$(jq -r '.claude_role.type' "$config_file")
        if [[ "$role_type" == "secondary_agent_controlled_write" ]]; then
            echo -e "${GREEN}✅ Claude role correctly set to controlled write${NC}"
        else
            echo -e "${RED}❌ Claude role type incorrect: $role_type${NC}"
            return 1
        fi
        
        # Check permissions
        local permissions=$(jq -r '.claude_role.permissions' "$config_file")
        if [[ "$permissions" == "controlled_write" ]]; then
            echo -e "${GREEN}✅ Permissions correctly set to controlled_write${NC}"
        else
            echo -e "${RED}❌ Permissions incorrect: $permissions${NC}"
            return 1
        fi
        
        # Check controlled write permissions
        local swiftui_perms=$(jq -r '.controlled_write_permissions.swiftui_view_optimizations' "$config_file")
        local accessibility_perms=$(jq -r '.controlled_write_permissions.accessibility_enhancements' "$config_file")
        local style_perms=$(jq -r '.controlled_write_permissions.code_style_fixes' "$config_file")
        
        if [[ "$swiftui_perms" == "true" && "$accessibility_perms" == "true" && "$style_perms" == "true" ]]; then
            echo -e "${GREEN}✅ Controlled write permissions properly configured${NC}"
        else
            echo -e "${RED}❌ Controlled write permissions not properly configured${NC}"
            return 1
        fi
        
        # Check restrictions
        local git_restricted=$(jq -r '.strict_restrictions.git_operations' "$config_file")
        local deps_restricted=$(jq -r '.strict_restrictions.dependency_management' "$config_file")
        
        if [[ "$git_restricted" == "false" && "$deps_restricted" == "false" ]]; then
            echo -e "${GREEN}✅ Critical restrictions properly enforced${NC}"
        else
            echo -e "${RED}❌ Critical restrictions not properly configured${NC}"
            return 1
        fi
    fi
    
    return 0
}

# Function to check directory structure
check_directory_structure() {
    local directories=(
        "Documentation"
        "Logs"
        "Logs/Backups"
        "Scripts"
    )
    
    for dir in "${directories[@]}"; do
        local full_path="$PROJECT_ROOT/$dir"
        if [[ -d "$full_path" ]]; then
            echo -e "${GREEN}✅ Directory exists: $dir${NC}"
        else
            echo -e "${YELLOW}⚠️  Creating missing directory: $dir${NC}"
            mkdir -p "$full_path"
        fi
    done
}

# Function to validate approval workflow
check_approval_workflow() {
    local workflow_file="$PROJECT_ROOT/Documentation/CLAUDE_APPROVAL_WORKFLOW.md"
    
    if [[ -f "$workflow_file" ]]; then
        echo -e "${GREEN}✅ Approval workflow documentation exists${NC}"
        
        # Check for key sections
        local sections=(
            "Approval Process Flow"
            "Approved Categories"
            "Diff Preview Requirements"
            "Implementation Process"
            "Change Logging System"
            "Rollback Procedures"
        )
        
        for section in "${sections[@]}"; do
            if grep -q "$section" "$workflow_file"; then
                echo -e "${GREEN}  ✅ Section found: $section${NC}"
            else
                echo -e "${RED}  ❌ Missing section: $section${NC}"
                return 1
            fi
        done
    else
        echo -e "${RED}❌ Approval workflow documentation missing${NC}"
        return 1
    fi
}

# Function to check log file structure
check_log_structure() {
    local log_file="$1"
    
    if command -v jq >/dev/null 2>&1; then
        # Check required top-level keys
        local required_keys=("metadata" "configuration" "statistics" "modifications")
        
        for key in "${required_keys[@]}"; do
            if jq -e ".$key" "$log_file" >/dev/null 2>&1; then
                echo -e "${GREEN}  ✅ Log structure has: $key${NC}"
            else
                echo -e "${RED}  ❌ Log structure missing: $key${NC}"
                return 1
            fi
        done
    fi
    
    return 0
}

# Main validation process
main() {
    local exit_code=0
    
    echo -e "\n${BLUE}📁 Checking File Structure${NC}"
    echo "----------------------------"
    
    check_file_exists "$CONFIG_FILE" "Claude configuration file" || exit_code=1
    check_file_exists "$RULES_FILE" "Augment rules file" || exit_code=1
    check_file_exists "$CLAUDE_FILE" "Claude instructions file" || exit_code=1
    check_file_exists "$LOG_FILE" "Claude modifications log" || exit_code=1
    
    echo -e "\n${BLUE}📂 Checking Directory Structure${NC}"
    echo "--------------------------------"
    check_directory_structure
    
    echo -e "\n${BLUE}🔧 Validating Configuration Files${NC}"
    echo "----------------------------------"
    
    validate_json "$CONFIG_FILE" "Claude configuration" || exit_code=1
    validate_json "$LOG_FILE" "Modifications log" || exit_code=1
    
    echo -e "\n${BLUE}⚙️  Checking Configuration Values${NC}"
    echo "---------------------------------"
    check_config_values "$CONFIG_FILE" || exit_code=1
    
    echo -e "\n${BLUE}📋 Validating Approval Workflow${NC}"
    echo "-------------------------------"
    check_approval_workflow || exit_code=1
    
    echo -e "\n${BLUE}📊 Checking Log Structure${NC}"
    echo "-------------------------"
    check_log_structure "$LOG_FILE" || exit_code=1
    
    echo -e "\n${BLUE}🔐 Security Validation${NC}"
    echo "---------------------"
    
    # Check that critical files are not in allowed modification paths
    if grep -q "DependencyContainer" "$CONFIG_FILE" 2>/dev/null; then
        echo -e "${RED}❌ DependencyContainer found in allowed paths${NC}"
        exit_code=1
    else
        echo -e "${GREEN}✅ Core architecture files protected${NC}"
    fi
    
    # Summary
    echo -e "\n=================================================="
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}🎉 All Claude Code controlled write permissions validated successfully!${NC}"
        echo -e "${GREEN}✅ Claude Code is ready for controlled file modifications${NC}"
    else
        echo -e "${RED}❌ Validation failed. Please fix the issues above before proceeding.${NC}"
    fi
    
    return $exit_code
}

# Run main function
main "$@"
