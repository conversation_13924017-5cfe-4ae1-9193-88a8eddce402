name: NeuroNexa
options:
  bundleIdPrefix: com.neuronexa
  deploymentTarget:
    iOS: "18.5"
  developmentLanguage: en
  xcodeVersion: "16.0"

settings:
  SWIFT_VERSION: "6.0"
  IPHONEOS_DEPLOYMENT_TARGET: "18.5"
  TARGETED_DEVICE_FAMILY: "1"
  ENABLE_BITCODE: NO
  SWIFT_STRICT_CONCURRENCY: complete

targets:
  NeuroNexa:
    type: application
    platform: iOS
    deploymentTarget: "18.5"
    sources:
      - path: App
      - path: Core
      - path: UI
      - path: Services
      - path: ViewModels
      - path: Views
    resources:
      - Info.plist
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.neuronexa.ios
      PRODUCT_NAME: NeuroNexa
      INFOPLIST_FILE: Info.plist
      SWIFT_VERSION: "6.0"
      ENABLE_PREVIEWS: YES
      DEVELOPMENT_ASSET_PATHS: ""
      CODE_SIGN_STYLE: Automatic
      DEVELOPMENT_TEAM: ""
    dependencies:
      - framework: SwiftUI.framework
      - framework: HealthKit.framework
      - framework: Combine.framework
      - framework: Foundation.framework
      - framework: UIKit.framework
    preBuildScripts:
      - name: SwiftLint
        script: |
          if which swiftlint >/dev/null; then
            swiftlint
          else
            echo "warning: SwiftLint not installed"
          fi

  NeuroNexaTests:
    type: bundle.unit-test
    platform: iOS
    sources:
      - path: Tests/NeuroNexaTests
    dependencies:
      - target: NeuroNexa

  NeuroNexaUITests:
    type: bundle.ui-testing
    platform: iOS
    sources:
      - path: Tests/NeuroNexaUITests
    dependencies:
      - target: NeuroNexa

schemes:
  NeuroNexa:
    build:
      targets:
        NeuroNexa: all
        NeuroNexaTests: [test]
        NeuroNexaUITests: [test]
    run:
      config: Debug
    test:
      config: Debug
      targets:
        - NeuroNexaTests
        - NeuroNexaUITests
    archive:
      config: Release
