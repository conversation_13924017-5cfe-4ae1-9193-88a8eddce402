warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/118)
Linting 'NeuroNexaApp.swift' (2/118)
Linting 'AppDelegate.swift' (3/118)
Linting 'SceneDelegate.swift' (4/118)
Linting 'SettingsViewModel.swift' (5/118)
Linting 'BreathingViewModel.swift' (6/118)
Linting 'AITaskCoachViewModel.swift' (7/118)
Linting 'DashboardViewModel.swift' (8/118)
Linting 'iOS26Extensions.swift' (9/118)
Linting 'NeuroNexaDesignSystem.swift' (10/118)
Linting 'CognitiveProgressViewStyle.swift' (11/118)
Linting 'TaskCardConfiguration.swift' (12/118)
Linting 'BreathingSupportingViews.swift' (15/118)
Linting 'CognitiveButton.swift' (13/118)
Linting 'TaskCard.swift' (14/118)
Linting 'BreathingHelpers.swift' (16/118)
Linting 'BreathingOverlayViews.swift' (18/118)
Linting 'AnxietyDetectionSheet.swift' (17/118)
Linting 'UI/Views/BreathingView.swift' (19/118)
Linting 'UI/Views/Breathing/BreathingView.swift' (20/118)
Linting 'UI/Views/DashboardView.swift' (21/118)
Linting 'UI/Views/Settings/SettingsView.swift' (22/118)
Linting 'BreathingContentViews.swift' (23/118)
Linting 'AITaskCoachView.swift' (24/118)
Linting 'ContentView.swift' (25/118)
Linting 'UserRepository.swift' (26/118)
Linting 'UI/Views/SettingsView.swift' (27/118)
Linting 'UI/Views/Dashboard/DashboardView.swift' (28/118)
Linting 'TaskRepository.swift' (29/118)
Linting 'UserProfileRepository.swift' (30/118)
Linting 'RoutineRepository.swift' (31/118)
Linting 'BreathingSessionRepository.swift' (32/118)
Linting 'NeurodiversityEnums.swift' (33/118)
Linting 'BehaviorModels.swift' (34/118)
Linting 'CognitivePatternModels.swift' (35/118)
Linting 'OpenAIModels.swift' (36/118)
Linting 'SettingsModels.swift' (37/118)
Linting 'NeuroNexaModels.swift' (38/118)
Linting 'TaskEnums.swift' (39/118)
Linting 'BehaviorPredictionModels.swift' (40/118)
Linting 'UserPreferences.swift' (42/118)
Linting 'TaskTimingModels.swift' (41/118)
Linting 'OpenAIUserContextModels.swift' (43/118)
Linting 'ViewPlaceholders.swift' (44/118)
Linting 'BreathingModels.swift' (45/118)
Linting 'NeurodiversityServices.swift' (46/118)
Linting 'OpenAITaskModels.swift' (47/118)
Linting 'SensoryOptimizationModels.swift' (49/118)
Linting 'ExecutiveFunctionModels.swift' (48/118)
Linting 'OpenAITypes.swift' (50/118)
Linting 'OpenAIBreakModels.swift' (51/118)
Linting 'OpenAICoachModels.swift' (52/118)
Linting 'CognitiveOptimizationModels.swift' (53/118)
Linting 'User.swift' (54/118)
Linting 'AccessibilitySettings.swift' (55/118)
Linting 'OpenAICognitiveAdaptationModels.swift' (56/118)
Linting 'PersonalizedContentTypes.swift' (58/118)
Linting 'SensoryEnums.swift' (57/118)
Linting 'NeuroNexaServices.swift' (59/118)
Linting 'CognitivePreferencesModels.swift' (60/118)
Linting 'NeuroNexaTheme.swift' (61/118)
Linting 'CognitiveSupportingTypes.swift' (62/118)
Linting 'NeurodiversityTypes.swift' (63/118)
Linting 'SensoryModels.swift' (64/118)
Linting 'SharedTypes.swift' (65/118)
Linting 'UserProfileModels.swift' (66/118)
Linting 'CognitiveModels.swift' (67/118)
Linting 'BreakSuggestionTypes.swift' (68/118)
Linting 'OpenAIContentModels.swift' (69/118)
Linting 'SensoryCognitiveModels.swift' (70/118)
Linting 'OpenAITaskCoachModels.swift' (71/118)
Linting 'CognitiveAnalysisModels.swift' (72/118)
Linting 'CognitiveAdaptationTypes.swift' (73/118)
Linting 'SensoryAdaptationModels.swift' (75/118)
Linting 'BreathingEnums.swift' (74/118)
Linting 'TaskModels.swift' (76/118)
Linting 'WellnessEnums.swift' (77/118)
Linting 'PrivacySettings.swift' (78/118)
Linting 'BehaviorInsightsModels.swift' (79/118)
Linting 'BehaviorAnalysisModels.swift' (80/118)
Linting 'ViewModel.swift' (81/118)
Linting 'DependencyContainer.swift' (82/118)
Linting 'Coordinator.swift' (83/118)
Linting 'CognitiveLoadService.swift' (85/118)
Linting 'ExecutiveFunctionService.swift' (86/118)
Linting 'CognitiveAnalysisServiceExtensions.swift' (87/118)
Linting 'ServiceProtocols.swift' (84/118)
Linting 'UserService.swift' (88/118)
Linting 'CoreDataService.swift' (89/118)
Linting 'PersonalizedTaskServiceExtensions.swift' (90/118)
Linting 'BreathingServiceHelpers.swift' (91/118)
Linting 'SensoryAdaptationService.swift' (92/118)
Linting 'WatchConnectivityService.swift' (93/118)
Linting 'HealthKitService.swift' (94/118)
Linting 'BasicServiceImplementations.swift' (95/118)
Linting 'OpenAIErrors.swift' (96/118)
Linting 'OpenAIService.swift' (97/118)
Linting 'OpenAITaskCoachParsing.swift' (98/118)
Linting 'OpenAITaskCoach.swift' (99/118)
Linting 'OpenAITaskCoachHelpers.swift' (100/118)
Linting 'PersonalizedTaskHelpers.swift' (101/118)
Linting 'OpenAITaskCoachPrompts.swift' (102/118)
Linting 'OpenAITaskCoachExtensions.swift' (103/118)
Linting 'CognitiveAnalysisService.swift' (104/118)
Linting 'CognitiveAnalysisHelpers.swift' (105/118)
Linting 'PersonalizedTaskService.swift' (106/118)
Linting 'BreathingService.swift' (107/118)
Linting 'AuthenticationService.swift' (109/118)
Linting 'CloudKitService.swift' (110/118)
Linting 'NeuroNexaUITests.swift' (111/118)
Linting 'SettingsService.swift' (108/118)
Linting 'NeuroNexaTests.swift' (112/118)
Linting 'NeurodiversityAccessibilityTests.swift' (113/118)
Linting 'AccessibilityAuditTests.swift' (114/118)
Linting 'BreathingExerciseServiceTests.swift' (115/118)
Linting 'BreathingSessionTests.swift' (116/118)
Linting 'AITaskCoachServiceTests.swift' (117/118)
Linting 'AITaskCoachServiceTestsExtensions.swift' (118/118)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 423,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisService.swift",
    "line" : 20,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 347 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskService.swift",
    "line" : 20,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 359 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "File should contain 500 lines or less: currently contains 501",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  }
]
Done linting! Found 5 violations, 0 serious in 118 files.
