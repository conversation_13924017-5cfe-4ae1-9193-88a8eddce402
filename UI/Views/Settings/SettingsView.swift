import SwiftUI

@available(iOS 18.0, *)
struct SettingsView: View {
    @State private var notificationsEnabled: Bool = true
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        NavigationStack {
            List {
                // Profile Section
                profileSection
                
                // Neurodiversity Settings
                neurodiversitySection
                
                // Accessibility Settings
                accessibilitySection
                
                // Privacy & Data
                privacySection
                
                // AI & Coaching
                aiSection
                
                // Support & About
                supportSection
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.neuroNexaBackground)
            .accessibilityElement(children: .contain)
            .accessibilityLabel("Settings and preferences")
            .accessibilityHint("Customize your NeuroNexa experience")
        }
        .onAppear {
            // Settings loaded automatically
        }
    }
    
    private var profileSection: some View {
        Section {
            HStack {
                Image(systemName: "person.circle.fill")
                    .font(.title2)
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("Profile picture")
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("User")
                        .font(.headline)
                        .foregroundColor(.neuroNexaText)

                    Text("No email set")
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
                
                Spacer()
                
                Button("Edit") {
                    // Edit profile action
                }
                .font(.caption)
                .foregroundColor(.neuroNexaPrimary)
                .accessibilityLabel("Edit profile")
                .accessibilityHint("Modify your profile information")
            }
            .padding(.vertical, 8)
        } header: {
            Text("Profile")
                .accessibilityAddTraits(.isHeader)
        }
    }
    
    private var neurodiversitySection: some View {
        Section {
            NavigationLink(destination: Text("Cognitive Load Settings")) {
                SettingsRow(
                    icon: "brain.head.profile",
                    title: "Cognitive Load Settings",
                    subtitle: "Customize cognitive assistance",
                    iconColor: .neuroNexaPrimary
                )
            }
            .accessibilityLabel("Cognitive Load Settings")
            .accessibilityHint("Adjust cognitive assistance preferences")
            
            NavigationLink(destination: Text("Sensory Adaptation Settings")) {
                SettingsRow(
                    icon: "eye.circle",
                    title: "Sensory Adaptations",
                    subtitle: "Visual and sensory preferences",
                    iconColor: .blue
                )
            }
            .accessibilityLabel("Sensory Adaptations")
            .accessibilityHint("Customize visual and sensory settings")
            
            NavigationLink(destination: Text("Executive Function Settings")) {
                SettingsRow(
                    icon: "list.bullet.clipboard",
                    title: "Executive Function Support",
                    subtitle: "Task management preferences",
                    iconColor: .green
                )
            }
            .accessibilityLabel("Executive Function Support")
            .accessibilityHint("Configure task management assistance")
        } header: {
            Text("Neurodiversity Support")
                .accessibilityAddTraits(.isHeader)
        }
    }
    
    private var accessibilitySection: some View {
        Section {
            Toggle("Reduce Motion", isOn: $viewModel.reduceMotion)
                .accessibilityLabel("Reduce motion animations")
                .accessibilityHint("Minimizes animated transitions")
                .accessibilityIdentifier("ReduceMotionToggle")
                .onChange(of: viewModel.reduceMotion) { _, newValue in
                    UIAccessibility.post(
                        notification: .announcement,
                        argument: "Motion reduction \(newValue ? "enabled" : "disabled")"
                    )
                }
            
            Toggle("High Contrast", isOn: $viewModel.highContrast)
                .accessibilityLabel("High contrast mode")
                .accessibilityHint("Increases color contrast for better visibility")
                .accessibilityIdentifier("HighContrastToggle")
                .onChange(of: viewModel.highContrast) { _, newValue in
                    UIAccessibility.post(
                        notification: .announcement,
                        argument: "High contrast \(newValue ? "enabled" : "disabled")"
                    )
                }
            
            Toggle("Large Text", isOn: $viewModel.largeText)
                .accessibilityLabel("Large text mode")
                .accessibilityHint("Increases text size throughout the app")
                .accessibilityIdentifier("LargeTextToggle")
                .onChange(of: viewModel.largeText) { _, newValue in
                    UIAccessibility.post(
                        notification: .announcement,
                        argument: "Large text \(newValue ? "enabled" : "disabled")"
                    )
                }
            
            HStack {
                Text("Haptic Feedback")
                    .foregroundColor(.neuroNexaText)
                
                Spacer()
                
                Picker("Haptic Feedback", selection: $viewModel.hapticFeedback) {
                    Text("Off").tag(HapticFeedbackLevel.off)
                    Text("Light").tag(HapticFeedbackLevel.light)
                    Text("Medium").tag(HapticFeedbackLevel.medium)
                    Text("Strong").tag(HapticFeedbackLevel.strong)
                }
                .pickerStyle(MenuPickerStyle())
                .accessibilityLabel("Haptic feedback intensity")
                .accessibilityHint("Choose haptic feedback strength")
            }
        } header: {
            Text("Accessibility")
                .accessibilityAddTraits(.isHeader)
        }
    }
    
    private var privacySection: some View {
        Section {
            NavigationLink(destination: PrivacySettingsView()) {
                SettingsRow(
                    icon: "lock.shield",
                    title: "Privacy & Security",
                    subtitle: "Data protection settings",
                    iconColor: .red
                )
            }
            .accessibilityLabel("Privacy and Security")
            .accessibilityHint("Manage data protection and privacy settings")
            
            NavigationLink(destination: HealthDataSettingsView()) {
                SettingsRow(
                    icon: "heart.circle",
                    title: "Health Data",
                    subtitle: "HealthKit integration",
                    iconColor: .pink
                )
            }
            .accessibilityLabel("Health Data Settings")
            .accessibilityHint("Configure health data sharing preferences")
        } header: {
            Text("Privacy & Data")
                .accessibilityAddTraits(.isHeader)
        }
    }
    
    private var aiSection: some View {
        Section {
            NavigationLink(destination: AICoachSettingsView()) {
                SettingsRow(
                    icon: "sparkles",
                    title: "AI Coach Settings",
                    subtitle: "Personalization preferences",
                    iconColor: .purple
                )
            }
            .accessibilityLabel("AI Coach Settings")
            .accessibilityHint("Customize AI coaching behavior")
            
            Toggle("Background Processing", isOn: $viewModel.backgroundProcessing)
                .accessibilityLabel("Background AI processing")
                .accessibilityHint("Allow AI to process data in background")
        } header: {
            Text("AI & Coaching")
                .accessibilityAddTraits(.isHeader)
        }
    }
    
    private var supportSection: some View {
        Section {
            NavigationLink(destination: HelpView()) {
                SettingsRow(
                    icon: "questionmark.circle",
                    title: "Help & Support",
                    subtitle: "Get assistance",
                    iconColor: .orange
                )
            }
            .accessibilityLabel("Help and Support")
            .accessibilityHint("Access help documentation and support")
            
            NavigationLink(destination: AboutView()) {
                SettingsRow(
                    icon: "info.circle",
                    title: "About NeuroNexa",
                    subtitle: "Version info and credits",
                    iconColor: .gray
                )
            }
            .accessibilityLabel("About NeuroNexa")
            .accessibilityHint("View app information and version details")
        } header: {
            Text("Support")
                .accessibilityAddTraits(.isHeader)
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let iconColor: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(iconColor)
                .frame(width: 28, height: 28)
                .accessibilityLabel("\(title) icon")
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .foregroundColor(.neuroNexaText)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.neuroNexaTextSecondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
    }
}

// MARK: - Supporting Types

enum HapticFeedbackLevel: String, CaseIterable {
    case off = "Off"
    case light = "Light"
    case medium = "Medium"
    case strong = "Strong"
}

#Preview {
    SettingsView()
}
