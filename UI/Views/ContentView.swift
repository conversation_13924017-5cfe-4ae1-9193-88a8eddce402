import SwiftUI

@available(iOS 18.0, *)
struct ContentView: View {
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager
    @State private var selectedTab = 0
    @State private var showingOnboarding = false

    var body: some View {
        Group {
            if appState.isAuthenticated {
                MainTabView(selectedTab: $selectedTab)
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
            } else {
                AuthenticationView()
                    .transition(.asymmetric(
                        insertion: .move(edge: .leading).combined(with: .opacity),
                        removal: .move(edge: .trailing).combined(with: .opacity)
                    ))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: appState.isAuthenticated)
        .sheet(isPresented: $showingOnboarding) {
            OnboardingView()
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
        }
        .task {
            await checkOnboardingStatus()
        }
        .accessibilityEnhanced()
        .cognitiveLoadOptimized()
        .sensoryAdaptive()
        .accessibilityElement(children: .contain)
        .accessibilityLabel("NeuroNexa Main Interface")
        .accessibilityHint("Navigate between authenticated and authentication views")
    }

    @MainActor
    private func checkOnboardingStatus() async {
        // Use async/await pattern for iOS 26 best practices
        let hasCompletedOnboarding = await Task.detached {
            UserDefaults.standard.bool(forKey: "HasCompletedOnboarding")
        }.value

        if !hasCompletedOnboarding {
            withAnimation(.easeInOut(duration: 0.5)) {
                showingOnboarding = true
            }
        }
    }
}

@available(iOS 18.0, *)
struct MainTabView: View {
    @Binding var selectedTab: Int
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager

    // iOS 26 optimization: Use @State for tab animation
    @State private var tabTransition: AnyTransition = .opacity

    var body: some View {
        TabView(selection: $selectedTab) {
            // Dashboard
            DashboardView()
                .tabItem {
                    Label("Dashboard", systemImage: "house.fill")
                        .accessibilityLabel("Dashboard Tab")
                        .accessibilityHint("View your personalized dashboard")
                }
                .tag(0)
                .toolbarBackground(.visible, for: .tabBar)
                .accessibilityIdentifier("DashboardTab")

            // AI Task Coach
            // Claude: PERFORMANCE - Extract service initialization to avoid recreation
            AITaskCoachTabView()
            .tabItem {
                Image(systemName: "brain.head.profile")
                    .accessibilityLabel("AI Coach")
                Text("AI Coach")
            }
            .tag(1)

            // Breathing Exercises
            // Claude: PERFORMANCE - Extract service initialization to avoid recreation
            BreathingTabView()
            .tabItem {
                Image(systemName: "lungs.fill")
                Text("Breathe")
            }
            .tag(2)
            .accessibilityLabel("Breathing exercises tab")

            // Routine Builder
            // Claude: PERFORMANCE - Extract ViewModel initialization
            RoutineBuilderTabView()
                .tabItem {
                    Image(systemName: "calendar")
                    Text("Routines")
                }
                .tag(3)
                .accessibilityLabel("Routine builder tab")

            // Settings
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("Settings")
                }
                .tag(4)
                .accessibilityLabel("Settings tab")
        }
        .accentColor(.neuroNexaPrimary)
        .cognitiveLoadAdaptive()
        // Claude: ACCESSIBILITY - Enhanced tab navigation with announcements
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Main navigation tabs")
        .accessibilityIdentifier("MainTabView")
        .onChange(of: selectedTab) { _, newValue in
            let tabNames = ["Dashboard", "AI Coach", "Breathing", "Routines", "Settings"]
            if newValue < tabNames.count {
                UIAccessibility.post(
                    notification: .screenChanged,
                    argument: "\(tabNames[newValue]) tab selected"
                )
            }
        }
    }
}

// MARK: - Tab View Wrappers
// Claude: PERFORMANCE - Extracted tab views to prevent heavy initialization in TabView

@available(iOS 18.0, *)
struct AITaskCoachTabView: View {
    @StateObject private var viewModel = AITaskCoachViewModel(
        aiTaskCoachService: DependencyContainer.shared.aiTaskCoachService,
        cognitiveLoadService: DependencyContainer.shared.cognitiveLoadService,
        userRepository: UserRepository(),
        taskRepository: TaskRepository()
    )
    
    var body: some View {
        AITaskCoachView(viewModel: viewModel)
    }
}

@available(iOS 18.0, *)
struct BreathingTabView: View {
    @StateObject private var viewModel = BreathingViewModel(
        breathingService: BreathingExerciseService(
            healthKitService: DependencyContainer.shared.healthKitService,
            watchConnectivityService: DependencyContainer.shared.watchConnectivityService,
            sensoryAdaptationService: DependencyContainer.shared.sensoryAdaptationService
        ),
        userRepository: UserRepository()
    )
    
    var body: some View {
        AdvancedBreathingView(viewModel: viewModel)
    }
}

@available(iOS 18.0, *)
struct RoutineBuilderTabView: View {
    @StateObject private var viewModel = RoutineBuilderViewModel()
    
    var body: some View {
        RoutineBuilderView(viewModel: viewModel)
    }
}
