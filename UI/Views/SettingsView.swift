import SwiftUI

@available(iOS 18.0, *)
struct SettingsView: View {
    @StateObject private var viewModel = SettingsViewModel()
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            Form {
                // Profile Section
                profileSection

                // Neurodiversity Settings
                neurodiversitySection

                // Accessibility Settings
                accessibilitySection

                // Cognitive Load Settings
                cognitiveLoadSection

                // Notifications
                notificationsSection

                // Privacy & Data
                privacySection

                // About
                aboutSection
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            Task {
                await viewModel.loadSettings()
            }
        }
    }

    private var profileSection: some View {
        Section("Profile") {
            HStack {
                Image(systemName: "person.circle.fill")
                    .font(.largeTitle)
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("User profile avatar - your personal account")

                VStack(alignment: .leading, spacing: 4) {
                    Text(viewModel.userName)
                        .font(.headline)
                        .foregroundColor(.neuroNexaText)

                    Text(viewModel.userEmail)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }

                Spacer()

                Button("Edit") {
                    viewModel.showProfileEditor = true
                }
                .font(.caption)
                .foregroundColor(.neuroNexaPrimary)
            }
            .padding(.vertical, 8)
        }
        .sheet(isPresented: $viewModel.showProfileEditor) {
            ProfileEditorView(viewModel: viewModel)
        }
    }

    private var neurodiversitySection: some View {
        Section("Neurodiversity Profile") {
            NavigationLink("Cognitive Profile") {
                CognitiveProfileView(viewModel: viewModel)
            }

            NavigationLink("Sensory Preferences") {
                SensoryPreferencesView(viewModel: viewModel)
            }

            NavigationLink("Executive Function Support") {
                ExecutiveFunctionView(viewModel: viewModel)
            }

            Picker("Primary Support Need", selection: $viewModel.primarySupportNeed) {
                Text("ADHD").tag(NeurodiversityType.adhd)
                Text("Autism").tag(NeurodiversityType.autism)
                Text("Dyslexia").tag(NeurodiversityType.dyslexia)
                Text("Anxiety").tag(NeurodiversityType.anxiety)
                Text("Multiple").tag(NeurodiversityType.multiple)
            }
        }
    }

    private var accessibilitySection: some View {
        Section("Accessibility") {
            Toggle("VoiceOver Support", isOn: $viewModel.voiceOverEnabled)

            Picker("Text Size", selection: $viewModel.preferredTextSize) {
                Text("Small").tag(DynamicTypeSize.small)
                Text("Medium").tag(DynamicTypeSize.medium)
                Text("Large").tag(DynamicTypeSize.large)
                Text("Extra Large").tag(DynamicTypeSize.xLarge)
                Text("XXL").tag(DynamicTypeSize.xxLarge)
            }

            Toggle("Reduce Motion", isOn: $viewModel.reduceMotionEnabled)
            Toggle("Increase Contrast", isOn: $viewModel.increaseContrastEnabled)
            Toggle("Bold Text", isOn: $viewModel.boldTextEnabled)
            Toggle("Button Shapes", isOn: $viewModel.buttonShapesEnabled)
        }
    }

    private var cognitiveLoadSection: some View {
        Section("Cognitive Load Management") {
            Picker("Default Cognitive Load", selection: $viewModel.defaultCognitiveLoad) {
                Text("Low").tag(CognitiveLoadLevel.low)
                Text("Medium").tag(CognitiveLoadLevel.medium)
                Text("High").tag(CognitiveLoadLevel.high)
            }

            Toggle("Auto-Adapt Interface", isOn: $viewModel.autoAdaptInterface)
            Toggle("Break Reminders", isOn: $viewModel.breakRemindersEnabled)

            if viewModel.breakRemindersEnabled {
                Picker("Break Frequency", selection: $viewModel.breakFrequency) {
                    Text("Every 30 minutes").tag(30)
                    Text("Every 45 minutes").tag(45)
                    Text("Every 60 minutes").tag(60)
                    Text("Every 90 minutes").tag(90)
                }
            }

            Toggle("Cognitive Load Monitoring", isOn: $viewModel.cognitiveLoadMonitoring)
        }
    }

    private var notificationsSection: some View {
        Section("Notifications") {
            Toggle("Task Reminders", isOn: $viewModel.taskRemindersEnabled)
            Toggle("Break Notifications", isOn: $viewModel.breakNotificationsEnabled)
            Toggle("Routine Reminders", isOn: $viewModel.routineRemindersEnabled)
            Toggle("Mood Check-ins", isOn: $viewModel.moodCheckInsEnabled)

            if viewModel.taskRemindersEnabled {
                Picker("Reminder Style", selection: $viewModel.reminderStyle) {
                    Text("Gentle").tag(ReminderStyle.gentle)
                    Text("Standard").tag(ReminderStyle.standard)
                    Text("Persistent").tag(ReminderStyle.persistent)
                }
            }
        }
    }

    private var privacySection: some View {
        Section("Privacy & Data") {
            Toggle("Health Data Sharing", isOn: $viewModel.healthDataSharingEnabled)
            Toggle("Analytics", isOn: $viewModel.analyticsEnabled)
            Toggle("Crash Reporting", isOn: $viewModel.crashReportingEnabled)

            NavigationLink("Data Export") {
                DataExportView()
            }

            Button("Clear All Data") {
                viewModel.showClearDataAlert = true
            }
            .foregroundColor(.red)
        }
        .alert("Clear All Data", isPresented: $viewModel.showClearDataAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Clear", role: .destructive) {
                Task {
                    await viewModel.clearAllData()
                }
            }
        } message: {
            Text("This will permanently delete all your data. This action cannot be undone.")
        }
    }

    private var aboutSection: some View {
        Section("About") {
            HStack {
                Text("Version")
                Spacer()
                Text(viewModel.appVersion)
                    .foregroundColor(.neuroNexaTextSecondary)
            }

            NavigationLink("Privacy Policy") {
                PrivacyPolicyView()
            }

            NavigationLink("Terms of Service") {
                TermsOfServiceView()
            }

            NavigationLink("Support") {
                SupportView()
            }

            Button("Rate NeuroNexa") {
                viewModel.rateApp()
            }
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct ProfileEditorView: View {
    @ObservedObject var viewModel: SettingsViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            Form {
                Section("Basic Information") {
                    TextField("Name", text: $viewModel.editingUserName)
                    TextField("Email", text: $viewModel.editingUserEmail)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                }

                Section("Preferences") {
                    Picker("Preferred Theme", selection: $viewModel.preferredTheme) {
                        Text("Adaptive").tag(NeuroNexaTheme.adaptive)
                        Text("Light").tag(NeuroNexaTheme.light)
                        Text("Dark").tag(NeuroNexaTheme.dark)
                        Text("High Contrast").tag(NeuroNexaTheme.highContrast)
                    }
                }
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await viewModel.saveProfile()
                            dismiss()
                        }
                    }
                }
            }
        }
    }
}

@available(iOS 18.0, *)
struct CognitiveProfileView: View {
    @ObservedObject var viewModel: SettingsViewModel

    var body: some View {
        Form {
            Section("Neurodiversity Types") {
                ForEach(NeurodiversityType.allCases, id: \.self) { type in
                    Toggle(type.displayName, isOn: binding(for: type))
                }
            }

            Section("Cognitive Strengths") {
                ForEach(CognitiveStrength.allCases, id: \.self) { strength in
                    Toggle(strength.displayName, isOn: strengthBinding(for: strength))
                }
            }

            Section("Support Needs") {
                ForEach(CognitiveSupport.allCases, id: \.self) { support in
                    Toggle(support.displayName, isOn: supportBinding(for: support))
                }
            }
        }
        .navigationTitle("Cognitive Profile")
        .navigationBarTitleDisplayMode(.inline)
    }

    private func binding(for type: NeurodiversityType) -> Binding<Bool> {
        Binding(
            get: { viewModel.selectedNeurodiversityTypes.contains(type) },
            set: { isSelected in
                if isSelected {
                    viewModel.selectedNeurodiversityTypes.insert(type)
                } else {
                    viewModel.selectedNeurodiversityTypes.remove(type)
                }
            }
        )
    }

    private func strengthBinding(for strength: CognitiveStrength) -> Binding<Bool> {
        Binding(
            get: { viewModel.selectedCognitiveStrengths.contains(strength) },
            set: { isSelected in
                if isSelected {
                    viewModel.selectedCognitiveStrengths.insert(strength)
                } else {
                    viewModel.selectedCognitiveStrengths.remove(strength)
                }
            }
        )
    }

    private func supportBinding(for support: CognitiveSupport) -> Binding<Bool> {
        Binding(
            get: { viewModel.selectedCognitiveSupports.contains(support) },
            set: { isSelected in
                if isSelected {
                    viewModel.selectedCognitiveSupports.insert(support)
                } else {
                    viewModel.selectedCognitiveSupports.remove(support)
                }
            }
        )
    }
}

@available(iOS 18.0, *)
struct SensoryPreferencesView: View {
    @ObservedObject var viewModel: SettingsViewModel

    var body: some View {
        Form {
            Section("Visual Preferences") {
                Picker("Brightness Preference", selection: $viewModel.brightnessPreference) {
                    Text("Dim").tag(BrightnessLevel.dim)
                    Text("Normal").tag(BrightnessLevel.normal)
                    Text("Bright").tag(BrightnessLevel.bright)
                }

                Picker("Color Intensity", selection: $viewModel.colorIntensity) {
                    Text("Muted").tag(ColorIntensity.muted)
                    Text("Normal").tag(ColorIntensity.normal)
                    Text("Vibrant").tag(ColorIntensity.vibrant)
                }
            }

            Section("Audio Preferences") {
                Picker("Sound Level", selection: $viewModel.preferredSoundLevel) {
                    Text("Silent").tag(SoundLevel.silent)
                    Text("Quiet").tag(SoundLevel.quiet)
                    Text("Normal").tag(SoundLevel.normal)
                    Text("Loud").tag(SoundLevel.loud)
                }

                Toggle("Haptic Feedback", isOn: $viewModel.hapticFeedbackEnabled)
            }

            Section("Motion Preferences") {
                Toggle("Reduce Animations", isOn: $viewModel.reduceAnimations)
                Toggle("Parallax Effects", isOn: $viewModel.parallaxEffects)
            }
        }
        .navigationTitle("Sensory Preferences")
        .navigationBarTitleDisplayMode(.inline)
    }
}

@available(iOS 18.0, *)
struct ExecutiveFunctionView: View {
    @ObservedObject var viewModel: SettingsViewModel

    var body: some View {
        Form {
            Section("Task Management") {
                Picker("Default Task Breakdown", selection: $viewModel.defaultTaskBreakdown) {
                    Text("Simple").tag(TaskBreakdownLevel.simple)
                    Text("Standard").tag(TaskBreakdownLevel.standard)
                    Text("Detailed").tag(TaskBreakdownLevel.detailed)
                }

                Toggle("Auto-Generate Steps", isOn: $viewModel.autoGenerateSteps)
                Toggle("Time Estimates", isOn: $viewModel.showTimeEstimates)
            }

            Section("Focus Support") {
                Picker("Focus Duration", selection: $viewModel.defaultFocusDuration) {
                    Text("15 minutes").tag(15)
                    Text("25 minutes").tag(25)
                    Text("45 minutes").tag(45)
                    Text("60 minutes").tag(60)
                }

                Toggle("Focus Reminders", isOn: $viewModel.focusReminders)
                Toggle("Distraction Blocking", isOn: $viewModel.distractionBlocking)
            }
        }
        .navigationTitle("Executive Function")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Placeholder Views

@available(iOS 18.0, *)
struct DataExportView: View {
    var body: some View {
        Text("Data Export")
            .navigationTitle("Data Export")
    }
}

@available(iOS 18.0, *)
struct PrivacyPolicyView: View {
    var body: some View {
        Text("Privacy Policy")
            .navigationTitle("Privacy Policy")
    }
}

@available(iOS 18.0, *)
struct TermsOfServiceView: View {
    var body: some View {
        Text("Terms of Service")
            .navigationTitle("Terms of Service")
    }
}

@available(iOS 18.0, *)
struct SupportView: View {
    var body: some View {
        Text("Support")
            .navigationTitle("Support")
    }
}

// MARK: - Preview

@available(iOS 18.0, *)
#Preview {
    SettingsView()
}
