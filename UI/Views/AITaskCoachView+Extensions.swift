import SwiftUI

// MARK: - AITaskCoachView Supporting Components

@available(iOS 18.0, *)
extension AITaskCoachView {
    // Accessibility Support Methods
    var accessibilityValue: String {
        var components: [String] = []
        
        components.append("Current cognitive state: \(viewModel.currentCognitiveState.description)")
        
        if !viewModel.aiSuggestions.isEmpty {
            components.append("\(viewModel.aiSuggestions.count) AI suggestions available")
        }
        
        if !viewModel.generatedTasks.isEmpty {
            components.append("\(viewModel.generatedTasks.count) generated tasks")
        }
        
        if viewModel.isGenerating {
            components.append("Currently generating tasks")
        }
        
        return components.isEmpty ? "Ready to assist" : components.joined(separator: ", ")
    }
    
    func refreshSuggestionsWithAnnouncement() async {
        UIAccessibility.post(
            notification: .announcement,
            argument: "Refreshing AI suggestions"
        )
        await viewModel.loadAISuggestions()
        UIAccessibility.post(
            notification: .announcement,
            argument: "AI suggestions updated with \(viewModel.aiSuggestions.count) new suggestions"
        )
    }
    
    func clearAllTasksWithAnnouncement() {
        let taskCount = viewModel.generatedTasks.count
        // Assuming clearAllTasks method exists
        // viewModel.clearAllTasks()
        UIAccessibility.post(
            notification: .announcement,
            argument: "\(taskCount) tasks cleared"
        )
    }
    
    func announceCoachingStatus() {
        let status = """
        AI Task Coach Status: 
        Cognitive state: \(viewModel.currentCognitiveState.description). 
        Available suggestions: \(viewModel.aiSuggestions.count). 
        Generated tasks: \(viewModel.generatedTasks.count). 
        Coach is \(viewModel.isGenerating ? "currently generating tasks" : "ready to assist").
        """
        UIAccessibility.post(notification: .announcement, argument: status)
    }
    
    func generateTasksWithAnnouncement() async {
        UIAccessibility.post(
            notification: .announcement,
            argument: "Generating \(viewModel.selectedPriority.rawValue) priority tasks from your input"
        )
        await viewModel.generateTasks()
    }
    
    func saveAllTasksWithAnnouncement() async {
        let taskCount = viewModel.generatedTasks.count
        UIAccessibility.post(
            notification: .announcement,
            argument: "Saving \(taskCount) tasks to your task list"
        )
        await viewModel.saveAllTasks()
        UIAccessibility.post(
            notification: .announcement,
            argument: "\(taskCount) tasks successfully saved"
        )
    }
    
    func announceTaskPreview() {
        let taskTitles = viewModel.generatedTasks.prefix(3).map { $0.title }.joined(separator: ", ")
        let remaining = max(0, viewModel.generatedTasks.count - 3)
        let preview = remaining > 0 ? 
            "\(taskTitles), and \(remaining) more tasks" : 
            taskTitles
        
        UIAccessibility.post(
            notification: .announcement,
            argument: "Tasks to save: \(preview)"
        )
    }
}

// MARK: - Supporting View Components

@available(iOS 18.0, *)
struct CognitiveStateCard: View {
    let state: CognitiveState

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Current State")
                    .font(.caption)
                    .foregroundColor(.neuroNexaTextSecondary)

                Text(state.description)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
            }

            Spacer()

            Circle()
                .fill(state.color)
                .frame(width: 12, height: 12)
        }
        .padding()
        .background(state.color.opacity(0.1))
        .cornerRadius(12)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Current cognitive state: \(state.description)")
        .accessibilityValue("Status indicator")
    }
}

@available(iOS 18.0, *)
struct AISuggestionCard: View {
    let suggestion: AISuggestion
    let onApply: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: suggestion.icon)
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("\(suggestion.title) suggestion icon")

                VStack(alignment: .leading, spacing: 4) {
                    Text(suggestion.title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)

                    Text(suggestion.description)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }

                Spacer()

                Button("Apply") {
                    onApply()
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.neuroNexaPrimary)
                .foregroundColor(.white)
                .cornerRadius(8)
                .accessibilityLabel("Apply \(suggestion.title)")
                .accessibilityHint("Apply this AI suggestion to your workflow")
                .sensoryFeedback(.impact(intensity: 0.3), trigger: onApply)
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
        .sensoryAdaptive()
    }
}

@available(iOS 18.0, *)
struct GeneratedTaskCard: View {
    let task: AITask
    let onUpdate: (AITask) -> Void

    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(task.title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)

                    if !task.description.isEmpty {
                        Text(task.description)
                            .font(.caption)
                            .foregroundColor(.neuroNexaTextSecondary)
                            .lineLimit(isExpanded ? nil : 2)
                    }
                }

                Spacer()

                VStack(spacing: 8) {
                    CognitiveLoadBadge(level: task.cognitiveLoad)

                    Button {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isExpanded.toggle()
                        }
                    } label: {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .foregroundColor(.neuroNexaSecondary)
                            .accessibilityLabel(isExpanded ? "Collapse task details" : "Expand task details")
                            .accessibilityHint("Toggle visibility of task steps and details")
                    }
                    .sensoryFeedback(.selection, trigger: isExpanded)
                }
            }

            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    if !task.aiGeneratedSteps.isEmpty {
                        Text("Steps:")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.neuroNexaText)

                        ForEach(Array(task.aiGeneratedSteps.enumerated()), id: \.offset) { index, step in
                            HStack(alignment: .top) {
                                Text("\(index + 1).")
                                    .font(.caption)
                                    .foregroundColor(.neuroNexaTextSecondary)

                                Text(step)
                                    .font(.caption)
                                    .foregroundColor(.neuroNexaTextSecondary)
                            }
                        }
                    }

                    HStack {
                        Text("Estimated: \(Int(task.estimatedDuration / 60)) min")
                            .font(.caption)
                            .foregroundColor(.neuroNexaTextSecondary)

                        Spacer()

                        Button("Save Task") {
                            // Save individual task
                        }
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.neuroNexaSecondary)
                        .foregroundColor(.white)
                        .cornerRadius(6)
                        .accessibilityLabel("Save \(task.title)")
                        .accessibilityHint("Save this individual task to your task list")
                        .sensoryFeedback(.success, trigger: task.isCompleted)
                    }
                }
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Generated task: \(task.title)")
        .accessibilityValue("Estimated duration: \(Int(task.estimatedDuration / 60)) minutes")
        .animation(.easeInOut(duration: 0.3), value: isExpanded)
    }
}

@available(iOS 18.0, *)
struct CognitiveInsightsCard: View {
    let insights: CognitiveInsights

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Personalized Insights")
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.neuroNexaText)

            ForEach(insights.recommendations, id: \.self) { recommendation in
                HStack(alignment: .top) {
                    Image(systemName: "lightbulb")
                        .foregroundColor(.neuroNexaAccent)
                        .frame(width: 16)
                        .accessibilityLabel("Insight recommendation - helpful tip")

                    Text(recommendation)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
            }

            if let breakSuggestion = insights.breakSuggestion {
                Divider()

                HStack {
                    Image(systemName: "pause.circle")
                        .foregroundColor(.orange)
                        .accessibilityLabel("Break suggestion - time to rest and recharge")

                    Text(breakSuggestion)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Cognitive insights and recommendations")
        .accessibilityValue("\(insights.recommendations.count) personalized recommendations available")
    }
}
