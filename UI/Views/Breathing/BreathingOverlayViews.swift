import HealthKit
import SwiftUI

/// Overlay and toolbar components for BreathingView
/// Extracted to reduce file length and improve modularity
@available(iOS 18.0, *)
struct AnxietyDetectionOverlay: View {
    @Binding var showingAnxietyDetection: Bool
    let detection: AnxietyDetection?
    let onExerciseSelected: (BreathingExercise) -> Void

    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    showingAnxietyDetection = false
                }
                .accessibilityAddTraits(.isButton)
                .accessibilityLabel("Dismiss anxiety detection overlay")

            if let detection = detection {
                AnxietyDetectionSheet(
                    detection: detection,
                    onDismiss: {
                        showingAnxietyDetection = false
                    },
                    onExerciseSelected: onExerciseSelected
                )
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: showingAnxietyDetection)
    }
}

@available(iOS 18.0, *)
struct BreathingToolbarButtons: View {
    @Binding var showingSettings: Bool
    @Binding var showingHistory: Bool
    let onEmergencyTap: () -> Void

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences

    private var adaptiveButtonSize: CGFloat {
        switch cognitiveLoad {
        case .low: return 32
        case .medium: return 36
        case .high: return 44
        }
    }

    var body: some View {
        HStack {
            // Settings button
            Button {
                showingSettings = true
            } label: {
                Image(systemName: "gearshape.fill")
                    .font(.system(size: adaptiveButtonSize * 0.6))
                    .foregroundColor(.secondary)
            }
            .accessibilityLabel("Settings")
            .accessibilityHint("Open breathing exercise settings")

            Spacer()

            // Emergency button
            Button {
                onEmergencyTap()
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: adaptiveButtonSize * 0.5))
                    Text("Emergency")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.red)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.red.opacity(0.1))
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
            }
            .accessibilityLabel("Emergency assistance")
            .accessibilityHint("Get immediate help for severe anxiety or panic")

            Spacer()

            // History button
            Button {
                showingHistory = true
            } label: {
                Image(systemName: "clock.fill")
                    .font(.system(size: adaptiveButtonSize * 0.6))
                    .foregroundColor(.secondary)
            }
            .accessibilityLabel("Session history")
            .accessibilityHint("View your breathing exercise history")
        }
        .padding(.horizontal)
    }
}

@available(iOS 18.0, *)
struct AnxietyStatusCard: View {
    let detection: AnxietyDetection
    let onTap: () -> Void

    @Environment(\.sensoryPreferences) private var sensoryPreferences

    private var statusColor: Color {
        switch detection.level {
        case .low: return .green
        case .moderate: return .orange
        case .high: return .red
        }
    }

    private var statusText: String {
        switch detection.level {
        case .low: return "Mild anxiety detected"
        case .moderate: return "Moderate anxiety detected"
        case .high: return "High anxiety detected"
        }
    }

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Status indicator
                Circle()
                    .fill(statusColor)
                    .frame(width: 12, height: 12)
                    .accessibilityHidden(true)

                VStack(alignment: .leading, spacing: 2) {
                    Text(statusText)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text("Tap for recommendations")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .accessibilityHidden(true)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(statusColor.opacity(0.1))
                    .stroke(statusColor.opacity(0.3), lineWidth: 1)
            )
        }
        .accessibilityLabel("\(statusText). Tap for breathing exercise recommendations")
        .accessibilityAddTraits(.isButton)
    }
}

@available(iOS 18.0, *)
struct QuickStartSection: View {
    let exercises: [BreathingExercise]
    let onExerciseSelected: (BreathingExercise) -> Void

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences

    private var adaptiveSubheadlineFont: Font {
        switch cognitiveLoad {
        case .low: return .subheadline
        case .medium: return .headline
        case .high: return .title3
        }
    }

    private var adaptiveSpacing: CGFloat {
        switch cognitiveLoad {
        case .low: return 8
        case .medium: return 12
        case .high: return 16
        }
    }

    var body: some View {
        VStack(spacing: adaptiveSpacing) {
            Text("Quick Start")
                .font(adaptiveSubheadlineFont)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: adaptiveSpacing) {
                ForEach(exercises.prefix(4), id: \.id) { exercise in
                    BreathingExerciseCard(
                        exercise: exercise,
                        isCompact: true,
                        onTap: {
                            onExerciseSelected(exercise)
                        }
                    )
                }
            }
        }
    }
}

@available(iOS 18.0, *)
struct ExerciseLibrarySection: View {
    let exercises: [BreathingExercise]
    let onExerciseSelected: (BreathingExercise) -> Void
    @Binding var showingLibrary: Bool

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad

    private var adaptiveSubheadlineFont: Font {
        switch cognitiveLoad {
        case .low: return .subheadline
        case .medium: return .headline
        case .high: return .title3
        }
    }

    private var adaptiveSpacing: CGFloat {
        switch cognitiveLoad {
        case .low: return 8
        case .medium: return 12
        case .high: return 16
        }
    }

    var body: some View {
        VStack(spacing: adaptiveSpacing) {
            HStack {
                Text("Exercise Library")
                    .font(adaptiveSubheadlineFont)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    showingLibrary = true
                }
                .font(.caption)
                .foregroundColor(.accentColor)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: adaptiveSpacing) {
                    ForEach(exercises.prefix(6), id: \.id) { exercise in
                        BreathingExerciseCard(
                            exercise: exercise,
                            isCompact: true,
                            onTap: {
                                onExerciseSelected(exercise)
                            }
                        )
                        .frame(width: 140)
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}
