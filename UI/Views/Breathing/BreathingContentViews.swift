import HealthKit
import SwiftUI

/// Main content views for BreathingView
/// Extracted to reduce file length and improve modularity
@available(iOS 18.0, *)
extension BreathingView {
    // MARK: - Main Content Views

    var adaptiveBackground: some View {
        Group {
            switch sensoryPreferences.colorContrast {
            case .low:
                LinearGradient(
                    colors: [.blue.opacity(0.1), .purple.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            case .medium:
                LinearGradient(
                    colors: [.blue.opacity(0.2), .purple.opacity(0.2)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            case .high:
                Color(.systemBackground)
            }
        }
        .ignoresSafeArea()
    }

    var mainContent: some View {
        ScrollView {
            VStack(spacing: adaptiveSpacing) {
                if viewModel.isSessionActive {
                    activeSessionView
                } else {
                    inactiveSessionView
                }
            }
            .padding(adaptivePadding)
        }
    }

    var activeSessionView: some View {
        VStack(spacing: adaptiveSpacing) {
            // Session progress
            sessionProgressView

            // Breathing circle
            breathingCircleView

            // Phase guidance
            phaseGuidanceView

            // Biometric feedback
            biometricFeedbackView

            // Session controls
            sessionControlsView
        }
    }

    var inactiveSessionView: some View {
        VStack(spacing: adaptiveSpacing) {
            // Welcome header
            welcomeHeaderView

            // Quick start section
            quickStartSection

            // Anxiety status
            anxietyStatusView

            // Exercise library
            exerciseLibrarySection
        }
    }

    var sessionProgressView: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Session Progress")
                    .font(adaptiveSubheadlineFont)
                    .fontWeight(.medium)

                Spacer()

                Text("\(viewModel.currentCycle)/\(viewModel.totalCycles)")
                    .font(adaptiveBodyFont)
                    .foregroundColor(.secondary)
            }

            ProgressView(value: viewModel.sessionProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: calmingColor))
                .scaleEffect(y: 2)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }

    var breathingCircleView: some View {
        ZStack {
            // Outer ring
            Circle()
                .stroke(calmingColor.opacity(0.3), lineWidth: 4)
                .frame(width: breathingCircleSize, height: breathingCircleSize)

            // Animated breathing circle
            Circle()
                .fill(
                    RadialGradient(
                        colors: [calmingColor.opacity(breathingOpacity), calmingColor.opacity(0.1)],
                        center: .center,
                        startRadius: 0,
                        endRadius: breathingCircleSize / 2
                    )
                )
                .frame(width: breathingCircleSize, height: breathingCircleSize)
                .scaleEffect(breathingCircleScale)
                .animation(.easeInOut(duration: 2), value: breathingCircleScale)

            // Phase text
            Text(phaseText)
                .font(adaptiveTitleFont)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .opacity(guidanceTextOpacity)
        }
        .onReceive(viewModel.$currentPhase) { phase in
            animateBreathingPhase(phase)
        }
    }

    var phaseGuidanceView: some View {
        VStack(spacing: 8) {
            Text(phaseInstructionText)
                .font(adaptiveHeadlineFont)
                .fontWeight(.medium)
                .multilineTextAlignment(.center)
                .foregroundColor(.primary)

            if let pattern = viewModel.currentExercise?.pattern {
                Text(patternDescription(pattern))
                    .font(adaptiveBodyFont)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }

    var biometricFeedbackView: some View {
        HStack(spacing: 16) {
            // Heart rate
            VStack(spacing: 4) {
                Image(systemName: "heart.fill")
                    .foregroundColor(heartRateColor)
                    .font(.title2)

                Text("\(Int(viewModel.currentHeartRate))")
                    .font(adaptiveHeadlineFont)
                    .fontWeight(.semibold)

                Text("BPM")
                    .font(adaptiveBodyFont)
                    .foregroundColor(.secondary)
            }

            Divider()
                .frame(height: 40)

            // HRV
            VStack(spacing: 4) {
                Image(systemName: "waveform.path.ecg")
                    .foregroundColor(hrvColor)
                    .font(.title2)

                Text("\(Int(viewModel.currentHRV))")
                    .font(adaptiveHeadlineFont)
                    .fontWeight(.semibold)

                Text("HRV")
                    .font(adaptiveBodyFont)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }

    var sessionControlsView: some View {
        HStack(spacing: 16) {
            // Pause/Resume button
            Button {
                if viewModel.isSessionPaused {
                    viewModel.resumeSession()
                } else {
                    viewModel.pauseSession()
                }
            } label: {
                Image(systemName: viewModel.isSessionPaused ? "play.fill" : "pause.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Circle().fill(calmingColor))
            }
            .accessibilityLabel(viewModel.isSessionPaused ? "Resume session" : "Pause session")

            // Stop button
            Button {
                viewModel.stopSession()
            } label: {
                Image(systemName: "stop.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Circle().fill(.red))
            }
            .accessibilityLabel("Stop session")
        }
    }

    var welcomeHeaderView: some View {
        VStack(spacing: 12) {
            Text("Breathing & Calming")
                .font(adaptiveTitleFont)
                .fontWeight(.bold)
                .frame(maxWidth: .infinity, alignment: .leading)

            Text("Take a moment to center yourself with guided breathing exercises designed for your needs.")
                .font(adaptiveBodyFont)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(adaptivePadding)
    }

    var exerciseLibrarySection: some View {
        ExerciseLibrarySection(
            exercises: viewModel.availableExercises,
            onExerciseSelected: { exercise in
                selectedExercise = exercise
                Task {
                    await viewModel.startBreathingSession(exercise)
                }
            }
        )
    }
}
