import SwiftUI

// MARK: - Supporting Views for Breathing Interface

/// Biometric indicator component for displaying heart rate and HRV data
struct BiometricIndicator: View {
    let title: String
    let value: String
    let unit: String
    let color: Color
    let trend: BiometricTrend

    var body: some View {
        VStack(spacing: 4) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Image(systemName: trendIcon)
                    .font(.caption2)
                    .foregroundColor(color)
                    .accessibilityLabel("\(title) trend: \(trend.description)")
            }

            HStack(alignment: .firstTextBaseline, spacing: 2) {
                Text(value)
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(color)

                Text(unit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title): \(value) \(unit), \(trend.description)")
    }

    private var trendIcon: String {
        switch trend {
        case .increasing: "arrow.up"
        case .stable: "minus"
        case .decreasing: "arrow.down"
        }
    }
}

/// Biometric trend enumeration for heart rate and HRV indicators
enum BiometricTrend {
    case increasing
    case stable
    case decreasing

    var description: String {
        switch self {
        case .increasing: "increasing"
        case .stable: "stable"
        case .decreasing: "decreasing"
        }
    }
}

/// Breathing exercise card component for exercise selection
struct BreathingExerciseCard: View {
    let exercise: BreathingExercise
    let onTap: () -> Void

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(exercise.name)
                        .font(adaptiveFont)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Spacer()

                    Image(systemName: exercise.iconName)
                        .font(.title3)
                        .foregroundColor(.accentColor)
                        .accessibilityLabel("Exercise icon")
                }

                Text(exercise.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)

                HStack {
                    Label("\(exercise.duration) min", systemImage: "clock")
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text(exercise.difficulty.rawValue.capitalized)
                        .font(.caption2)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(difficultyColor.opacity(0.2))
                        .foregroundColor(difficultyColor)
                        .cornerRadius(4)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(adaptiveCornerRadius)
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Breathing exercise: \(exercise.name)")
        .accessibilityHint("Tap to start this exercise")
    }

    private var adaptiveFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .subheadline,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveCornerRadius: CGFloat {
        NeuroNexaDesignSystem.CornerRadius.adaptive(for: cognitiveLoad)
    }

    private var difficultyColor: Color {
        switch exercise.difficulty {
        case .beginner: .green
        case .intermediate: .orange
        case .advanced: .red
        }
    }
}

/// Breathing phase indicator showing current breathing instruction
struct BreathingPhaseIndicator: View {
    let phase: BreathingPhase
    let instruction: String

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad

    var body: some View {
        VStack(spacing: 8) {
            Text(phaseTitle)
                .font(adaptiveTitleFont)
                .fontWeight(.semibold)
                .foregroundColor(phaseColor)

            Text(instruction)
                .font(adaptiveBodyFont)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Breathing phase: \(phaseTitle). \(instruction)")
    }

    private var phaseTitle: String {
        switch phase {
        case .preparation: "Prepare"
        case .inhale: "Breathe In"
        case .hold: "Hold"
        case .exhale: "Breathe Out"
        case .rest: "Rest"
        case .complete: "Complete"
        }
    }

    private var phaseColor: Color {
        switch phase {
        case .preparation: .blue
        case .inhale: .green
        case .hold: .orange
        case .exhale: .purple
        case .rest: .gray
        case .complete: .blue
        }
    }

    private var adaptiveTitleFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .title2,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveBodyFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .body,
            cognitiveLoad: cognitiveLoad
        )
    }
}

/// Session progress indicator with time remaining and completion percentage
struct BreathingSessionProgress: View {
    let progress: Double
    let timeRemaining: TimeInterval
    let totalTime: TimeInterval

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad

    var body: some View {
        VStack(spacing: 8) {
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .accentColor))
                .scaleEffect(y: 2.0)

            HStack {
                Text("Time Remaining")
                    .font(adaptiveCaptionFont)
                    .foregroundColor(.secondary)

                Spacer()

                Text(formatTime(timeRemaining))
                    .font(adaptiveBodyFont)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Session progress: \(Int(progress * 100))% complete. \(formatTime(timeRemaining)) remaining")
    }

    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    private var adaptiveCaptionFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .caption,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveBodyFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .body,
            cognitiveLoad: cognitiveLoad
        )
    }
}

// MARK: - Preview Providers

#if DEBUG
struct BreathingSupportingViews_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            BiometricIndicator(
                title: "Heart Rate",
                value: "72",
                unit: "BPM",
                color: .red,
                trend: .stable
            )

            BreathingPhaseIndicator(
                phase: .inhale,
                instruction: "Breathe in slowly through your nose"
            )

            BreathingSessionProgress(
                progress: 0.65,
                timeRemaining: 180,
                totalTime: 300
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
#endif
