import SwiftUI

// MARK: - Anxiety Detection Sheet

/// Sheet view for displaying anxiety detection results and recommended exercises
struct AnxietyDetectionSheet: View {
    let detection: AnxietyDetection
    let onDismiss: () -> Void
    let onExerciseSelected: (BreathingExercise) -> Void

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                // Detection summary
                detectionSummaryView

                // Recommended exercises
                recommendedExercisesView

                Spacer()

                // Action buttons
                actionButtonsView
            }
            .padding()
            .navigationTitle("Anxiety Detected")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Dismiss", action: onDismiss)
                }
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
    }

    private var detectionSummaryView: some View {
        VStack(spacing: 12) {
            // Anxiety level indicator
            HStack {
                Image(systemName: anxietyLevelIcon)
                    .font(.title2)
                    .foregroundColor(anxietyLevelColor)
                    .accessibilityLabel("Anxiety level indicator")

                VStack(alignment: .leading, spacing: 4) {
                    Text("Anxiety Level: \(detection.level.rawValue.capitalized)")
                        .font(adaptiveHeadlineFont)
                        .fontWeight(.semibold)

                    Text("Confidence: \(Int(detection.confidence * 100))%")
                        .font(adaptiveCaptionFont)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // Detection details
            if !detection.triggers.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Detected Triggers:")
                        .font(adaptiveSubheadlineFont)
                        .fontWeight(.medium)

                    ForEach(detection.triggers, id: \.self) { trigger in
                        HStack {
                            Image(systemName: "circle.fill")
                                .font(.caption2)
                                .foregroundColor(.orange)
                                .accessibilityLabel("Trigger indicator")

                            Text(trigger.description)
                                .font(adaptiveBodyFont)
                                .foregroundColor(.primary)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(adaptiveCornerRadius)
            }
        }
    }

    private var recommendedExercisesView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recommended Exercises")
                .font(adaptiveHeadlineFont)
                .fontWeight(.semibold)

            LazyVStack(spacing: 8) {
                ForEach(detection.recommendedExercises, id: \.id) { exercise in
                    RecommendedExerciseRow(
                        exercise: exercise,
                        onTap: { onExerciseSelected(exercise) }
                    )
                }
            }
        }
    }

    private var actionButtonsView: some View {
        VStack(spacing: 12) {
            // Start recommended exercise button
            if let topRecommendation = detection.recommendedExercises.first {
                Button {
                    onExerciseSelected(topRecommendation)
                } label: {
                    HStack {
                        Image(systemName: "play.fill")
                        Text("Start \(topRecommendation.name)")
                    }
                    .font(adaptiveBodyFont)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.accentColor)
                    .cornerRadius(adaptiveCornerRadius)
                }
                .accessibilityLabel("Start recommended breathing exercise: \(topRecommendation.name)")
            }

            // Dismiss button
            Button("Not Now", action: onDismiss)
                .font(adaptiveBodyFont)
                .foregroundColor(.secondary)
        }
    }

    // MARK: - Computed Properties

    private var anxietyLevelIcon: String {
        switch detection.level {
        case .low: "exclamationmark.circle"
        case .moderate: "exclamationmark.triangle"
        case .high: "exclamationmark.triangle.fill"
        case .severe: "exclamationmark.octagon.fill"
        }
    }

    private var anxietyLevelColor: Color {
        switch detection.level {
        case .low: .yellow
        case .moderate: .orange
        case .high: .red
        case .severe: .red
        }
    }

    private var adaptiveHeadlineFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .headline,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveSubheadlineFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .subheadline,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveBodyFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .body,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveCaptionFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .caption,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveCornerRadius: CGFloat {
        NeuroNexaDesignSystem.CornerRadius.adaptive(for: cognitiveLoad)
    }
}

// MARK: - Recommended Exercise Row

struct RecommendedExerciseRow: View {
    let exercise: BreathingExercise
    let onTap: () -> Void

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                Image(systemName: exercise.iconName)
                    .font(.title3)
                    .foregroundColor(.accentColor)
                    .frame(width: 24, height: 24)
                    .accessibilityLabel("Exercise icon")

                VStack(alignment: .leading, spacing: 2) {
                    Text(exercise.name)
                        .font(adaptiveSubheadlineFont)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text("\(exercise.duration) min • \(exercise.difficulty.rawValue.capitalized)")
                        .font(adaptiveCaptionFont)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .accessibilityLabel("Select exercise")
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(adaptiveCornerRadius)
            .shadow(color: .black.opacity(0.05), radius: 1, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Recommended exercise: \(exercise.name)")
        .accessibilityHint("Tap to start this exercise")
    }

    private var adaptiveSubheadlineFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .subheadline,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveCaptionFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            .caption,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveCornerRadius: CGFloat {
        NeuroNexaDesignSystem.CornerRadius.adaptive(for: cognitiveLoad)
    }
}

// MARK: - Preview

#if DEBUG
struct AnxietyDetectionSheet_Previews: PreviewProvider {
    static var previews: some View {
        AnxietyDetectionSheet(
            detection: AnxietyDetection(
                level: .moderate,
                confidence: 0.85,
                triggers: [.heartRateElevated, .stressDetected],
                recommendedExercises: Array(BreathingExercise.defaultExercises.prefix(3))
            ),
            onDismiss: {},
            onExerciseSelected: { _ in }
        )
    }
}
#endif
