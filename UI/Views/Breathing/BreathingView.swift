import SwiftUI

// MARK: - Breathing View

/// Main breathing and calming interface with neurodiversity-optimized design
/// Provides guided breathing exercises with sensory adaptation and biometric feedback
@available(iOS 18.0, *)
struct AdvancedBreathingView: View {
    // MARK: - Properties
    @StateObject private var viewModel: BreathingViewModel
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings

    // UI State
    @State private var showingExerciseSelection = false
    @State private var showingAnxietyDetection = false
    @State private var showingSettings = false
    @State private var showingHistory = false
    @State private var showingEmergencyHelp = false
    @State private var selectedExercise: BreathingExercise?

    // Animation state
    @State private var breathingCircleScale: CGFloat = 1.0
    @State private var breathingOpacity: Double = 0.6
    @State private var guidanceTextOpacity: Double = 1.0

    // MARK: - Initialization
    init(viewModel: BreathingViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    // MARK: - Body
    var body: some View {
        NavigationView {
            ZStack {
                // Adaptive background
                adaptiveBackground

                // Main content
                mainContent

                // Anxiety detection overlay
                if showingAnxietyDetection {
                    anxietyDetectionOverlay
                }
            }
            .navigationTitle("Breathing & Calm")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    toolbarButtons
                }
            }
            .sheet(isPresented: $showingExerciseSelection) {
                ExerciseSelectionView(
                    exercises: viewModel.personalizedExercises,
                    onSelection: { exercise in
                        selectedExercise = exercise
                        Task {
                            await viewModel.startBreathingSession(exercise)
                        }
                    }
                )
            }
            .task {
                await viewModel.loadPersonalizedExercises()
                await viewModel.startAnxietyMonitoring()
            }
            .onChange(of: viewModel.currentPhase) { _, newPhase in
                animateBreathingPhase(newPhase)
            }
            .onChange(of: viewModel.anxietyDetection) { _, detection in
                if let detection = detection, detection.overwhelmLevel != .calm {
                    showingAnxietyDetection = true
                }
            }
        }
    }

    private var anxietyDetectionOverlay: some View {
        AnxietyDetectionOverlay(
            showingAnxietyDetection: $showingAnxietyDetection,
            detection: viewModel.anxietyDetection,
            onExerciseSelected: { exercise in
                selectedExercise = exercise
                Task {
                    await viewModel.startBreathingSession(exercise)
                }
            }
        )
    }

    private var toolbarButtons: some View {
        BreathingToolbarButtons(
            showingSettings: $showingSettings,
            showingHistory: $showingHistory,
            onEmergencyTap: {
                // Handle emergency action
                showingEmergencyHelp = true
            }
        )
    }

    // MARK: - Computed Properties

    private var adaptiveBackground: some View {
        LinearGradient(
            colors: [
                Color.blue.opacity(0.1),
                Color.purple.opacity(0.05)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }

    private var mainContent: some View {
        VStack(spacing: 24) {
            // Breathing circle
            breathingCircle

            // Current phase text
            phaseGuidanceText

            // Session controls
            sessionControls

            // Quick access buttons
            quickAccessButtons
        }
        .padding()
    }

    private var breathingCircle: some View {
        Circle()
            .fill(
                RadialGradient(
                    colors: [
                        Color.blue.opacity(0.3),
                        Color.blue.opacity(0.1)
                    ],
                    center: .center,
                    startRadius: 50,
                    endRadius: 150
                )
            )
            .frame(width: 200, height: 200)
            .scaleEffect(breathingCircleScale)
            .opacity(breathingOpacity)
            .animation(.easeInOut(duration: 4), value: breathingCircleScale)
    }

    private var phaseGuidanceText: some View {
        Text(viewModel.currentPhase.rawValue.capitalized)
            .font(.title2)
            .fontWeight(.medium)
            .foregroundColor(.primary)
            .opacity(guidanceTextOpacity)
            .animation(.easeInOut(duration: 0.5), value: guidanceTextOpacity)
    }

    private var sessionControls: some View {
        HStack(spacing: 20) {
            Button {
                Task {
                    if viewModel.isSessionActive {
                        await viewModel.pauseSession()
                    } else {
                        await viewModel.resumeSession()
                    }
                }
            } label: {
                Image(systemName: viewModel.isSessionActive ? "pause.circle.fill" : "play.circle.fill")
                    .font(.title)
                    .foregroundColor(.blue)
                    .accessibilityLabel(viewModel.isSessionActive ? "Pause breathing session - take a break when needed" : "Start breathing session - begin calming exercise")
            }

            Button {
                Task {
                    await viewModel.endSession()
                }
            } label: {
                Image(systemName: "stop.circle.fill")
                    .font(.title)
                    .foregroundColor(.red)
                    .accessibilityLabel("Stop breathing session - end current exercise")
            }
        }
    }

    private var quickAccessButtons: some View {
        VStack(spacing: 12) {
            Button("Choose Exercise") {
                showingExerciseSelection = true
            }
            .buttonStyle(.borderedProminent)

            if let detection = viewModel.anxietyDetection, detection.overwhelmLevel != .calm {
                Button("Calm Now") {
                    Task {
                        // Create a quick calming exercise and start it
                        let quickExercise = BreathingExercise(
                            id: UUID(),
                            name: "Quick Calm",
                            description: "Immediate calming breathing",
                            pattern: BreathingPattern(
                                name: "Quick Calm Pattern",
                                description: "Simple calming breathing pattern",
                                inhaleTime: 4,
                                holdTime: 2,
                                exhaleTime: 6,
                                difficulty: .beginner,
                                neurodiversitySupport: [.anxiety]
                            ),
                            duration: 120,
                            difficulty: .beginner,
                            neurodiversitySupport: [.anxiety]
                        )
                        await viewModel.startBreathingSession(quickExercise)
                    }
                }
                .buttonStyle(.bordered)
                .foregroundColor(.orange)
            }
        }
    }

    // MARK: - Animation Methods

    private func animateBreathingPhase(_ phase: BreathingPhase?) {
        guard let phase = phase else { return }

        switch phase {
        case .inhale:
            breathingCircleScale = 1.3
            breathingOpacity = 0.8
        case .hold:
            breathingCircleScale = 1.3
            breathingOpacity = 0.9
        case .exhale:
            breathingCircleScale = 1.0
            breathingOpacity = 0.6
        case .preparation:
            breathingCircleScale = 1.0
            breathingOpacity = 0.5
        case .pause:
            breathingCircleScale = 1.0
            breathingOpacity = 0.4
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct ExerciseSelectionView: View {
    let exercises: [BreathingExercise]
    let onSelection: (BreathingExercise) -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List(exercises, id: \.id) { exercise in
                VStack(alignment: .leading, spacing: 8) {
                    Text(exercise.name)
                        .font(.headline)
                    Text(exercise.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("Duration: \(exercise.duration)s")
                        .font(.caption2)
                        .foregroundColor(.blue)
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    onSelection(exercise)
                    dismiss()
                }
                .accessibilityAddTraits(.isButton)
            }
            .navigationTitle("Choose Exercise")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

@available(iOS 18.0, *)
struct AnxietyDetectionOverlay: View {
    @Binding var showingAnxietyDetection: Bool
    let detection: AnxietyOverwhelmDetection?
    let onExerciseSelected: (BreathingExercise) -> Void

    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                Text("Overwhelm Detected")
                    .font(.title2)
                    .fontWeight(.semibold)

                if let detection = detection {
                    Text("Level: \(detection.overwhelmLevel.rawValue.capitalized)")
                        .font(.headline)
                        .foregroundColor(.orange)

                    Text("Confidence: \(Int(detection.confidence * 100))%")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Button("Start Calming Exercise") {
                    // Create a quick calming exercise
                    let calmingExercise = BreathingExercise(
                        id: UUID(),
                        name: "Quick Calm",
                        description: "Immediate calming breathing",
                        pattern: BreathingPattern(
                            name: "Box Breathing",
                            description: "Equal timing breathing pattern",
                            inhaleTime: 4,
                            holdTime: 2,
                            exhaleTime: 6,
                            difficulty: .beginner,
                            neurodiversitySupport: [.anxiety, .adhd]
                        ),
                        duration: 120,
                        difficulty: .beginner,
                        neurodiversitySupport: [.anxiety, .adhd]
                    )
                    onExerciseSelected(calmingExercise)
                    showingAnxietyDetection = false
                }
                .buttonStyle(.borderedProminent)

                Button("Dismiss") {
                    showingAnxietyDetection = false
                }
                .buttonStyle(.bordered)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .padding()
        }
    }
}

@available(iOS 18.0, *)
struct BreathingToolbarButtons: View {
    @Binding var showingSettings: Bool
    @Binding var showingHistory: Bool
    let onEmergencyTap: () -> Void

    var body: some View {
        HStack {
            Button { showingSettings = true } label: {
                Image(systemName: "gear")
                    .accessibilityLabel("Open breathing settings - customize your experience")
            }

            Button { showingHistory = true } label: {
                Image(systemName: "clock")
                    .accessibilityLabel("View breathing history - track your progress")
            }

            Button(action: onEmergencyTap) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                    .accessibilityLabel("Emergency support - quick access to crisis resources")
            }
        }
    }
}

// MARK: - Preview

// MARK: - Mock Services for Preview

#if DEBUG
@available(iOS 18.0, *)
class MockBreathingExerciseService: BreathingExerciseServiceProtocol {
    func getAvailableExercises() async -> [BreathingExercise] {
        [
            BreathingExercise(
                id: UUID(),
                name: "Basic Calm",
                description: "Simple breathing for beginners",
                pattern: BreathingPattern(
                    name: "4-7-8 Breathing",
                    description: "Relaxing breathing technique",
                    inhaleTime: 4,
                    holdTime: 2,
                    exhaleTime: 6,
                    difficulty: .intermediate,
                    neurodiversitySupport: [.anxiety, .autism]
                ),
                duration: 300,
                difficulty: .beginner,
                neurodiversitySupport: [.anxiety]
            )
        ]
    }

    func getPersonalizedExercises(for userProfile: UserProfile) async -> [BreathingExercise] {
        await getAvailableExercises()
    }

    func startBreathingSession(_ exercise: BreathingExercise) async throws {}
    func pauseSession() async {}
    func resumeSession() async {}
    func endBreathingSession() async throws -> BreathingSessionResult {
        BreathingSessionResult(
            sessionId: UUID(),
            duration: 300,
            completionRate: 100.0,
            averageHeartRate: 72,
            stressReduction: 0.3,
            effectivenessScore: 0.8,
            calmingEffectiveness: 0.8,
            heartRateReduction: 8.0
        )
    }
}

@available(iOS 18.0, *)
class MockUserRepository: UserRepositoryProtocol {
    func getCurrentUser() async throws -> UserProfile? {
        UserProfile(
            id: UUID(),
            name: "Test User",
            email: "<EMAIL>",
            neurodiversityTypes: [.adhd],
            accessibilitySettings: AccessibilitySettings(
                voiceOverEnabled: false,
                dynamicTypeSize: .large,
                reduceMotionEnabled: false,
                increaseContrastEnabled: false,
                boldTextEnabled: false,
                buttonShapesEnabled: false,
                reduceTransparencyEnabled: false,
                assistiveTouchEnabled: false
            )
        )
    }

    func saveUser(_ user: UserProfile) async throws {}
    func updateUser(_ user: UserProfile) async throws {}
    func deleteUser(_ user: UserProfile) async throws {}
}

@available(iOS 18.0, *)
struct AdvancedBreathingView_Previews: PreviewProvider {
    static var previews: some View {
        let mockViewModel = BreathingViewModel(
            breathingService: MockBreathingExerciseService(),
            userRepository: MockUserRepository()
        )

        AdvancedBreathingView(viewModel: mockViewModel)
            .environment(\.cognitiveLoadLevel, .medium)
            .environment(\.sensoryPreferences, .default)
            .environment(\.accessibilitySettings, .default)
    }
}
#endif
