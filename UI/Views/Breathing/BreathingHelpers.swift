import HealthKit
import SwiftUI

/// Helper functions and computed properties for BreathingView
/// Extracted to reduce file length and improve modularity
@available(iOS 18.0, *)
extension BreathingView {
    // MARK: - Animation Helpers

    func animateBreathingPhase(_ phase: BreathingPhase) {
        guard sensoryPreferences.motionSensitivity != .high else { return }

        let animationDuration = getPhaseAnimationDuration(phase)

        withAnimation(.easeInOut(duration: animationDuration)) {
            switch phase {
            case .preparation:
                breathingCircleScale = 1.0
                breathingOpacity = 0.6
            case .inhale:
                breathingCircleScale = 1.3
                breathingOpacity = 0.8
            case .hold:
                breathingCircleScale = 1.3
                breathingOpacity = 0.9
            case .exhale:
                breathingCircleScale = 0.8
                breathingOpacity = 0.4
            case .rest:
                breathingCircleScale = 1.0
                breathingOpacity = 0.6
            }
        }

        // Haptic feedback for phase transitions
        if sensoryPreferences.hapticsEnabled {
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
    }

    func getPhaseAnimationDuration(_ phase: BreathingPhase) -> Double {
        guard let pattern = viewModel.currentExercise?.pattern else { return 2.0 }

        switch phase {
        case .preparation: return 1.0
        case .inhale: return pattern.inhaleTime
        case .hold: return pattern.holdTime
        case .exhale: return pattern.exhaleTime
        case .rest: return 1.0
        }
    }

    // MARK: - Formatting Helpers

    func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    func patternDescription(_ pattern: BreathingPattern) -> String {
        if pattern.holdTime > 0 {
            return "Breathe in for \(Int(pattern.inhaleTime))s, hold for \(Int(pattern.holdTime))s, breathe out for \(Int(pattern.exhaleTime))s"
        } else {
            return "Breathe in for \(Int(pattern.inhaleTime))s, breathe out for \(Int(pattern.exhaleTime))s"
        }
    }

    // MARK: - Computed Properties

    var calmingColor: Color {
        switch sensoryPreferences.colorContrast {
        case .low: return .blue.opacity(0.7)
        case .medium: return .blue
        case .high: return .blue
        }
    }

    var heartRateColor: Color {
        guard viewModel.currentHeartRate > 0 else { return .secondary }

        switch viewModel.heartRateTrend {
        case .decreasing: return .green
        case .stable: return .blue
        case .increasing: return .orange
        }
    }

    var hrvColor: Color {
        switch viewModel.hrvTrend {
        case .increasing: return .green
        case .stable: return .blue
        case .decreasing: return .orange
        }
    }

    var breathingCircleSize: CGFloat {
        let baseSize: CGFloat = 200
        return NeuroNexaDesignSystem.Sizing.adaptiveSize(
            base: baseSize,
            cognitiveLoad: cognitiveLoad
        )
    }

    var phaseText: String {
        switch viewModel.currentPhase {
        case .preparation: return "Prepare"
        case .inhale: return "Breathe In"
        case .hold: return "Hold"
        case .exhale: return "Breathe Out"
        case .rest: return "Rest"
        }
    }

    var phaseInstructionText: String {
        switch viewModel.currentPhase {
        case .preparation:
            return "Get ready to begin your breathing exercise"
        case .inhale:
            return "Slowly breathe in through your nose"
        case .hold:
            return "Hold your breath gently"
        case .exhale:
            return "Slowly breathe out through your mouth"
        case .rest:
            return "Rest and prepare for the next cycle"
        }
    }

    var adaptiveTitleFont: Font {
        switch cognitiveLoad {
        case .low: return .title2
        case .medium: return .title
        case .high: return .largeTitle
        }
    }

    var adaptiveHeadlineFont: Font {
        switch cognitiveLoad {
        case .low: return .headline
        case .medium: return .title3
        case .high: return .title2
        }
    }

    var adaptiveSubheadlineFont: Font {
        switch cognitiveLoad {
        case .low: return .subheadline
        case .medium: return .headline
        case .high: return .title3
        }
    }

    var adaptiveBodyFont: Font {
        switch cognitiveLoad {
        case .low: return .body
        case .medium: return .callout
        case .high: return .subheadline
        }
    }

    var adaptiveSpacing: CGFloat {
        switch cognitiveLoad {
        case .low: return 12
        case .medium: return 16
        case .high: return 20
        }
    }

    var adaptivePadding: CGFloat {
        switch cognitiveLoad {
        case .low: return 16
        case .medium: return 20
        case .high: return 24
        }
    }

    var quickStartColumns: [GridItem] {
        [
            GridItem(.flexible()),
            GridItem(.flexible())
        ]
    }
}

/// Quick start card component for breathing exercises
@available(iOS 18.0, *)
struct QuickStartCard: View {
    let exercise: BreathingExercise
    let onTap: () -> Void

    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences

    private var adaptiveCornerRadius: CGFloat {
        switch cognitiveLoad {
        case .low: return 8
        case .medium: return 12
        case .high: return 16
        }
    }

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Image(systemName: exercise.iconName)
                    .font(.title2)
                    .foregroundColor(.accentColor)

                Text(exercise.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)

                Text(exercise.duration.formatted())
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                    .fill(Color(.systemBackground))
                    .shadow(
                        color: .black.opacity(0.1),
                        radius: 2,
                        x: 0,
                        y: 1
                    )
            )
        }
        .accessibilityLabel("\(exercise.name) breathing exercise, \(exercise.duration.formatted())")
        .accessibilityAddTraits(.isButton)
    }
}
