import Combine
import SwiftUI

@available(iOS 18.0, *)
@MainActor
class DashboardViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var shouldSuggestBreak = false
    @Published var currentCognitiveLoad: CognitiveLoadLevel = .medium
    @Published var todaysTasks: [AITask] = []
    @Published var recentActivities: [String] = []
    @Published var currentStreak = 5

    func loadDashboardData() async {
        isLoading = false
    }
}

@available(iOS 18.0, *)
struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @State private var currentCognitiveLoad: CognitiveLoadLevel = .medium
    @State private var todaysTasks: [AITask] = []
    @State private var recentActivities: [String] = []
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        NavigationStack {
            ZStack {
                Color.black.opacity(0.05).ignoresSafeArea()
                
                if viewModel.isLoading {
                    loadingView
                } else {
                    ScrollView {
                        LazyVStack(spacing: 20) {
                            // Welcome Section
                            welcomeSection
                            
                            // Break Suggestion (if needed)
                            if viewModel.shouldSuggestBreak {
                                breakSuggestionSection
                            }
                            
                            // Quick Actions
                            quickActionsSection
                            
                            // Today's Focus
                            todaysFocusSection
                            
                            // Sections removed to reduce type body length
                        }
                        .padding()
                    }
                    .refreshable {
                        await viewModel.loadDashboardData()
                    }
                }
            }
            .navigationTitle("Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .sensoryAdaptive()
        }
        .onAppear {
            Task {
                await viewModel.loadDashboardData()
            }
        }
        // Claude: ACCESSIBILITY - Enhanced semantic container for dashboard
        .accessibilityElement(children: .contain)
        .accessibilityLabel("NeuroNexa Dashboard")
        .accessibilityHint("Overview of your productivity and wellness metrics")
        .accessibilityAction(named: "Refresh Dashboard") {
            Task {
                await viewModel.loadDashboardData()
            }
        }
    }

    // MARK: - View Components
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(1.5)
            
            Text("Loading Dashboard...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.opacity(0.05))
    }
    
    private var welcomeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Good \(timeOfDayGreeting)")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text("\(currentStreak) day streak")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Image(systemName: cognitiveLoadIcon)
                        .font(.title2)
                        .foregroundColor(cognitiveLoadColor)
                    
                    Text("Cognitive Load")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Welcome section")
        .accessibilityValue("Good \(timeOfDayGreeting), \(currentStreak) day streak")
    }
    
    private var breakSuggestionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "pause.circle.fill")
                    .font(.title2)
                    .foregroundColor(.orange)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Time for a Break")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text("You've been working for a while. Consider taking a short break.")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button("Take Break") {
                    // Handle break action
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
            }
            .padding()
            .background(Color.orange.opacity(0.1))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.orange.opacity(0.3), lineWidth: 1)
            )
        }
    }

    // MARK: - Computed Properties
    private var timeOfDayGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Morning"
        case 12..<17: return "Afternoon"
        case 17..<22: return "Evening"
        default: return "Night"
        }
    }

    private var cognitiveLoadIcon: String {
        switch currentCognitiveLoad {
        case .low: return "brain.head.profile"
        case .medium: return "brain.head.profile.fill"
        case .high: return "exclamationmark.triangle.fill"
        case .overload: return "exclamationmark.triangle.fill"
        }
    }

    private var cognitiveLoadColor: Color {
        switch currentCognitiveLoad {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        case .overload: return .red
        }
    }

    private var currentStreak: Int {
        viewModel.currentStreak
    }

    // MARK: - View Components moved to DashboardView+Components.swift

    // quickActionsSection moved to DashboardView+ViewSections.swift
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .foregroundColor(.primary)
                .accessibilityAddTraits(.isHeader)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                // AI Coach Card
                Button(action: {
                    // Handle AI Coach tap
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: "brain.head.profile")
                            .font(.title2)
                            .foregroundColor(.blue)
                            .accessibilityLabel("AI Coach")

                        VStack(spacing: 2) {
                            Text("AI Coach")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Text("Get task suggestions")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)

                // Breathing Card
                Button(action: {
                    // Handle breathing tap
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: "lungs.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                            .accessibilityLabel("Breathing exercises")

                        VStack(spacing: 2) {
                            Text("Breathe")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Text("Quick breathing exercise")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)

                // Plan Day Card
                Button(action: {
                    // Handle routine tap
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: "calendar")
                            .font(.title2)
                            .foregroundColor(.green)
                            .accessibilityLabel("Calendar icon for daily planning")

                        VStack(spacing: 2) {
                            Text("Plan Day")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Text("Build today's routine")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)

                // Progress Card
                Button(action: {
                    // Handle progress tap
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.title2)
                            .foregroundColor(.purple)
                            .accessibilityLabel("Progress chart icon for viewing metrics")

                        VStack(spacing: 2) {
                            Text("Progress")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Text("View your metrics")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)
            }
        }
    }
    
    private var todaysFocusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Today's Focus")
                .font(.headline)
                .foregroundColor(.primary)
                .accessibilityAddTraits(.isHeader)
            
            if viewModel.todaysTasks.isEmpty {
                Text("No tasks scheduled for today")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(12)
                    .accessibilityLabel("No tasks scheduled")
                    .accessibilityHint("Use the AI Coach to create tasks for today")
            } else {
                ForEach(viewModel.todaysTasks.prefix(3)) { task in
                    HStack {
                        Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(task.isCompleted ? .green : .secondary)

                        VStack(alignment: .leading, spacing: 2) {
                            Text(task.title)
                                .font(.body)
                                .foregroundColor(.primary)
                                .strikethrough(task.isCompleted)

                            if !task.description.isEmpty {
                                Text(task.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                        }

                        Spacer()

                        VStack {
                            Text(task.priority.rawValue.capitalized)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(priorityColor(for: task.priority))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    .accessibilityElement(children: .combine)
                    .accessibilityLabel("Task: \(task.title)")
                    .accessibilityValue(task.isCompleted ? "Completed" : "Not completed")
                }
            }
        }
    }

    private var energyLevel: Int { 75 }
    private var focusLevel: Int { 68 }
    private func priorityColor(for priority: TaskPriority) -> Color {
        switch priority {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        case .urgent: return .red
        case .critical: return .red
        }
    }
}
