import Combine
import SwiftUI

@available(iOS 18.0, *)
@MainActor
class DashboardViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var shouldSuggestBreak = false
    @Published var currentCognitiveLoad: CognitiveLoadLevel = .medium
    @Published var todaysTasks: [AITask] = []
    @Published var recentActivities: [String] = []
    @Published var currentStreak = 5

    func loadDashboardData() async {
        isLoading = false
    }
}

@available(iOS 18.0, *)
struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @State private var currentCognitiveLoad: CognitiveLoadLevel = .medium
    @State private var todaysTasks: [AITask] = []
    @State private var recentActivities: [String] = []
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        NavigationStack {
            ZStack {
                Color.neuroNexaBackground.ignoresSafeArea()
                
                if viewModel.isLoading {
                    loadingView
                } else {
                    ScrollView {
                        LazyVStack(spacing: 20) {
                            // Welcome Section
                            welcomeSection
                            
                            // Break Suggestion (if needed)
                            if viewModel.shouldSuggestBreak {
                                breakSuggestionSection
                            }
                            
                            // Quick Actions
                            quickActionsSection
                            
                            // Today's Focus
                            todaysFocusSection
                            
                            // Sections removed to reduce type body length
                        }
                        .padding()
                    }
                    .refreshable {
                        await viewModel.loadDashboardData()
                    }
                }
            }
            .navigationTitle("Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .sensoryAdaptive()
        }
        .onAppear {
            Task {
                await viewModel.loadDashboardData()
            }
        }
        // Claude: ACCESSIBILITY - Enhanced semantic container for dashboard
        .accessibilityElement(children: .contain)
        .accessibilityLabel("NeuroNexa Dashboard")
        .accessibilityHint("Overview of your productivity and wellness metrics")
        .accessibilityAction(named: "Refresh Dashboard") {
            Task {
                await viewModel.loadDashboardData()
            }
        }
    }

    // MARK: - Computed Properties
    private var timeOfDayGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Morning"
        case 12..<17: return "Afternoon"
        case 17..<22: return "Evening"
        default: return "Night"
        }
    }

    private var cognitiveLoadIcon: String {
        switch currentCognitiveLoad {
        case .low: return "brain.head.profile"
        case .medium: return "brain.head.profile.fill"
        case .high: return "exclamationmark.triangle.fill"
        }
    }

    private var cognitiveLoadColor: Color {
        switch currentCognitiveLoad {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }

    private var currentStreak: Int {
        viewModel.currentStreak
    }

    // MARK: - View Components
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            Text("Loading Dashboard...")
                .font(.headline)
                .foregroundColor(.neuroNexaTextSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityLabel("Loading dashboard data")
    }

    private var breakSuggestionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "pause.circle.fill")
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("Break suggestion")
                Text("Time for a Break")
                    .font(.headline)
                    .foregroundColor(.neuroNexaText)
                Spacer()
            }

            Text("You've been focused for a while. Consider taking a short break to recharge.")
                .font(.body)
                .foregroundColor(.neuroNexaTextSecondary)

            Button("Take Break") {
                // Break action
            }
            .buttonStyle(.borderedProminent)
            .accessibilityLabel("Take a break")
            .accessibilityHint("Opens break suggestions and activities")
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
    }

    private var welcomeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Good \(timeOfDayGreeting)")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)
                    
                    Text("Ready to make today productive?")
                        .font(.body)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
                
                Spacer()
                
                // Cognitive Load indicator
                Image(systemName: cognitiveLoadIcon)
                    .font(.title2)
                    .foregroundColor(cognitiveLoadColor)
                    .accessibilityLabel("Cognitive load indicator")
                    .accessibilityValue("Current cognitive load: \(viewModel.currentCognitiveLoad.rawValue)")
                    .accessibilityHint("Shows your current cognitive state for interface adaptation")
            }
            
            // Streak indicator
            HStack {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                    .accessibilityLabel("Productivity streak")
                
                Text("\(currentStreak) day streak")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(16)
        .cognitiveLoadOptimized()
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Welcome section")
        .accessibilityValue("Good \(timeOfDayGreeting), \(currentStreak) day streak")
    }
    
    // quickActionsSection moved to DashboardView+ViewSections.swift
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
                .accessibilityAddTraits(.isHeader)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                // AI Coach Card
                Button(action: {
                    // Handle AI Coach tap
                }, label: {
                    VStack(spacing: 8) {
                        Image(systemName: "brain.head.profile")
                            .font(.title2)
                            .foregroundColor(.neuroNexaPrimary)
                            .accessibilityLabel("AI Coach")

                        VStack(spacing: 2) {
                            Text("AI Coach")
                                .font(.headline)
                                .foregroundColor(.neuroNexaText)
                            Text("Get task suggestions")
                                .font(.caption)
                                .foregroundColor(.neuroNexaTextSecondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.neuroNexaCardBackground)
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)

                // Breathing Card
                Button(action: {
                    // Handle breathing tap
                }, label: {
                    VStack(spacing: 8) {
                        Image(systemName: "lungs.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                            .accessibilityLabel("Breathing exercises")

                        VStack(spacing: 2) {
                            Text("Breathe")
                                .font(.headline)
                                .foregroundColor(.neuroNexaText)
                            Text("Quick breathing exercise")
                                .font(.caption)
                                .foregroundColor(.neuroNexaTextSecondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.neuroNexaCardBackground)
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)

                // Plan Day Card
                Button(action: {
                    // Handle routine tap
                }, label: {
                    VStack(spacing: 8) {
                        Image(systemName: "calendar")
                            .font(.title2)
                            .foregroundColor(.green)
                            .accessibilityLabel("Calendar icon for daily planning")

                        VStack(spacing: 2) {
                            Text("Plan Day")
                                .font(.headline)
                                .foregroundColor(.neuroNexaText)
                            Text("Build today's routine")
                                .font(.caption)
                                .foregroundColor(.neuroNexaTextSecondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.neuroNexaCardBackground)
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)

                // Progress Card
                Button(action: {
                    // Handle progress tap
                }, label: {
                    VStack(spacing: 8) {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.title2)
                            .foregroundColor(.purple)
                            .accessibilityLabel("Progress chart icon for viewing metrics")

                        VStack(spacing: 2) {
                            Text("Progress")
                                .font(.headline)
                                .foregroundColor(.neuroNexaText)
                            Text("View your metrics")
                                .font(.caption)
                                .foregroundColor(.neuroNexaTextSecondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.neuroNexaCardBackground)
                    .cornerRadius(12)
                }
                .buttonStyle(.plain)
            }
        }
    }
    
    private var todaysFocusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Today's Focus")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
                .accessibilityAddTraits(.isHeader)
            
            if viewModel.todaysTasks.isEmpty {
                Text("No tasks scheduled for today")
                    .font(.body)
                    .foregroundColor(.neuroNexaTextSecondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.neuroNexaSurface.opacity(0.5))
                    .cornerRadius(12)
                    .accessibilityLabel("No tasks scheduled")
                    .accessibilityHint("Use the AI Coach to create tasks for today")
            } else {
                ForEach(viewModel.todaysTasks.prefix(3)) { task in
                    HStack {
                        Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(task.isCompleted ? .green : .neuroNexaTextSecondary)

                        VStack(alignment: .leading, spacing: 2) {
                            Text(task.title)
                                .font(.body)
                                .foregroundColor(.neuroNexaText)
                                .strikethrough(task.isCompleted)

                            if let description = task.description {
                                Text(description)
                                    .font(.caption)
                                    .foregroundColor(.neuroNexaTextSecondary)
                                    .lineLimit(1)
                            }
                        }

                        Spacer()

                        if let priority = task.priority {
                            Text(priority.rawValue.capitalized)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(priorityColor(for: priority))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(Color.neuroNexaCardBackground)
                    .cornerRadius(12)
                    .accessibilityElement(children: .combine)
                    .accessibilityLabel("Task: \(task.title)")
                    .accessibilityValue(task.isCompleted ? "Completed" : "Not completed")
                }
            }
        }
    }
    
    private var cognitiveStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cognitive Status")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
                .accessibilityAddTraits(.isHeader)
            
            HStack(spacing: 16) {
                // Energy Card
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "bolt.fill")
                            .foregroundColor(.yellow)
                            .accessibilityLabel("Energy level indicator")
                        Text("Energy")
                            .font(.caption)
                            .foregroundColor(.neuroNexaTextSecondary)
                        Spacer()
                    }

                    Text("\(energyLevel)%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.neuroNexaText)
                }
                .padding()
                .background(Color.neuroNexaCardBackground)
                .cornerRadius(12)
                .frame(maxWidth: .infinity)

                // Focus Card - removed to reduce type body length

                // Stress Card
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "heart.fill")
                            .foregroundColor(.red)
                            .accessibilityLabel("Stress level indicator")
                        Text("Stress")
                            .font(.caption)
                            .foregroundColor(.neuroNexaTextSecondary)
                        Spacer()
                    }

                    Text("\(stressLevel)%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.neuroNexaText)
                }
                .padding()
                .background(Color.neuroNexaCardBackground)
                .cornerRadius(12)
                .frame(maxWidth: .infinity)
            }
        }
    }
    
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Activity")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
                .accessibilityAddTraits(.isHeader)
            
            if viewModel.recentActivities.isEmpty {
                Text("No recent activity")
                    .font(.body)
                    .foregroundColor(.neuroNexaTextSecondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.neuroNexaSurface.opacity(0.5))
                    .cornerRadius(12)
            } else {
                ForEach(viewModel.recentActivities.prefix(3), id: \.self) { activity in
                    HStack {
                        Image(systemName: "clock.fill")
                            .foregroundColor(.neuroNexaPrimary)
                            .accessibilityLabel("Recent activity time indicator")

                        Text(activity)
                            .font(.body)
                            .foregroundColor(.neuroNexaText)

                        Spacer()
                    }
                    .padding()
                    .background(Color.neuroNexaCardBackground)
                    .cornerRadius(12)
                }
            }
        }
    }

    private var energyLevel: Int { 75 }
    private var focusLevel: Int { 68 }
    private func priorityColor(for priority: TaskPriority) -> Color {
        switch priority {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}
