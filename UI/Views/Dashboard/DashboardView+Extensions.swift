import SwiftUI

// MARK: - DashboardView Supporting Components

@available(iOS 18.0, *)
extension DashboardView {
    var timeOfDayGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Morning"
        case 12..<17: return "Afternoon"
        case 17..<22: return "Evening"
        default: return "Night"
        }
    }
    
    var currentStreak: Int {
        // Mock data - replace with actual streak calculation
        7
    }
    
    var cognitiveLoadIcon: String {
        switch viewModel.currentCognitiveLoad {
        case .low: return "brain"
        case .medium: return "brain.filled.head.profile"
        case .high: return "brain.head.profile"
        case .overload: return "exclamationmark.triangle.fill"
        }
    }
    
    var cognitiveLoadColor: Color {
        switch viewModel.currentCognitiveLoad {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .overload: return .red
        }
    }
    
    var energyLevel: Double {
        // Mock data - replace with actual energy calculation
        switch viewModel.currentCognitiveLoad {
        case .low: return 0.8
        case .medium: return 0.6
        case .high: return 0.4
        case .overload: return 0.2
        }
    }
    
    var focusLevel: Double {
        // Mock data - replace with actual focus calculation
        switch viewModel.currentCognitiveLoad {
        case .low: return 0.9
        case .medium: return 0.7
        case .high: return 0.5
        case .overload: return 0.3
        }
    }
    
    var stressLevel: Double {
        // Mock data - replace with actual stress calculation
        switch viewModel.currentCognitiveLoad {
        case .low: return 0.2
        case .medium: return 0.4
        case .high: return 0.7
        case .overload: return 0.9
        }
    }
    
    var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .accessibilityLabel("Loading dashboard data")
                .accessibilityHint("Please wait while we load your personalized dashboard")
            
            Text("Loading your dashboard...")
                .font(.body)
                .foregroundColor(.neuroNexaTextSecondary)
                .accessibilityAddTraits(.updatesFrequently)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Loading dashboard")
        .accessibilityValue("Dashboard content is being loaded")
    }
    
    var breakSuggestionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "pause.circle.fill")
                    .font(.title2)
                    .foregroundColor(.orange)
                    .accessibilityLabel("Break suggestion")
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Take a Break")
                        .font(.headline)
                        .foregroundColor(.neuroNexaText)
                    
                    Text("Your cognitive load is high. A short break can help you reset.")
                        .font(.body)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
                
                Spacer()
            }
            
            Button("Start Breathing Exercise") {
                // Navigate to breathing exercise
                UIAccessibility.post(
                    notification: .announcement,
                    argument: "Starting breathing exercise to help reduce cognitive load"
                )
            }
            .font(.body)
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(Color.orange)
            .cornerRadius(8)
            .accessibilityLabel("Start breathing exercise")
            .accessibilityHint("Begin a guided breathing session to reduce stress")
            .accessibilityIdentifier("StartBreathingExerciseButton")
            .accessibilityAddTraits(.startsMediaSession)
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(16)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Break suggestion")
        .accessibilityValue("High cognitive load detected, breathing exercise recommended")
    }
}

// MARK: - Dashboard Supporting View Components

@available(iOS 18.0, *)
struct QuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .accessibilityLabel("\(title) quick action")
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
                
                Spacer()
            }
            .padding()
            .frame(maxWidth: .infinity, minHeight: 80)
            .background(Color.neuroNexaSurface)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title)")
        .accessibilityHint("\(subtitle)")
        .accessibilityAddTraits(.isButton)
        .accessibilityIdentifier("QuickAction_\(title.replacingOccurrences(of: " ", with: "_"))")
    }
}

@available(iOS 18.0, *)
struct TodaysTaskCard: View {
    let task: DashboardTask
    
    var body: some View {
        HStack {
            Circle()
                .fill(task.isCompleted ? Color.green : Color.neuroNexaSecondary)
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(task.title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
                    .strikethrough(task.isCompleted)
                
                if let timeEstimate = task.estimatedDuration {
                    Text("\(Int(timeEstimate / 60)) min")
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
            }
            
            Spacer()
            
            if task.priority == .high {
                Image(systemName: "exclamationmark.circle.fill")
                    .foregroundColor(.red)
                    .accessibilityLabel("High priority task")
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct CognitiveMetricCard: View {
    let title: String
    let value: Double
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .accessibilityLabel("\(title) metric")
            
            Text(title)
                .font(.caption)
                .foregroundColor(.neuroNexaTextSecondary)
            
            Text("\(Int(value * 100))%")
                .font(.body)
                .fontWeight(.bold)
                .foregroundColor(.neuroNexaText)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title) metric")
        .accessibilityValue("\(Int(value * 100)) percent")
        .accessibilityHint("Current \(title.lowercased()) level indicator")
    }
}

@available(iOS 18.0, *)
struct ActivityCard: View {
    let activity: RecentActivity
    
    var body: some View {
        HStack {
            Image(systemName: activity.icon)
                .font(.title3)
                .foregroundColor(.neuroNexaPrimary)
                .frame(width: 24)
                .accessibilityLabel("\(activity.type) activity")
            
            VStack(alignment: .leading, spacing: 2) {
                Text(activity.title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
                
                Text(activity.timestamp, style: .relative)
                    .font(.caption)
                    .foregroundColor(.neuroNexaTextSecondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Recent activity: \(activity.title)")
        .accessibilityValue("\(activity.timestamp, style: .relative)")
    }
}

// MARK: - Dashboard Supporting Types

@available(iOS 18.0, *)
struct DashboardTask: Identifiable {
    let id = UUID()
    let title: String
    let isCompleted: Bool
    let priority: TaskPriority
    let estimatedDuration: TimeInterval?
}

@available(iOS 18.0, *)
extension TaskPriority {
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .urgent: return .red
        }
    }
}

@available(iOS 18.0, *)
struct TaskPriority: Hashable {
    static let low = TaskPriority(rawValue: "low")
    static let medium = TaskPriority(rawValue: "medium")
    static let high = TaskPriority(rawValue: "high")
    static let urgent = TaskPriority(rawValue: "urgent")
    
    let rawValue: String
}

// MARK: - RecentActivity Type Extension

@available(iOS 18.0, *)
extension RecentActivity {
    var type: String {
        switch icon {
        case "lungs.fill": return "Breathing"
        case "checkmark.circle.fill": return "Task completion"
        case "pause.circle.fill": return "Break"
        default: return "Activity"
        }
    }
}
