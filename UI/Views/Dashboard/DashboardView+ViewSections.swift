import SwiftUI

@available(iOS 18.0, *)
extension DashboardView {
    // MARK: - View Sections
    
    var headerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Good morning!")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
                
                Spacer()
                
                But<PERSON>(action: {}, label: {
                    Image(systemName: "person.circle")
                        .font(.title2)
                        .foregroundColor(.neuroNexaPrimary)
                })
            }
            
            Text("How are you feeling today?")
                .font(.body)
                .foregroundColor(.neuroNexaSecondaryText)
        }
        .padding(.horizontal)
    }
    
    var cognitiveLoadSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cognitive Load")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
            
            HStack(spacing: 12) {
                ForEach([CognitiveLoadLevel.low, .medium, .high], id: \.self) { level in
                    <PERSON><PERSON>(action: {
                        currentCognitiveLoad = level
                    }, label: {
                        Text(level.rawValue.capitalized)
                            .font(.caption)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                currentCognitiveLoad == level ?
                                Color.neuroNexaPrimary : Color.neuroNexaCardBackground
                            )
                            .foregroundColor(
                                currentCognitiveLoad == level ?
                                .white : .neuroNexaText
                            )
                            .cornerRadius(16)
                    })
                }
            }
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    var wellnessMetricsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Wellness Metrics")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
            
            HStack(spacing: 16) {
                MetricCard(title: "Energy", value: energyLevel, color: .green)
                MetricCard(title: "Focus", value: focusLevel, color: .blue)
            }
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    var tasksSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Today's Tasks")
                    .font(.headline)
                    .foregroundColor(.neuroNexaText)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to tasks
                }
                .font(.caption)
                .foregroundColor(.neuroNexaPrimary)
            }
            
            if todaysTasks.isEmpty {
                Text("No tasks for today")
                    .font(.body)
                    .foregroundColor(.neuroNexaSecondaryText)
                    .padding(.vertical, 20)
            } else {
                ForEach(todaysTasks.prefix(3)) { task in
                    TaskRowView(task: task)
                }
            }
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    var breathingSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Breathing Exercises")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
            
            Button(action: {
                // Navigate to breathing
            }, label: {
                HStack {
                    Image(systemName: "lungs")
                        .font(.title2)
                        .foregroundColor(.neuroNexaPrimary)

                    VStack(alignment: .leading) {
                        Text("Start Breathing Exercise")
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(.neuroNexaText)

                        Text("Reduce stress and improve focus")
                            .font(.caption)
                            .foregroundColor(.neuroNexaSecondaryText)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.neuroNexaSecondaryText)
                }
                .padding()
                .background(Color.neuroNexaCardBackground.opacity(0.5))
                .cornerRadius(8)
            })
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct MetricCard: View {
    let title: String
    let value: Int
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.neuroNexaSecondaryText)
            
            Text("\(value)%")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color.neuroNexaBackground)
        .cornerRadius(8)
    }
}

@available(iOS 18.0, *)
struct TaskRowView: View {
    let task: AITask
    
    var body: some View {
        HStack {
            Circle()
                .fill(priorityColor(for: task.priority))
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(task.title)
                    .font(.body)
                    .foregroundColor(.neuroNexaText)
                    .lineLimit(1)
                
                if let duration = task.estimatedDuration {
                    Text("\(Int(duration / 60)) min")
                        .font(.caption)
                        .foregroundColor(.neuroNexaSecondaryText)
                }
            }
            
            Spacer()
            
            Button(action: {}, label: {
                Image(systemName: "circle")
                    .font(.body)
                    .foregroundColor(.neuroNexaSecondaryText)
                    .accessibilityLabel("Task completion status")
            })
        }
        .padding(.vertical, 4)
    }
    
    private func priorityColor(for priority: TaskPriority) -> Color {
        switch priority {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}
