import SwiftUI

@available(iOS 18.0, *)
extension DashboardView {
    // MARK: - Loading and Status Views
    
    var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            Text("Loading Dashboard...")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityLabel("Loading dashboard data")
    }
    
    var breakSuggestionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "pause.circle.fill")
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("Break suggestion")
                
                Text("Time for a Break")
                    .font(.headline)
                    .foregroundColor(.neuroNexaText)
                
                Spacer()
            }
            
            Text("You've been focused for a while. Consider taking a short break to recharge.")
                .font(.body)
                .foregroundColor(.neuroNexaSecondaryText)
            
            Button("Start Break Timer") {
                // Handle break timer
            }
            .buttonStyle(.borderedProminent)
            .tint(.neuroNexaPrimary)
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
    }
    
    var welcomeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Good \(timeOfDayGreeting)")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.neuroNexaText)
                    
                    Text("Ready to focus today?")
                        .font(.body)
                        .foregroundColor(.neuroNexaSecondaryText)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.orange)
                            .accessibilityLabel("Streak icon")
                        
                        Text("\(currentStreak)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.neuroNexaText)
                    }
                    
                    Text("day streak")
                        .font(.caption)
                        .foregroundColor(.neuroNexaSecondaryText)
                }
            }
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
        .accessibilityLabel("Welcome section")
        .accessibilityValue("Good \(timeOfDayGreeting), \(currentStreak) day streak")
    }
    
    var cognitiveStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cognitive Status")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
                .accessibilityAddTraits(.isHeader)
            
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Load")
                            .font(.caption)
                            .foregroundColor(.neuroNexaSecondaryText)
                        
                        Text(currentCognitiveLoad.rawValue.capitalized)
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(.neuroNexaText)
                    }
                    
                    Spacer()
                    
                    Circle()
                        .fill(cognitiveLoadColor(for: currentCognitiveLoad))
                        .frame(width: 12, height: 12)
                        .accessibilityLabel("Cognitive load indicator")
                        .accessibilityValue(currentCognitiveLoad.rawValue)
                }
                
                ProgressView(value: cognitiveLoadProgress(for: currentCognitiveLoad))
                    .progressViewStyle(LinearProgressViewStyle(tint: cognitiveLoadColor(for: currentCognitiveLoad)))
                    .frame(maxWidth: .infinity)
            }
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
    }
    
    var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Activity")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
                .accessibilityAddTraits(.isHeader)
            
            if recentActivities.isEmpty {
                Text("No recent activity")
                    .font(.body)
                    .foregroundColor(.neuroNexaSecondaryText)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                ForEach(recentActivities, id: \.self) { activity in
                    HStack {
                        Circle()
                            .fill(Color.neuroNexaPrimary)
                            .frame(width: 8, height: 8)
                        
                        Text(activity)
                            .font(.body)
                            .foregroundColor(.neuroNexaText)
                        
                        Spacer()
                    }
                    .padding(.vertical, 2)
                }
            }
        }
        .padding()
        .background(Color.neuroNexaCardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Helper Functions
    
    private func cognitiveLoadColor(for level: CognitiveLoadLevel) -> Color {
        switch level {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
    
    private func cognitiveLoadProgress(for level: CognitiveLoadLevel) -> Double {
        switch level {
        case .low: return 0.3
        case .medium: return 0.6
        case .high: return 0.9
        }
    }
}
