import Accessibility
import SwiftUI

// MARK: - iOS 18 SwiftUI Extensions

@available(iOS 18.0, *)
extension View {
    /// Applies cognitive load optimization to reduce interface complexity when needed
    func cognitiveLoadOptimized() -> some View {
        self.modifier(CognitiveLoadOptimizedModifier())
    }

    /// Adapts interface based on cognitive load level
    func cognitiveLoadAdaptive() -> some View {
        self.modifier(CognitiveLoadAdaptiveModifier())
    }

    /// Applies sensory-friendly adaptations for neurodivergent users
    func sensoryAdaptive() -> some View {
        self.modifier(SensoryAdaptiveModifier())
    }

    /// Enhanced accessibility features for iOS 18
    func accessibilityEnhanced() -> some View {
        self.modifier(AccessibilityEnhancedModifier())
    }

    /// Sensory-friendly animations that respect motion preferences
    func sensoryFriendlyAnimation<V: Equatable>(_ animation: Animation, value: V) -> some View {
        self.animation(animation.sensoryAdapted(), value: value)
    }

    /// Neurodiversity-optimized button style
    func neuroButtonStyle() -> some View {
        self.modifier(NeuroButtonStyleModifier())
    }

    /// ADHD-friendly focus assistance
    func adhdFocusAssistance() -> some View {
        self.modifier(ADHDFocusAssistanceModifier())
    }

    /// Autism-friendly predictable interface
    func autismFriendlyInterface() -> some View {
        self.modifier(AutismFriendlyInterfaceModifier())
    }
}

// MARK: - Cognitive Load Optimization
@available(iOS 18.0, *)
struct CognitiveLoadOptimizedModifier: ViewModifier {
    @EnvironmentObject var appState: AppState

    func body(content: Content) -> some View {
        content
            .opacity(appState.cognitiveLoadLevel == .high ? 0.9 : 1.0)
            .scaleEffect(appState.cognitiveLoadLevel == .high ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.3), value: appState.cognitiveLoadLevel)
    }
}

@available(iOS 18.0, *)
struct CognitiveLoadAdaptiveModifier: ViewModifier {
    @EnvironmentObject var appState: AppState

    func body(content: Content) -> some View {
        switch appState.cognitiveLoadLevel {
        case .low:
            content
                .transition(.scale.combined(with: .opacity))
        case .medium:
            content
                .transition(.opacity)
        case .high:
            content
                .transition(.opacity)
                .animation(.linear(duration: 0.2), value: appState.cognitiveLoadLevel)
        @unknown default:
            content
                .transition(.opacity)
        }
    }
}

// MARK: - Sensory Adaptation
@available(iOS 18.0, *)
struct SensoryAdaptiveModifier: ViewModifier {
    @Environment(\.accessibilityReduceMotion) var reduceMotion
    @Environment(\.accessibilityDifferentiateWithoutColor) var differentiateWithoutColor
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager

    func body(content: Content) -> some View {
        content
            .brightness(sensoryBrightnessAdjustment)
            .contrast(sensoryContrastAdjustment)
            .saturation(sensorySaturationAdjustment)
    }

    private var sensoryBrightnessAdjustment: Double {
        switch neuroAccessibilityManager.sensoryAdaptationLevel {
        case .minimal: 0.0
        case .normal: -0.1
        case .high: -0.2
        }
    }

    private var sensoryContrastAdjustment: Double {
        switch neuroAccessibilityManager.sensoryAdaptationLevel {
        case .minimal: 1.0
        case .normal: 1.1
        case .high: 1.3
        }
    }

    private var sensorySaturationAdjustment: Double {
        switch neuroAccessibilityManager.sensoryAdaptationLevel {
        case .minimal: 1.0
        case .normal: 0.9
        case .high: 0.7
        }
    }
}

// MARK: - Enhanced Accessibility
@available(iOS 18.0, *)
struct AccessibilityEnhancedModifier: ViewModifier {
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager

    func body(content: Content) -> some View {
        content
            .accessibilityElement(children: .contain)
            .accessibilityAddTraits(neuroAccessibilityTraits)
            .accessibilityAction(.default) {
                // Enhanced accessibility action
            }
    }

    private var neuroAccessibilityTraits: AccessibilityTraits {
        var traits: AccessibilityTraits = []

        if neuroAccessibilityManager.cognitiveAssistanceLevel == .enhanced {
            traits.insert(.allowsDirectInteraction)
        }

        return traits
    }
}

// MARK: - Button Styles
@available(iOS 18.0, *)
struct NeuroButtonStyleModifier: ViewModifier {
    @EnvironmentObject var appState: AppState
    @Environment(\.isEnabled) var isEnabled

    func body(content: Content) -> some View {
        content
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(buttonBackground)
            .foregroundColor(buttonForeground)
            .cornerRadius(buttonCornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: buttonCornerRadius)
                    .stroke(buttonBorderColor, lineWidth: buttonBorderWidth)
            )
            .scaleEffect(isEnabled ? 1.0 : 0.95)
            .opacity(isEnabled ? 1.0 : 0.6)
            .animation(.easeInOut(duration: 0.2), value: isEnabled)
    }

    private var buttonBackground: Color {
        switch appState.cognitiveLoadLevel {
        case .low, .medium:
            .neuroNexaPrimary
        case .high:
            .neuroNexaSecondary
        @unknown default:
            .neuroNexaPrimary
        }
    }

    private var buttonForeground: Color {
        .white
    }

    private var buttonCornerRadius: CGFloat {
        switch appState.cognitiveLoadLevel {
        case .low, .medium: 12
        case .high: 8
        @unknown default: 10
        }
    }

    private var buttonBorderColor: Color {
        .neuroNexaPrimary.opacity(0.3)
    }

    private var buttonBorderWidth: CGFloat {
        appState.cognitiveLoadLevel == .high ? 2 : 1
    }
}

// MARK: - ADHD Focus Assistance
@available(iOS 18.0, *)
struct ADHDFocusAssistanceModifier: ViewModifier {
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager
    @State private var focusIndicatorOpacity: Double = 0.0

    func body(content: Content) -> some View {
        content
            .overlay(
                focusIndicator
                    .opacity(focusIndicatorOpacity)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: focusIndicatorOpacity)
            )
            .onAppear {
                if neuroAccessibilityManager.isADHDModeEnabled {
                    focusIndicatorOpacity = 0.3
                }
            }
    }

    private var focusIndicator: some View {
        RoundedRectangle(cornerRadius: 8)
            .stroke(Color.neuroNexaAccent, lineWidth: 2)
            .padding(-4)
    }
}

// MARK: - Autism-Friendly Interface
@available(iOS 18.0, *)
struct AutismFriendlyInterfaceModifier: ViewModifier {
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager

    func body(content: Content) -> some View {
        content
            .background(predictableBackground)
            .overlay(
                consistentBorder
            )
    }

    private var predictableBackground: Color {
        if neuroAccessibilityManager.isAutismModeEnabled {
            Color.neuroNexaBackground.opacity(0.8)
        } else {
            Color.clear
        }
    }

    private var consistentBorder: some View {
        RoundedRectangle(cornerRadius: 8)
            .stroke(Color.neuroNexaSecondary.opacity(0.2), lineWidth: 1)
    }
}

// MARK: - Animation Extensions
@available(iOS 18.0, *)
extension Animation {
    func sensoryAdapted() -> Animation {
        // Reduce animation intensity for sensory sensitivity
        .easeInOut(duration: 0.5)
    }
}

// MARK: - Color Extensions
@available(iOS 18.0, *)
extension Color {
    static let neuroNexaPrimary = Color("NeuroNexaPrimary")
    static let neuroNexaSecondary = Color("NeuroNexaSecondary")
    static let neuroNexaAccent = Color("NeuroNexaAccent")
    static let neuroNexaBackground = Color("NeuroNexaBackground")
    static let neuroNexaSurface = Color("NeuroNexaSurface")
    static let neuroNexaText = Color("NeuroNexaText")
    static let neuroNexaTextSecondary = Color("NeuroNexaTextSecondary")

    // Cognitive load adaptive colors
    static func adaptiveColor(for cognitiveLoad: CognitiveLoadLevel) -> Color {
        switch cognitiveLoad {
        case .low:
            .neuroNexaPrimary
        case .medium:
            .neuroNexaSecondary
        case .high:
            .neuroNexaAccent.opacity(0.7)
        @unknown default:
            .neuroNexaSecondary
        }
    }

    // Sensory-friendly color variants
    var sensoryFriendly: Color {
        self.opacity(0.8)
    }

    // High contrast variants for accessibility
    var highContrast: Color {
        self.opacity(1.0)
    }
}

// MARK: - Font Extensions
@available(iOS 18.0, *)
extension Font {
    static func neuroNexaTitle() -> Font {
        .system(size: 28, weight: .bold, design: .rounded)
    }

    static func neuroNexaHeadline() -> Font {
        .system(size: 22, weight: .semibold, design: .rounded)
    }

    static func neuroNexaBody() -> Font {
        .system(size: 16, weight: .regular, design: .rounded)
    }

    static func neuroNexaCaption() -> Font {
        .system(size: 14, weight: .medium, design: .rounded)
    }

    // Cognitive load adaptive fonts
    static func adaptiveFont(for cognitiveLoad: CognitiveLoadLevel) -> Font {
        switch cognitiveLoad {
        case .low:
            .neuroNexaBody()
        case .medium:
            .system(size: 17, weight: .regular, design: .rounded)
        case .high:
            .system(size: 18, weight: .medium, design: .rounded)
        @unknown default:
            .system(size: 17, weight: .regular, design: .rounded)
        }
    }
}

// MARK: - Layout Extensions
@available(iOS 18.0, *)
struct AdaptiveStack<Content: View>: View {
    let content: Content
    @EnvironmentObject var appState: AppState

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        Group {
            if appState.cognitiveLoadLevel == .high {
                VStack(spacing: 20) {
                    content
                }
            } else {
                LazyVStack(spacing: 16) {
                    content
                }
            }
        }
    }
}
