import SwiftUI

// MARK: - TaskCard Accessibility Support Methods

@available(iOS 18.0, *)
extension TaskCard {
    var accessibilityValue: String {
        var components: [String] = []
        
        components.append(task.isCompleted ? "Completed" : "Not completed")
        
        if task.progress > 0 {
            components.append("\(Int(task.progress * 100)) percent complete")
        }
        
        components.append("Priority: \(task.priority.rawValue)")
        
        if let duration = task.estimatedDuration {
            components.append("Estimated time: \(Int(duration / 60)) minutes")
        }
        
        if cognitiveLoad == .high || cognitiveLoad == .overload {
            components.append("Interface adapted for high cognitive load")
        }
        
        return components.joined(separator: ", ")
    }
    
    var accessibilityTraits: AccessibilityTraits {
        var traits: AccessibilityTraits = []
        
        if task.isCompleted {
            traits.insert(.notEnabled)
        }
        
        if task.priority == .urgent {
            traits.insert(.isSelected)
        }
        
        if task.progress > 0 {
            traits.insert(.updatesFrequently)
        }
        
        return traits
    }
    
    func announceTaskActivation() {
        let announcement = "Task \(task.title) activated. \(task.isCompleted ? "Task is completed" : "Task is active")."
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    func announceTaskCompletion() {
        let announcement = task.isCompleted ? "Task already completed" : "Marking task as complete"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    func announceTaskEdit() {
        UIAccessibility.post(
            notification: .announcement,
            argument: "Opening task editor for \(task.title)"
        )
    }
    
    func announceDetailsToggle() {
        let state = showingDetails ? "expanded" : "collapsed"
        UIAccessibility.post(
            notification: .announcement,
            argument: "Task details \(state)"
        )
    }
    
    func announceTaskPresence() {
        let status = task.isCompleted ? "completed" : "active"
        let priority = task.priority.rawValue
        UIAccessibility.post(
            notification: .announcement,
            argument: "\(priority) priority task: \(task.title), \(status)"
        )
    }
    
    func announceDetailedTaskInfo() {
        var details = ["Task: \(task.title)"]
        
        if !task.description.isEmpty {
            details.append("Description: \(task.description)")
        }
        
        details.append("Priority: \(task.priority.rawValue)")
        details.append("Status: \(task.isCompleted ? "Completed" : "Active")")
        
        if task.progress > 0 {
            details.append("Progress: \(Int(task.progress * 100)) percent")
        }
        
        if let duration = task.estimatedDuration {
            details.append("Estimated duration: \(Int(duration / 60)) minutes")
        }
        
        details.append("Current cognitive load: \(cognitiveLoad.rawValue)")
        
        UIAccessibility.post(
            notification: .announcement,
            argument: details.joined(separator: ", ")
        )
    }
}

// MARK: - TaskCard Configuration Properties

@available(iOS 18.0, *)
extension TaskCard {
    var adaptiveSpacing: CGFloat {
        configuration.adaptiveSpacing
    }

    var adaptivePadding: EdgeInsets {
        configuration.adaptivePadding
    }

    var adaptiveCornerRadius: CGFloat {
        configuration.adaptiveCornerRadius
    }

    var adaptiveShadow: Shadow {
        configuration.adaptiveShadow
    }

    var backgroundColor: Color {
        configuration.backgroundColor
    }

    var borderColor: Color {
        configuration.borderColor
    }

    var borderWidth: CGFloat {
        configuration.borderWidth
    }

    var adaptiveTitleFont: Font {
        configuration.adaptiveTitleFont
    }

    var adaptiveBodyFont: Font {
        configuration.adaptiveBodyFont
    }

    var adaptiveCaptionFont: Font {
        configuration.adaptiveCaptionFont
    }

    var adaptiveFontWeight: Font.Weight {
        configuration.adaptiveFontWeight
    }

    var titleColor: Color {
        configuration.titleColor
    }

    var descriptionColor: Color {
        configuration.descriptionColor
    }

    var progressColor: Color {
        configuration.progressColor
    }

    var adaptiveAnimation: Animation? {
        configuration.adaptiveAnimation
    }

    var priorityIndicatorWidth: CGFloat {
        configuration.priorityIndicatorWidth
    }

    var priorityIndicatorHeight: CGFloat {
        configuration.priorityIndicatorHeight
    }

    var shouldShowDescription: Bool {
        configuration.shouldShowDescription
    }

    var shouldShowCognitiveLoad: Bool {
        configuration.shouldShowCognitiveLoad
    }

    var shouldShowExecutiveFunctionSupport: Bool {
        configuration.shouldShowExecutiveFunctionSupport
    }

    var descriptionLineLimit: Int {
        configuration.descriptionLineLimit
    }

    var maxVisibleSteps: Int {
        configuration.maxVisibleSteps
    }

    var durationText: String {
        configuration.durationText
    }

    var accessibilityLabel: String {
        configuration.accessibilityLabel
    }

    var accessibilityHint: String {
        configuration.accessibilityHint
    }
}