import SwiftUI

// MARK: - Task Card Configuration Helper

/// Configuration helper for TaskCard adaptive properties
@available(iOS 26.0, *)
struct TaskCardConfiguration {
    let task: AITask
    let cognitiveLoad: CognitiveLoadLevel
    let sensoryPreferences: SensoryPreferences
    let executiveFunctionLevel: ExecutiveFunctionLevel

    // MARK: - Adaptive Spacing & Layout

    var adaptiveSpacing: CGFloat {
        NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: NeuroNexaDesignSystem.Spacing.md,
            cognitiveLoad: cognitiveLoad
        )
    }

    var adaptivePadding: EdgeInsets {
        let basePadding: CGFloat = 16
        let multiplier = NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: 1.0,
            cognitiveLoad: cognitiveLoad
        )

        let padding = basePadding * multiplier
        return EdgeInsets(top: padding, leading: padding, bottom: padding, trailing: padding)
    }

    var adaptiveCornerRadius: CGFloat {
        NeuroNexaDesignSystem.CornerRadius.adaptive(for: cognitiveLoad)
    }

    var adaptiveShadow: Shadow {
        NeuroNexaDesignSystem.Shadows.adaptiveShadow(for: sensoryPreferences)
    }

    // MARK: - Colors

    var backgroundColor: Color {
        if task.isCompleted {
            return NeuroNexaDesignSystem.Colors.lightGray.opacity(0.5)
        } else {
            return NeuroNexaDesignSystem.Colors.backgroundColor(for: sensoryPreferences.colorContrast)
        }
    }

    var borderColor: Color {
        if task.isCompleted {
            return .green.opacity(0.3)
        } else {
            return task.priority.color.opacity(0.2)
        }
    }

    var borderWidth: CGFloat {
        switch cognitiveLoad {
        case .low: return 1
        case .medium: return 1.5
        case .high: return 2
        case .overload: return 2.5
        }
    }

    var titleColor: Color {
        task.isCompleted ? .secondary : .primary
    }

    var descriptionColor: Color {
        task.isCompleted ? .secondary : NeuroNexaDesignSystem.Colors.darkGray
    }

    var progressColor: Color {
        task.priority.color
    }

    // MARK: - Typography

    var adaptiveTitleFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: NeuroNexaDesignSystem.Typography.headline,
            cognitiveLoad: cognitiveLoad
        )
    }

    var adaptiveBodyFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: NeuroNexaDesignSystem.Typography.body,
            cognitiveLoad: cognitiveLoad
        )
    }

    var adaptiveCaptionFont: Font {
        NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: NeuroNexaDesignSystem.Typography.caption,
            cognitiveLoad: cognitiveLoad
        )
    }

    var adaptiveFontWeight: Font.Weight {
        switch cognitiveLoad {
        case .low: return .medium
        case .medium: return .semibold
        case .high: return .bold
        case .overload: return .heavy
        }
    }

    // MARK: - Animation

    var adaptiveAnimation: Animation? {
        let motionSensitivity: MotionSensitivity
        switch sensoryPreferences.motionSensitivity {
        case 0.0..<0.2: motionSensitivity = .none
        case 0.2..<0.4: motionSensitivity = .low
        case 0.4..<0.6: motionSensitivity = .medium
        case 0.6..<0.8: motionSensitivity = .high
        default: motionSensitivity = .extreme
        }
        return NeuroNexaDesignSystem.Animation.adaptive(for: motionSensitivity)
    }

    // MARK: - Layout Constants

    var priorityIndicatorWidth: CGFloat { 4 }
    var priorityIndicatorHeight: CGFloat { 40 }

    // MARK: - Conditional Display Logic

    var shouldShowDescription: Bool {
        switch cognitiveLoad {
        case .low: return true
        case .medium: return !task.description.isEmpty
        case .high: return !task.description.isEmpty && task.description.count < 100
        case .overload: return false
        }
    }

    var shouldShowCognitiveLoad: Bool {
        executiveFunctionLevel == .low || cognitiveLoad == .high || cognitiveLoad == .overload
    }

    var shouldShowExecutiveFunctionSupport: Bool {
        executiveFunctionLevel == .low && !task.aiGeneratedSteps.isEmpty
    }

    var descriptionLineLimit: Int {
        switch cognitiveLoad {
        case .low: return 3
        case .medium: return 2
        case .high: return 1
        case .overload: return 1
        }
    }

    var maxVisibleSteps: Int {
        switch cognitiveLoad {
        case .low: return 3
        case .medium: return 2
        case .high: return 1
        case .overload: return 1
        }
    }

    var durationText: String {
        let minutes = Int(task.estimatedDuration / 60)
        return "\(minutes)m"
    }

    // MARK: - Accessibility

    var accessibilityLabel: String {
        var label = "\(task.title). \(task.priority.rawValue) priority."

        if task.isCompleted {
            label += " Completed."
        } else if task.progress > 0 {
            label += " \(Int(task.progress * 100))% complete."
        }

        return label
    }

    var accessibilityHint: String {
        if task.isCompleted {
            return "Task is completed. Double tap to view details."
        } else {
            return "Double tap to start task. Long press for options."
        }
    }

    // MARK: - Haptic Feedback

    @MainActor
    func provideHapticFeedback() {
        let intensity: CGFloat
        switch sensoryPreferences.vibrationIntensity {
        case .off: intensity = 0.0
        case .light: intensity = 0.3
        case .medium: intensity = 0.6
        case .strong: intensity = 1.0
        }

        if intensity > 0 {
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred(intensity: intensity)
        }
    }
}
