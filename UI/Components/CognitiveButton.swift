import SwiftUI

// MARK: - Cognitive Button - Neurodiversity-Optimized Button Component

/// A button component specifically designed for neurodiversity support
/// Features adaptive sizing, cognitive load awareness, and sensory considerations
@available(iOS 18.0, *)
struct CognitiveButton: View {
    // MARK: - Properties
    let title: String
    let action: () -> Void
    let style: CognitiveButtonStyle
    let priority: TaskPriority

    // Environment values for adaptive behavior
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings
    @Environment(\.executiveFunctionLevel) private var executiveFunctionLevel

    // State for interaction feedback
    @State private var isPressed = false
    @State private var isHovered = false

    // MARK: - Initialization
    init(
        _ title: String,
        style: CognitiveButtonStyle = .primary,
        priority: TaskPriority = .medium,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.style = style
        self.priority = priority
        self.action = action
    }

    // MARK: - Body
    var body: some View {
        buttonContent
            .buttonStyle(CognitiveButtonPressStyle())
            .accessibilityLabel(accessibilityLabel)
            .accessibilityHint(accessibilityHint)
            .accessibilityAddTraits(accessibilityTraits)
            .accessibilityIdentifier("CognitiveButton_\(title.replacingOccurrences(of: " ", with: "_"))")
            .accessibilityValue(accessibilityValue)
            .accessibilityRespondsToUserInteraction(true)
            .accessibilityShowsLargeContentViewer()
            .accessibilityInputLabels([accessibilityInputLabel])
            .dynamicTypeSize(.small ... .accessibility5)
            .onLongPressGesture(minimumDuration: 0.1, maximumDistance: 50) {
                // Provide haptic feedback for executive function support
                provideHapticFeedback()
                announceActivation()
                action()
            } onPressingChanged: { pressing in
                withAnimation(adaptiveAnimation) {
                    isPressed = pressing
                }
                // Announce press state for screen readers
                if pressing {
                    UIAccessibility.post(notification: .announcement, argument: "Button pressed")
                }
            }
            .sensoryFeedback(.impact(intensity: 0.5), trigger: isPressed)
            .sensoryFeedback(.success, trigger: isPressed)
            .accessibilityAction(named: "Activate") {
                announceActivation()
                action()
            }
            .accessibilityAction(named: "Get more information") {
                announceDetailedInfo()
            }
            .onHover { hovering in
                withAnimation(adaptiveAnimation) {
                    isHovered = hovering
                }
                // Announce hover state for assistive technologies
                if hovering {
                    UIAccessibility.post(
                        notification: .announcement,
                        argument: "\(title) button focused"
                    )
                }
            }
            .focusable(true)
            .focusEffectDisabled(false)
    }

    // MARK: - Button Content
    private var buttonContent: some View {
        Button(action: action) {
            buttonLabel
                .padding(adaptivePadding)
                .frame(minHeight: minimumTouchTarget)
                .background(backgroundColor)
                .cornerRadius(adaptiveCornerRadius)
                .neuroNexaShadow(adaptiveShadow)
                .scaleEffect(interactionScale)
                .animation(adaptiveAnimation, value: interactionScale)
        }
    }

    // MARK: - Button Label
    private var buttonLabel: some View {
        HStack(spacing: adaptiveSpacing) {
            // Icon based on priority (optional)
            if shouldShowIcon {
                priorityIcon
                    .font(adaptiveIconSize)
                    .foregroundColor(iconColor)
            }

            // Button text
            Text(title)
                .font(adaptiveFont)
                .fontWeight(adaptiveFontWeight)
                .foregroundColor(textColor)
                .lineLimit(nil)
                .multilineTextAlignment(.center)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    // MARK: - Computed Properties - Moved to CognitiveButton+Extensions.swift

    private var backgroundColor: Color {
        let baseColor = switch style {
        case .primary: Color.neuroNexaPrimary
        case .secondary: Color.neuroNexaSecondary
        case .tertiary: Color.clear
        case .destructive: Color.red
        }

        return isPressed ? baseColor.opacity(0.8) :
            isHovered ? baseColor.opacity(0.9) :
            baseColor
    }

    private var textColor: Color {
        switch style {
        case .primary, .destructive: .white
        case .secondary: Color.neuroNexaText
        case .tertiary: Color.neuroNexaPrimary
        }
    }

    private var iconColor: Color {
        textColor
    }

    // MARK: - Layout Properties - Moved to CognitiveButton+Extensions.swift

    // MARK: - Accessibility

    private var accessibilityLabel: String {
        let cognitiveContext = shouldShowCognitiveContext ? " - Cognitive load: \(cognitiveLoad.rawValue)" : ""
        return "\(title)\(cognitiveContext)"
    }

    private var accessibilityHint: String {
        let priorityHint = switch priority {
        case .low: "Low priority action"
        case .medium: "Medium priority action"
        case .high: "High priority action"
        case .urgent: "Urgent action requiring immediate attention"
        }
        
        let cognitiveHint = getCognitiveHint()
        let interactionHint = "Double tap to activate, or use VoiceOver actions for more options."
        
        return "\(priorityHint). \(cognitiveHint) \(interactionHint)"
    }

    private var accessibilityTraits: AccessibilityTraits {
        var traits: AccessibilityTraits = [.isButton]

        if priority == .urgent {
            traits.insert(.isSelected)
        }

        return traits
    }

    // MARK: - Accessibility Support Methods
    
    private var accessibilityValue: String {
        var components: [String] = []
        
        if shouldShowIcon {
            components.append("Has \(priority.rawValue) priority icon")
        }
        
        if cognitiveLoad == .high || cognitiveLoad == .overload {
            components.append("Optimized for high cognitive load")
        }
        
        return components.isEmpty ? "" : components.joined(separator: ", ")
    }
    
    private var accessibilityInputLabel: String {
        // Simplified label for voice control
        title.lowercased().replacingOccurrences(of: " ", with: "")
    }
    
    private var shouldShowCognitiveContext: Bool {
        cognitiveLoad == .high || cognitiveLoad == .overload
    }
    
    private func getCognitiveHint() -> String {
        switch cognitiveLoad {
        case .low:
            return "Cognitive load is low, full functionality available."
        case .medium:
            return "Cognitive load is moderate, interface adapted for clarity."
        case .high:
            return "Cognitive load is high, interface simplified for easier use."
        case .overload:
            return "Cognitive overload detected, interface optimized for essential actions only."
        }
    }
    
    private func announceActivation() {
        let announcement = "\(title) activated. \(getCognitiveHint())"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    private func announceDetailedInfo() {
        let details = """
        Button: \(title). 
        Priority: \(priority.rawValue). 
        Style: \(style). 
        Current cognitive load: \(cognitiveLoad.rawValue). 
        Touch target size: \(Int(minimumTouchTarget)) points.
        """
        UIAccessibility.post(notification: .announcement, argument: details)
    }

    // MARK: - Haptic Feedback

    private func provideHapticFeedback() {
        let intensity = sensoryPreferences.vibrationIntensity.intensity

        if intensity > 0 {
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred(intensity: CGFloat(intensity))
        }
    }

    // MARK: - Computed Properties

    private var adaptiveAnimation: Animation? {
        switch cognitiveLoad {
        case .low, .medium:
            return .easeInOut(duration: 0.2)
        case .high, .overload:
            return .easeInOut(duration: 0.3)
        }
    }

    private var adaptivePadding: EdgeInsets {
        switch cognitiveLoad {
        case .low, .medium:
            return EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16)
        case .high, .overload:
            return EdgeInsets(top: 16, leading: 20, bottom: 16, trailing: 20)
        }
    }

    private var minimumTouchTarget: CGFloat {
        switch cognitiveLoad {
        case .low, .medium:
            return 44
        case .high, .overload:
            return 56
        }
    }

    private var adaptiveCornerRadius: CGFloat {
        switch cognitiveLoad {
        case .low, .medium:
            return 8
        case .high, .overload:
            return 12
        }
    }

    private var adaptiveShadow: Shadow {
        switch cognitiveLoad {
        case .low, .medium:
            return Shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        case .high, .overload:
            return Shadow(color: .black.opacity(0.15), radius: 4, x: 0, y: 2)
        }
    }

    private var interactionScale: CGFloat {
        isPressed ? 0.95 : (isHovered ? 1.02 : 1.0)
    }

    private var adaptiveSpacing: CGFloat {
        switch cognitiveLoad {
        case .low, .medium:
            return 8
        case .high, .overload:
            return 12
        }
    }

    private var shouldShowIcon: Bool {
        priority != .low || cognitiveLoad == .high || cognitiveLoad == .overload
    }

    // MARK: - Priority Icon

    private var priorityIcon: some View {
        switch priority {
        case .low:
            return Image(systemName: "circle")
                .accessibilityLabel("Low priority task - can be done later")
        case .medium:
            return Image(systemName: "circle.fill")
                .accessibilityLabel("Medium priority task - moderate importance")
        case .high:
            return Image(systemName: "exclamationmark.circle.fill")
                .accessibilityLabel("High priority task - needs attention")
        case .urgent:
            return Image(systemName: "exclamationmark.triangle.fill")
                .accessibilityLabel("Urgent priority task - immediate action required")
        }
    }

    private var adaptiveIconSize: Font {
        switch cognitiveLoad {
        case .low, .medium:
            return .body
        case .high, .overload:
            return .title3
        }
    }

    private var adaptiveFont: Font {
        switch cognitiveLoad {
        case .low, .medium:
            return .body
        case .high, .overload:
            return .title3
        }
    }

    private var adaptiveFontWeight: Font.Weight {
        switch cognitiveLoad {
        case .low, .medium:
            return .medium
        case .high, .overload:
            return .semibold
        }
    }
}

// MARK: - Supporting Types

enum CognitiveButtonStyle {
    case primary
    case secondary
    case tertiary
    case destructive
}

struct CognitiveButtonPressStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .opacity(configuration.isPressed ? 0.8 : 1.0)
    }
}
