import SwiftUI

// MARK: - TaskCard View Sections

@available(iOS 18.0, *)
extension TaskCard {
    
    var headerSection: some View {
        HStack(alignment: .top, spacing: adaptiveSpacing) {
            // Priority indicator
            priorityIndicator

            VStack(alignment: .leading, spacing: 4) {
                // Task title
                Text(task.title)
                    .font(adaptiveTitleFont)
                    .fontWeight(adaptiveFontWeight)
                    .foregroundColor(titleColor)
                    .lineLimit(cognitiveLoad == .high ? 1 : 2)

                // Metadata row
                metadataRow
            }

            Spacer()

            // Completion status
            completionIndicator
        }
    }

    var descriptionSection: some View {
        Text(task.description)
            .font(adaptiveBodyFont)
            .foregroundColor(descriptionColor)
            .lineLimit(descriptionLineLimit)
            .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    var progressSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Progress")
                    .font(adaptiveCaptionFont)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(Int(task.progress * 100))%")
                    .font(adaptiveCaptionFont)
                    .fontWeight(.medium)
                    .foregroundColor(progressColor)
            }

            ProgressView(value: progressAnimation)
                .progressViewStyle(LinearProgressViewStyle())
                .tint(Color.neuroNexaPrimary)
                .accessibilityLabel("Task progress: \(Int(task.progress * 100)) percent complete")
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    var executiveFunctionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            if !task.aiGeneratedSteps.isEmpty {
                Text("Next Steps")
                    .font(adaptiveCaptionFont)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                ForEach(Array(task.aiGeneratedSteps.prefix(maxVisibleSteps).enumerated()), id: \.offset) { index, step in
                    HStack(spacing: 8) {
                        Image(systemName: "circle")
                            .foregroundColor(.secondary)
                            .font(.caption)
                            .accessibilityLabel("Step \(index + 1)")

                        Text(step)
                            .font(adaptiveCaptionFont)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                }
            }
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    var actionButtonsSection: some View {
        HStack(spacing: adaptiveSpacing) {
            // Complete button
            Button {
                onComplete()
            } label: {
                HStack {
                    Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    Text(task.isCompleted ? "Completed" : "Complete")
                }
                .foregroundColor(task.isCompleted ? .secondary : .white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(task.isCompleted ? Color.gray.opacity(0.3) : Color.neuroNexaPrimary)
                .cornerRadius(8)
            }
            .disabled(task.isCompleted)
            .accessibilityLabel(task.isCompleted ? "Task completed" : "Mark task as complete")
            .sensoryFeedback(.success, trigger: task.isCompleted)

            // Edit button (if provided)
            if let onEdit = onEdit {
                Button {
                    onEdit()
                } label: {
                    Text("Edit")
                        .foregroundColor(.neuroNexaPrimary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.neuroNexaPrimary, lineWidth: 1)
                        )
                }
                .accessibilityLabel("Edit task")
                .accessibilityHint("Modify task details")
            }
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    // MARK: - Helper Views

    var priorityIndicator: some View {
        RoundedRectangle(cornerRadius: 2)
            .fill(task.priority.color)
            .frame(width: priorityIndicatorWidth, height: priorityIndicatorHeight)
            .accessibilityLabel("\(task.priority.rawValue) priority")
    }

    var metadataRow: some View {
        HStack(spacing: 12) {
            // Estimated duration
            Label(
                durationText,
                systemImage: "clock"
            )
            .font(adaptiveCaptionFont)
            .foregroundColor(.secondary)

            // Cognitive load indicator
            if shouldShowCognitiveLoad {
                Label(
                    task.cognitiveLoad.rawValue,
                    systemImage: "brain"
                )
                .font(adaptiveCaptionFont)
                .foregroundColor(task.cognitiveLoad.color)
            }
        }
    }

    var completionIndicator: some View {
        Group {
            if task.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                    .accessibilityLabel("Task completed")
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.secondary)
                    .font(.title2)
                    .accessibilityLabel("Task not completed")
            }
        }
    }
}