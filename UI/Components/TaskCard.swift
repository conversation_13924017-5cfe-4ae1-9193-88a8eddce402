import SwiftUI

// MARK: - Task Card - Neurodiversity-Optimized Task Display Component

/// A card component for displaying AI tasks with cognitive load awareness and executive function support
@available(iOS 18.0, *)
struct TaskCard: View {
    // MARK: - Properties
    let task: AITask
    let onTap: () -> Void
    let onComplete: () -> Void
    let onEdit: (() -> Void)?

    // Environment values for adaptive behavior
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings
    @Environment(\.executiveFunctionLevel) private var executiveFunctionLevel

    // State for interaction and animation
    @State private var isPressed = false
    @State private var showingDetails = false
    @State private var progressAnimation = 0.0
    
    // Claude: PERFORMANCE - Memoized configuration to avoid repeated creation
    @State private var cachedConfiguration: TaskCardConfiguration?

    // MARK: - Initialization
    init(
        task: AITask,
        onTap: @escaping () -> Void,
        onComplete: @escaping () -> Void,
        onEdit: (() -> Void)? = nil
    ) {
        self.task = task
        self.onTap = onTap
        self.onComplete = onComplete
        self.onEdit = onEdit
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: adaptiveSpacing) {
            // Header with title and priority
            headerSection

            // Task description (adaptive based on cognitive load)
            if shouldShowDescription {
                descriptionSection
            }

            // Progress indicator
            if task.progress > 0 {
                progressSection
            }

            // Executive function support section
            if shouldShowExecutiveFunctionSupport {
                executiveFunctionSection
            }

            // Action buttons
            actionButtonsSection
        }
        .padding(adaptivePadding)
        .background(backgroundColor)
        .cornerRadius(adaptiveCornerRadius)
        .neuroNexaShadow(adaptiveShadow)
        .overlay(
            RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(adaptiveAnimation, value: isPressed)
        .animation(adaptiveAnimation, value: progressAnimation)
        .onTapGesture {
            announceTaskActivation()
            onTap()
            provideHapticFeedback()
        }
        .onLongPressGesture(minimumDuration: 0.1) {
            // Long press for additional options
            withAnimation {
                showingDetails.toggle()
            }
            announceDetailsToggle()
        } onPressingChanged: { pressing in
            withAnimation(adaptiveAnimation) {
                isPressed = pressing
            }
            if pressing {
                UIAccessibility.post(notification: .announcement, argument: "Task card pressed")
            }
        }
        .sensoryFeedback(.selection, trigger: showingDetails)
        .sensoryFeedback(.impact(intensity: 0.3), trigger: onTap)
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityValue(accessibilityValue)
        .accessibilityAddTraits([.isButton] + accessibilityTraits)
        .accessibilityIdentifier("TaskCard_\(task.id)")
        .accessibilityRespondsToUserInteraction(true)
        .accessibilityShowsLargeContentViewer()
        .accessibilityInputLabels(["Task \(task.title)", task.title.lowercased()])
        .accessibilityAction(named: "Complete Task") {
            announceTaskCompletion()
            onComplete()
        }
        .accessibilityAction(named: "Edit Task") {
            announceTaskEdit()
            onEdit?()
        }
        .accessibilityAction(named: "Toggle Details") {
            withAnimation {
                showingDetails.toggle()
            }
            announceDetailsToggle()
        }
        .accessibilityAction(named: "Get Task Information") {
            announceDetailedTaskInfo()
        }
        .onAppear {
            // Animate progress on appear
            withAnimation(.easeInOut(duration: 1.0)) {
                progressAnimation = task.progress
            }
            // Announce task appearance for screen readers
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                announceTaskPresence()
            }
        }
        .onChange(of: task.progress) { _, newProgress in
            withAnimation {
                progressAnimation = newProgress
            }
            UIAccessibility.post(
                notification: .announcement,
                argument: "Task progress updated to \(Int(newProgress * 100)) percent"
            )
        }
        .onChange(of: cognitiveLoad) { _, _ in
            // Claude: PERFORMANCE - Invalidate cache when cognitive load changes
            cachedConfiguration = nil
        }
        .onChange(of: executiveFunctionLevel) { _, _ in
            // Claude: PERFORMANCE - Invalidate cache when executive function changes
            cachedConfiguration = nil
        }
        .focusable(true)
        .focusEffectDisabled(false)
    }

    // MARK: - View Sections - Moved to TaskCard+ViewSections.swift

    // MARK: - Configuration Helper - Optimized for Performance

    private var configuration: TaskCardConfiguration {
        // Claude: PERFORMANCE - Use cached configuration to avoid repeated creation
        if let cached = cachedConfiguration {
            return cached
        }
        
        let newConfig = TaskCardConfiguration(
            task: task,
            cognitiveLoad: cognitiveLoad,
            sensoryPreferences: sensoryPreferences,
            executiveFunctionLevel: executiveFunctionLevel
        )
        
        // Cache the configuration
        DispatchQueue.main.async {
            cachedConfiguration = newConfig
        }
        
        return newConfig
    }

    // MARK: - Computed Properties

    // MARK: - Configuration forwarding properties moved to TaskCard+Configuration extension

    // MARK: - Layout Constants

    // MARK: - All remaining configuration properties moved to TaskCard+Configuration extension

    // MARK: - Accessibility Support - Moved to TaskCard+Extensions.swift

    // MARK: - Haptic Feedback

    private func provideHapticFeedback() {
        configuration.provideHapticFeedback()
    }
}

// MARK: - Preview

#if DEBUG
@available(iOS 18.0, *)
struct TaskCard_Previews: PreviewProvider {
    static var previews: some View {
        let sampleTask = AITask(
            title: "Complete project documentation",
            description: "Write comprehensive documentation for the NeuroNexa iOS app including user guides and technical specifications.",
            priority: .high,
            estimatedDuration: 3_600,
            cognitiveLoad: .medium
        )

        VStack(spacing: 20) {
            TaskCard(
                task: sampleTask,
                onTap: { print("Task tapped") },
                onComplete: { print("Task completed") },
                onEdit: { print("Task edited") }
            )
        }
        .padding()
        .environment(\.cognitiveLoadLevel, .medium)
        .environment(\.sensoryPreferences, .default)
        .environment(\.accessibilitySettings, .default)
        .environment(\.executiveFunctionLevel, .low)
    }
}
#endif
