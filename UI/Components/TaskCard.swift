import SwiftUI

// MARK: - Task Card - Neurodiversity-Optimized Task Display Component

/// A card component for displaying AI tasks with cognitive load awareness and executive function support
@available(iOS 26.0, *)
struct TaskCard: View {
    // MARK: - Properties
    let task: AITask
    let onTap: () -> Void
    let onComplete: () -> Void
    let onEdit: (() -> Void)?

    // Environment values for adaptive behavior
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings
    @Environment(\.executiveFunctionLevel) private var executiveFunctionLevel

    // State for interaction and animation
    @State private var isPressed = false
    @State private var showingDetails = false
    @State private var progressAnimation = 0.0
    
    // Claude: PERFORMANCE - Memoized configuration to avoid repeated creation
    @State private var cachedConfiguration: TaskCardConfiguration?

    // MARK: - Initialization
    init(
        task: AITask,
        onTap: @escaping () -> Void,
        onComplete: @escaping () -> Void,
        onEdit: (() -> Void)? = nil
    ) {
        self.task = task
        self.onTap = onTap
        self.onComplete = onComplete
        self.onEdit = onEdit
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: adaptiveSpacing) {
            // Header with title and priority
            headerSection

            // Task description (adaptive based on cognitive load)
            if shouldShowDescription {
                descriptionSection
            }

            // Progress and action buttons
            bottomSection
        }
        .padding(adaptivePadding)
        .background(cardBackground)
        .cornerRadius(cornerRadius)
        .shadow(color: shadowColor, radius: shadowRadius, x: 0, y: shadowOffset)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            onTap()
        }
        .accessibilityAddTraits(.isButton)
        .onLongPressGesture(minimumDuration: 0.1) {
            // No action needed - animation handled by state
        } onPressingChanged: { pressing in
            isPressed = pressing
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityActions {
            Button("Complete Task") {
                onComplete()
            }
            
            if let editAction = onEdit {
                Button("Edit Task") {
                    editAction()
                }
            }
        }
        .onAppear {
            setupCardConfiguration()
        }
        .onChange(of: cognitiveLoad) { _, _ in
            setupCardConfiguration()
        }
    }

    // MARK: - Private Properties

    private var adaptiveSpacing: CGFloat {
        switch cognitiveLoad {
        case .low: return 16
        case .medium: return 14
        case .high: return 12
        case .overload: return 10
        }
    }

    private var adaptivePadding: EdgeInsets {
        let padding = cognitiveLoad == .overload ? 12 : 16
        return EdgeInsets(top: CGFloat(padding), leading: CGFloat(padding), bottom: CGFloat(padding), trailing: CGFloat(padding))
    }

    private var cornerRadius: CGFloat {
        accessibilitySettings.buttonShapes ? 4 : 12
    }

    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(backgroundGradient)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
    }

    private var backgroundGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.neuroNexaBackground,
                Color.neuroNexaBackground.opacity(0.95)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var borderColor: Color {
        switch task.priority {
        case .critical: return Color.neuroNexaError
        case .urgent: return Color.neuroNexaError
        case .high: return Color.neuroNexaWarning
        case .medium: return Color.neuroNexaPrimary.opacity(0.3)
        case .low: return Color.neuroNexaTextSecondary.opacity(0.2)
        }
    }

    private var borderWidth: CGFloat {
        accessibilitySettings.increaseContrast ? 2.0 : 1.0
    }

    private var shadowColor: Color {
        sensoryPreferences.lightSensitivity > 0.7 ? Color.clear : Color.black.opacity(0.1)
    }

    private var shadowRadius: CGFloat {
        sensoryPreferences.lightSensitivity > 0.7 ? 0 : 4
    }

    private var shadowOffset: CGFloat {
        sensoryPreferences.lightSensitivity > 0.7 ? 0 : 2
    }

    private var shouldShowDescription: Bool {
        switch cognitiveLoad {
        case .low: return true
        case .medium: return true
        case .high: return task.description.count < 100
        case .overload: return false
        }
    }

    private var accessibilityLabel: String {
        var label = "Task: \(task.title)"
        if task.isCompleted {
            label += ", Completed"
        } else {
            label += ", Priority: \(task.priority.displayName)"
        }
        return label
    }

    private var accessibilityHint: String {
        if task.isCompleted {
            return "Task is completed"
        } else {
            return "Double tap to view details, or use actions to complete or edit"
        }
    }

    // MARK: - Private Methods

    private func setupCardConfiguration() {
        cachedConfiguration = TaskCardConfiguration(
            task: task,
            cognitiveLoad: cognitiveLoad,
            sensoryPreferences: sensoryPreferences,
            executiveFunctionLevel: executiveFunctionLevel
        )
    }
    
    // MARK: - View Sections
    
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(task.title)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(2)
                
                HStack {
                    Text(task.priority.displayName)
                        .font(.caption)
                        .foregroundColor(priorityColor)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(priorityColor.opacity(0.1))
                        .cornerRadius(4)
                    
                    Spacer()
                    
                    if let dueDate = task.dueDate {
                        Text(dueDate, style: .date)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            if task.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                    .accessibilityLabel("Completed")
            }
        }
    }
    
    private var descriptionSection: some View {
        Text(task.description)
            .font(.body)
            .foregroundColor(.secondary)
            .lineLimit(shouldShowDescription ? 3 : 1)
            .multilineTextAlignment(.leading)
    }
    
    private var bottomSection: some View {
        HStack {
            // Progress indicator
            if !task.isCompleted {
                ProgressView(value: task.progress)
                    .progressViewStyle(LinearProgressViewStyle())
                    .frame(height: 4)
                    .scaleEffect(x: 1, y: 1.5)
            }
            
            Spacer()
            
            // Action buttons
            HStack(spacing: 8) {
                if !task.isCompleted {
                    Button("Complete") {
                        onComplete()
                    }
                    .buttonStyle(.borderedProminent)
                    .font(.caption)
                }
                
                if let editAction = onEdit {
                    Button("Edit") {
                        editAction()
                    }
                    .buttonStyle(.bordered)
                    .font(.caption)
                }
            }
        }
    }
    
    private var priorityColor: Color {
        switch task.priority {
        case .critical: return .red
        case .urgent: return .red
        case .high: return .orange
        case .medium: return .blue
        case .low: return .gray
        }
    }
}

// MARK: - Extensions

@available(iOS 26.0, *)
extension TaskCard {
    static func preview(task: AITask) -> TaskCard {
        TaskCard(
            task: task,
            onTap: { },
            onComplete: { },
            onEdit: { }
        )
    }
}

// MARK: - Preview Provider

#if DEBUG
@available(iOS 26.0, *)
struct TaskCard_Previews: PreviewProvider {
    static var previews: some View {
        let mockTask = AITask(
            title: "Complete Daily Reflection",
            description: "Take a moment to reflect on today's achievements and plan for tomorrow",
            priority: .medium,
            estimatedDuration: 15 * 60, // 15 minutes in seconds
            cognitiveLoad: .medium
        )

        VStack(spacing: 16) {
            TaskCard(
                task: mockTask,
                onTap: { print("Task tapped") },
                onComplete: { print("Task completed") },
                onEdit: { print("Task edited") }
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
        .environment(\.cognitiveLoadLevel, .medium)
        .environment(\.sensoryPreferences, .default)
        .environment(\.accessibilitySettings, .default)
        .environment(\.executiveFunctionLevel, .medium)
    }
}
#endif
