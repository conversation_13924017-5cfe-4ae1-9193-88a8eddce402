import SwiftUI
import UIKit

// MARK: - CognitiveButton Accessibility Support Methods

@available(iOS 18.0, *)
extension CognitiveButton {
    var accessibilityValue: String {
        var components: [String] = []
        
        if shouldShowIcon {
            components.append("Has \(priority.rawValue) priority icon")
        }
        
        if cognitiveLoad == .high || cognitiveLoad == .overload {
            components.append("Optimized for high cognitive load")
        }
        
        return components.isEmpty ? "" : components.joined(separator: ", ")
    }
    
    var accessibilityInputLabel: String {
        // Simplified label for voice control
        title.lowercased().replacingOccurrences(of: " ", with: "")
    }
    
    var shouldShowCognitiveContext: Bool {
        cognitiveLoad == .high || cognitiveLoad == .overload
    }
    
    func getCognitiveHint() -> String {
        switch cognitiveLoad {
        case .low:
            return "Cognitive load is low, full functionality available."
        case .medium:
            return "Cognitive load is moderate, interface adapted for clarity."
        case .high:
            return "Cognitive load is high, interface simplified for easier use."
        case .overload:
            return "Cognitive overload detected, interface optimized for essential actions only."
        }
    }
    
    func announceActivation() {
        let announcement = "\(title) activated. \(getCognitiveHint())"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    func announceDetailedInfo() {
        let details = """
        Button: \(title). 
        Priority: \(priority.rawValue). 
        Style: \(style). 
        Current cognitive load: \(cognitiveLoad.rawValue). 
        Touch target size: \(Int(minimumTouchTarget)) points.
        """
        UIAccessibility.post(notification: .announcement, argument: details)
    }
}

// MARK: - CognitiveButton Computed Properties

@available(iOS 18.0, *)
extension CognitiveButton {
    var adaptiveFont: Font {
        let baseFont = switch style {
        case .primary: NeuroNexaDesignSystem.Typography.headline
        case .secondary: NeuroNexaDesignSystem.Typography.body
        case .tertiary: NeuroNexaDesignSystem.Typography.callout
        case .destructive: NeuroNexaDesignSystem.Typography.headline
        }

        return NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: baseFont,
            cognitiveLoad: cognitiveLoad
        )
    }

    var adaptiveFontWeight: Font.Weight {
        switch cognitiveLoad {
        case .low: .regular
        case .medium: .medium
        case .high: .semibold
        case .overload: .bold
        }
    }

    var adaptivePadding: EdgeInsets {
        let basePadding = switch style {
        case .primary: EdgeInsets(top: 16, leading: 24, bottom: 16, trailing: 24)
        case .secondary: EdgeInsets(top: 12, leading: 20, bottom: 12, trailing: 20)
        case .tertiary: EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16)
        case .destructive: EdgeInsets(top: 16, leading: 24, bottom: 16, trailing: 24)
        }

        let multiplier = NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: 1.0,
            cognitiveLoad: cognitiveLoad
        )

        return EdgeInsets(
            top: basePadding.top * multiplier,
            leading: basePadding.leading * multiplier,
            bottom: basePadding.bottom * multiplier,
            trailing: basePadding.trailing * multiplier
        )
    }

    var adaptiveSpacing: CGFloat {
        NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: NeuroNexaDesignSystem.Spacing.sm,
            cognitiveLoad: cognitiveLoad
        )
    }

    var adaptiveCornerRadius: CGFloat {
        NeuroNexaDesignSystem.CornerRadius.adaptive(for: cognitiveLoad)
    }

    var adaptiveShadow: Shadow {
        let baseShadow = switch style {
        case .primary: NeuroNexaDesignSystem.Shadows.medium
        case .secondary: NeuroNexaDesignSystem.Shadows.soft
        case .tertiary: NeuroNexaDesignSystem.Shadows.subtle
        case .destructive: NeuroNexaDesignSystem.Shadows.medium
        }

        // Apply sensory adaptations to the base shadow
        let adaptiveShadowSettings = NeuroNexaDesignSystem.Shadows.adaptiveShadow(for: sensoryPreferences)

        // Combine base shadow properties with adaptive adjustments
        return Shadow(
            color: baseShadow.color,
            radius: baseShadow.radius * (adaptiveShadowSettings.radius / 4.0), // Normalize radius scaling
            x: baseShadow.x,
            y: baseShadow.y
        )
    }

    var interactionScale: CGFloat {
        if isPressed {
            0.95
        } else if isHovered {
            1.02
        } else {
            1.0
        }
    }

    var minimumTouchTarget: CGFloat {
        // Ensure minimum 44pt touch target for accessibility
        max(44, adaptivePadding.top + adaptivePadding.bottom + 20)
    }

    var shouldShowIcon: Bool {
        // Show icons for high cognitive load or executive function support
        cognitiveLoad == .high || cognitiveLoad == .overload ||
            executiveFunctionLevel == .low
    }

    var priorityIcon: some View {
        switch priority {
        case .low:
            return Image(systemName: "circle")
                .accessibilityLabel("Low priority task - can be done later")
        case .medium:
            return Image(systemName: "circle.fill")
                .accessibilityLabel("Medium priority task - moderate importance")
        case .high:
            return Image(systemName: "exclamationmark.circle.fill")
                .accessibilityLabel("High priority task - needs attention soon")
        case .urgent:
            return Image(systemName: "exclamationmark.triangle.fill")
                .accessibilityLabel("Urgent priority task - requires immediate action")
        }
    }

    var adaptiveIconSize: Font {
        switch cognitiveLoad {
        case .low: .caption
        case .medium: .body
        case .high: .headline
        case .overload: .title3
        }
    }

    var adaptiveAnimation: Animation? {
        let motionSensitivity: MotionSensitivity
        switch sensoryPreferences.motionSensitivity {
        case 0.0..<0.2: motionSensitivity = .none
        case 0.2..<0.4: motionSensitivity = .low
        case 0.4..<0.6: motionSensitivity = .medium
        case 0.6..<0.8: motionSensitivity = .high
        default: motionSensitivity = .extreme
        }
        return NeuroNexaDesignSystem.Animation.adaptive(for: motionSensitivity)
    }
}

// MARK: - Motion Sensitivity Support

enum MotionSensitivity {
    case none, low, medium, high, extreme
}