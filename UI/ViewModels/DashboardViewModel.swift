import Foundation
import SwiftUI

@available(iOS 18.0, *)
@MainActor
class DashboardViewModel: ObservableObject {
    @Published var userName: String = "User"
    @Published var todaysTasks: [AITask] = []
    @Published var currentCognitiveLoad: CognitiveLoadLevel = .medium
    @Published var shouldSuggestBreak: Bool = false
    @Published var recentActivities: [RecentActivity] = []
    @Published var isLoading: Bool = false

    private let userService: UserServiceProtocol
    private let taskService: TaskServiceProtocol
    private let cognitiveLoadService: CognitiveLoadServiceProtocol

    init(
        userService: UserServiceProtocol = UserService(),
        taskService: TaskServiceProtocol = TaskService(),
        cognitiveLoadService: CognitiveLoadServiceProtocol = CognitiveLoadService()
    ) {
        self.userService = userService
        self.taskService = taskService
        self.cognitiveLoadService = cognitiveLoadService
    }

    func loadDashboardData() async {
        isLoading = true

        async let userProfile = loadUserProfile()
        async let tasks = loadTodaysTasks()
        async let cognitiveLoad = loadCognitiveLoad()
        async let activities = loadRecentActivities()

        do {
            let (profile, taskList, load, activityList) = try await (userProfile, tasks, cognitiveLoad, activities)

            self.userName = profile?.name ?? "User"
            self.todaysTasks = taskList
            self.currentCognitiveLoad = load
            self.recentActivities = activityList
            self.shouldSuggestBreak = load == .high
        } catch {
            print("Error loading dashboard data: \(error)")
        }

        isLoading = false
    }

    private func loadUserProfile() async throws -> UserProfile? {
        try await userService.getCurrentUser()
    }

    private func loadTodaysTasks() async throws -> [AITask] {
        let allTasks = try await taskService.getAllTasks()
        let today = Calendar.current.startOfDay(for: Date())
        guard let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today) else {
            return []
        }

        return allTasks.filter { task in
            guard let dueDate = task.dueDate else { return false }
            return dueDate >= today && dueDate < tomorrow
        }
    }

    private func loadCognitiveLoad() async throws -> CognitiveLoadLevel {
        await cognitiveLoadService.getCurrentCognitiveLoad()
    }

    private func loadRecentActivities() async throws -> [RecentActivity] {
        // Mock data for now - replace with actual service call
        [
            RecentActivity(
                id: UUID(),
                title: "Completed breathing exercise",
                icon: "lungs.fill",
                timestamp: Date().addingTimeInterval(-3_600)
            ),
            RecentActivity(
                id: UUID(),
                title: "Finished task: Review documents",
                icon: "checkmark.circle.fill",
                timestamp: Date().addingTimeInterval(-7_200)
            ),
            RecentActivity(
                id: UUID(),
                title: "Took a cognitive break",
                icon: "pause.circle.fill",
                timestamp: Date().addingTimeInterval(-10_800)
            )
        ]
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct RecentActivity: Identifiable {
    let id: UUID
    let title: String
    let icon: String
    let timestamp: Date
}

// MARK: - Mock Service Implementations

@available(iOS 18.0, *)
class CognitiveLoadService: CognitiveLoadServiceProtocol {
    func getCurrentCognitiveLoad() async -> CognitiveLoadLevel {
        // Mock implementation - replace with actual cognitive load detection
        let hour = Calendar.current.component(.hour, from: Date())

        switch hour {
        case 9...11, 14...16:
            return .medium
        case 12...13, 17...19:
            return .high
        default:
            return .low
        }
    }

    func adaptUIForCognitiveLoad(_ level: CognitiveLoadLevel) -> CognitiveAdaptation {
        CognitiveAdaptation(
            reducedAnimations: level == .high,
            simplifiedInterface: level == .high,
            increasedContrast: level == .high,
            largerTouchTargets: level == .high
        )
    }

    func predictCognitiveOverload() async -> Bool {
        let currentLoad = await getCurrentCognitiveLoad()
        return currentLoad == .high
    }

    func suggestCognitiveBreak() async -> BreakSuggestion? {
        let shouldSuggest = await predictCognitiveOverload()

        guard shouldSuggest else { return nil }

        return BreakSuggestion(
            type: .breathing,
            duration: 300, // 5 minutes
            title: "Take a Breathing Break",
            description: "Your cognitive load is high. A short breathing exercise can help you reset and refocus."
        )
    }
}

@available(iOS 18.0, *)
struct CognitiveAdaptation {
    let reducedAnimations: Bool
    let simplifiedInterface: Bool
    let increasedContrast: Bool
    let largerTouchTargets: Bool
}

@available(iOS 18.0, *)
struct BreakSuggestion {
    let type: BreakType
    let duration: TimeInterval
    let title: String
    let description: String
}

@available(iOS 18.0, *)
enum BreakType {
    case breathing
    case movement
    case mindfulness
    case visual
}
