import Foundation
import SwiftUI

@available(iOS 18.0, *)
@MainActor
class AITaskCoachViewModel: ObservableObject {
    @Published var taskInput: String = ""
    @Published var selectedPriority: TaskPriority = .medium
    @Published var generatedTasks: [AITask] = []
    @Published var aiSuggestions: [AISuggestion] = []
    @Published var currentCognitiveState: CognitiveState = .balanced
    @Published var cognitiveInsights: CognitiveInsights?
    @Published var isGenerating: Bool = false

    private let aiTaskCoach: OpenAITaskCoach
    private let userService: UserServiceProtocol
    private let taskService: TaskServiceProtocol

    init(
        aiTaskCoach: OpenAITaskCoach = OpenAITaskCoach(),
        userService: UserServiceProtocol = UserService(),
        taskService: TaskServiceProtocol = TaskService()
    ) {
        self.aiTaskCoach = aiTaskCoach
        self.userService = userService
        self.taskService = taskService
    }

    func loadAISuggestions() async {
        do {
            let userProfile = try await userService.getCurrentUser()
            let suggestions = await generateAISuggestions(for: userProfile)
            self.aiSuggestions = suggestions
            self.currentCognitiveState = await determineCognitiveState(for: userProfile)
        } catch {
            print("Error loading AI suggestions: \(error)")
        }
    }

    func generateTasks() async {
        guard !taskInput.isEmpty else { return }

        isGenerating = true

        do {
            let userProfile = try await userService.getCurrentUser()
            let tasks = try await aiTaskCoach.generatePersonalizedTasks(
                for: taskInput,
                userProfile: userProfile,
                priority: selectedPriority
            )

            self.generatedTasks = tasks
            self.cognitiveInsights = await generateCognitiveInsights(for: tasks, userProfile: userProfile)
        } catch {
            print("Error generating tasks: \(error)")
        }

        isGenerating = false
    }

    func applySuggestion(_ suggestion: AISuggestion) async {
        switch suggestion.type {
        case .breakReminder:
            await suggestCognitiveBreak()
        case .taskBreakdown:
            await breakDownCurrentTask()
        case .cognitiveAdaptation:
            await adaptUIForCognitiveLoad()
        case .focusOptimization:
            await optimizeFocusSettings()
        }
    }

    func updateTask(_ task: AITask) {
        if let index = generatedTasks.firstIndex(where: { $0.id == task.id }) {
            generatedTasks[index] = task
        }
    }

    func saveAllTasks() async {
        do {
            for task in generatedTasks {
                try await taskService.createTask(task)
            }

            // Clear generated tasks after saving
            generatedTasks.removeAll()
            taskInput = ""
            cognitiveInsights = nil
        } catch {
            print("Error saving tasks: \(error)")
        }
    }

    // MARK: - Private Methods

    private func generateAISuggestions(for userProfile: UserProfile?) async -> [AISuggestion] {
        guard let profile = userProfile else {
            return defaultSuggestions()
        }

        var suggestions: [AISuggestion] = []

        // Check cognitive load
        let cognitiveLoad = await aiTaskCoach.getCurrentCognitiveLoad()
        if cognitiveLoad == .high {
            suggestions.append(AISuggestion(
                id: UUID(),
                type: .breakReminder,
                title: "Take a Cognitive Break",
                description: "Your cognitive load is high. Consider a 5-minute breathing exercise.",
                icon: "pause.circle",
                priority: .high
            ))
        }

        // Check for task breakdown opportunities
        let incompleteTasks = try? await taskService.getIncompleteTasks()
        if let tasks = incompleteTasks, tasks.contains(where: { $0.cognitiveLoad == .high }) {
            suggestions.append(AISuggestion(
                id: UUID(),
                type: .taskBreakdown,
                title: "Break Down Complex Tasks",
                description: "You have high cognitive load tasks. Let me help break them down.",
                icon: "list.bullet.rectangle",
                priority: .medium
            ))
        }

        // Neurodiversity-specific suggestions
        if profile.cognitiveProfile.neurodiversityType.contains(.adhd) {
            suggestions.append(AISuggestion(
                id: UUID(),
                type: .focusOptimization,
                title: "Optimize Focus Settings",
                description: "Adjust your environment for better ADHD focus.",
                icon: "target",
                priority: .medium
            ))
        }

        return suggestions
    }

    private func defaultSuggestions() -> [AISuggestion] {
        [
            AISuggestion(
                id: UUID(),
                type: .cognitiveAdaptation,
                title: "Personalize Your Experience",
                description: "Complete your cognitive profile for better task suggestions.",
                icon: "person.crop.circle.badge.plus",
                priority: .low
            )
        ]
    }

    private func determineCognitiveState(for userProfile: UserProfile?) async -> CognitiveState {
        let cognitiveLoad = await aiTaskCoach.getCurrentCognitiveLoad()
        let timeOfDay = Calendar.current.component(.hour, from: Date())

        switch (cognitiveLoad, timeOfDay) {
        case (.high, _):
            return .overwhelmed
        case (.medium, 9...11), (.medium, 14...16):
            return .focused
        case (.low, _):
            return .balanced
        default:
            return .tired
        }
    }

    private func generateCognitiveInsights(for tasks: [AITask], userProfile: UserProfile?) async -> CognitiveInsights {
        var recommendations: [String] = []
        var breakSuggestion: String?

        let totalCognitiveLoad = tasks.reduce(0) { sum, task in
            sum + task.cognitiveLoad.numericValue
        }

        let averageLoad = totalCognitiveLoad / tasks.count

        if averageLoad > 2 {
            recommendations.append("Consider spacing out high cognitive load tasks throughout the day")
            breakSuggestion = "Take a 10-minute break between complex tasks"
        }

        if let profile = userProfile {
            if profile.cognitiveProfile.neurodiversityType.contains(.adhd) {
                recommendations.append("Break tasks into 25-minute focused sessions with 5-minute breaks")
                recommendations.append("Use visual cues and reminders for task transitions")
            }

            if profile.cognitiveProfile.neurodiversityType.contains(.autism) {
                recommendations.append("Maintain consistent routines and clear task structures")
                recommendations.append("Allow extra time for task transitions")
            }
        }

        if tasks.count > 5 {
            recommendations.append("Consider prioritizing 3-5 key tasks to avoid cognitive overload")
        }

        return CognitiveInsights(
            recommendations: recommendations,
            breakSuggestion: breakSuggestion,
            cognitiveLoadScore: averageLoad,
            personalizedTips: generatePersonalizedTips(for: userProfile)
        )
    }

    private func generatePersonalizedTips(for userProfile: UserProfile?) -> [String] {
        guard let profile = userProfile else { return [] }

        var tips: [String] = []

        if profile.cognitiveProfile.attentionSpan == .short {
            tips.append("Use the Pomodoro Technique with 15-minute intervals")
        }

        if profile.cognitiveProfile.processingSpeed == .slow {
            tips.append("Allow 25% extra time for task completion")
        }

        if profile.sensoryPreferences.preferredSoundLevel == .quiet {
            tips.append("Work in a quiet environment or use noise-canceling headphones")
        }

        return tips
    }

    private func suggestCognitiveBreak() async {
        // Implementation for break suggestion
    }

    private func breakDownCurrentTask() async {
        // Implementation for task breakdown
    }

    private func adaptUIForCognitiveLoad() async {
        // Implementation for UI adaptation
    }

    private func optimizeFocusSettings() async {
        // Implementation for focus optimization
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct AISuggestion: Identifiable {
    let id: UUID
    let type: AISuggestionType
    let title: String
    let description: String
    let icon: String
    let priority: SuggestionPriority
}

@available(iOS 18.0, *)
enum AISuggestionType {
    case breakReminder
    case taskBreakdown
    case cognitiveAdaptation
    case focusOptimization
}

@available(iOS 18.0, *)
enum SuggestionPriority {
    case low, medium, high
}

@available(iOS 18.0, *)
enum CognitiveState {
    case balanced
    case focused
    case overwhelmed
    case tired

    var description: String {
        switch self {
        case .balanced:
            return "Balanced & Ready"
        case .focused:
            return "Highly Focused"
        case .overwhelmed:
            return "Feeling Overwhelmed"
        case .tired:
            return "Low Energy"
        }
    }

    var color: Color {
        switch self {
        case .balanced:
            return .green
        case .focused:
            return .blue
        case .overwhelmed:
            return .red
        case .tired:
            return .orange
        }
    }
}

@available(iOS 18.0, *)
struct CognitiveInsights {
    let recommendations: [String]
    let breakSuggestion: String?
    let cognitiveLoadScore: Int
    let personalizedTips: [String]
}

// MARK: - Extensions

@available(iOS 18.0, *)
extension CognitiveLoadLevel {
    var numericValue: Int {
        switch self {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        }
    }
}
