import Foundation
import SwiftUI

@available(iOS 18.0, *)
@MainActor
class BreathingViewModel: ObservableObject {
    @Published var availableExercises: [BreathingExercise] = []
    @Published var selectedExerciseIndex: Int = 0
    @Published var isSessionActive: Bool = false
    @Published var isPaused: Bool = false
    @Published var breathingPhase: BreathingPhase = .inhale
    @Published var phaseCountdown: Int = 0
    @Published var sessionProgress: Double = 0.0
    @Published var elapsedTime: TimeInterval = 0
    @Published var breathingScale: Double = 1.0
    @Published var circleSize: Double = 120
    @Published var showSettings: Bool = false

    // Settings
    @Published var sessionDuration: TimeInterval = 300 // 5 minutes default
    @Published var breathingSpeed: BreathingSpeed = .normal
    @Published var voiceGuidanceEnabled: Bool = false
    @Published var backgroundSoundsEnabled: Bool = false
    @Published var selectedBackgroundSound: String = "none"
    @Published var hapticFeedbackEnabled: Bool = true
    @Published var visualCuesOnly: Bool = false
    @Published var reduceMotion: Bool = false

    private let breathingService: BreathingServiceProtocol
    private var sessionTimer: Timer?
    private var phaseTimer: Timer?
    private var currentSession: BreathingSession?

    var currentExercise: BreathingExercise? {
        guard selectedExerciseIndex < availableExercises.count else { return nil }
        return availableExercises[selectedExerciseIndex]
    }

    var currentPattern: BreathingPattern {
        currentExercise?.pattern ?? .boxBreathing
    }

    var breathingDuration: Double {
        switch breathingSpeed {
        case .slow: return currentPattern.totalDuration * 1.5
        case .normal: return currentPattern.totalDuration
        case .fast: return currentPattern.totalDuration * 0.8
        }
    }

    var controlButtonIcon: String {
        if isSessionActive {
            return isPaused ? "play.fill" : "pause.fill"
        } else {
            return "play.fill"
        }
    }

    var controlButtonText: String {
        if isSessionActive {
            return isPaused ? "Resume" : "Pause"
        } else {
            return "Start Session"
        }
    }

    var controlButtonColor: Color {
        if isSessionActive {
            return isPaused ? .green : .orange
        } else {
            return .neuroNexaPrimary
        }
    }

    init(breathingService: BreathingServiceProtocol = BreathingService()) {
        self.breathingService = breathingService
    }

    func loadBreathingExercises() async {
        do {
            let exercises = try await breathingService.getAvailableExercises()
            self.availableExercises = exercises

            if !exercises.isEmpty && selectedExerciseIndex >= exercises.count {
                selectedExerciseIndex = 0
            }
        } catch {
            print("Error loading breathing exercises: \(error)")
            // Load default exercises
            loadDefaultExercises()
        }
    }

    func startSession() async {
        guard let exercise = currentExercise else { return }

        do {
            let session = try await breathingService.startSession(
                exercise: exercise,
                duration: sessionDuration
            )

            self.currentSession = session
            self.isSessionActive = true
            self.isPaused = false
            self.elapsedTime = 0
            self.sessionProgress = 0

            startSessionTimer()
            startBreathingCycle()
        } catch {
            print("Error starting breathing session: \(error)")
        }
    }

    func pauseSession() {
        guard isSessionActive else { return }

        isPaused = true
        sessionTimer?.invalidate()
        phaseTimer?.invalidate()

        Task {
            if let session = currentSession {
                try? await breathingService.pauseSession(session.id)
            }
        }
    }

    func resumeSession() {
        guard isSessionActive && isPaused else { return }

        isPaused = false
        startSessionTimer()
        startBreathingCycle()

        Task {
            if let session = currentSession {
                try? await breathingService.resumeSession(session.id)
            }
        }
    }

    func stopSession() {
        isSessionActive = false
        isPaused = false
        sessionTimer?.invalidate()
        phaseTimer?.invalidate()

        Task {
            if let session = currentSession {
                try? await breathingService.endSession(session.id)
                self.currentSession = nil
            }
        }

        resetSessionState()
    }

    func extendSession() {
        sessionDuration += 300 // Add 5 minutes
    }

    // MARK: - Private Methods

    private func loadDefaultExercises() {
        availableExercises = [
            BreathingExercise(
                id: UUID(),
                name: "Box Breathing",
                description: "Equal counts for inhale, hold, exhale, hold",
                instructions: "Breathe in for 4 counts, hold for 4, exhale for 4, hold for 4",
                pattern: .boxBreathing,
                duration: 300,
                difficulty: .beginner,
                neurodiversitySupport: [.adhd, .anxiety]
            ),
            BreathingExercise(
                id: UUID(),
                name: "4-7-8 Breathing",
                description: "Calming breath pattern for relaxation",
                instructions: "Inhale for 4, hold for 7, exhale for 8",
                pattern: .fourSevenEight,
                duration: 300,
                difficulty: .intermediate,
                neurodiversitySupport: [.anxiety, .autism]
            ),
            BreathingExercise(
                id: UUID(),
                name: "Simple Breathing",
                description: "Basic in and out breathing",
                instructions: "Breathe in slowly, then breathe out slowly",
                pattern: .simple,
                duration: 300,
                difficulty: .beginner,
                neurodiversitySupport: [.adhd, .autism, .anxiety]
            )
        ]
    }

    private func startSessionTimer() {
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateSessionProgress()
            }
        }
    }

    private func updateSessionProgress() {
        elapsedTime += 1
        sessionProgress = elapsedTime / sessionDuration

        if elapsedTime >= sessionDuration {
            stopSession()
        }
    }

    private func startBreathingCycle() {
        guard !isPaused else { return }

        breathingPhase = .inhale
        startPhase(.inhale)
    }

    private func startPhase(_ phase: BreathingPhase) {
        guard !isPaused else { return }

        breathingPhase = phase
        let phaseDuration = getPhaseDuration(for: phase)
        phaseCountdown = Int(phaseDuration)

        // Update visual animation
        updateBreathingAnimation(for: phase)

        // Start countdown timer
        phaseTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            Task { @MainActor in
                self?.updatePhaseCountdown(timer: timer)
            }
        }
    }

    private func updatePhaseCountdown(timer: Timer) {
        phaseCountdown -= 1

        if phaseCountdown <= 0 {
            timer.invalidate()
            moveToNextPhase()
        }
    }

    private func moveToNextPhase() {
        let nextPhase = getNextPhase()
        startPhase(nextPhase)
    }

    private func getNextPhase() -> BreathingPhase {
        switch breathingPhase {
        case .inhale:
            return currentPattern.hold > 0 ? .hold : .exhale
        case .hold:
            return .exhale
        case .exhale:
            return currentPattern.pause > 0 ? .pause : .inhale
        case .pause:
            return .inhale
        }
    }

    private func getPhaseDuration(for phase: BreathingPhase) -> Double {
        let baseDuration: Double

        switch phase {
        case .inhale:
            baseDuration = currentPattern.inhale
        case .hold:
            baseDuration = currentPattern.hold
        case .exhale:
            baseDuration = currentPattern.exhale
        case .pause:
            baseDuration = currentPattern.pause
        }

        return baseDuration * breathingSpeed.multiplier
    }

    private func updateBreathingAnimation(for phase: BreathingPhase) {
        withAnimation(.easeInOut(duration: getPhaseDuration(for: phase))) {
            switch phase {
            case .inhale:
                breathingScale = 1.3
                circleSize = 160
            case .hold:
                // Maintain current size
                break
            case .exhale:
                breathingScale = 0.8
                circleSize = 100
            case .pause:
                // Maintain current size
                break
            }
        }
    }

    private func resetSessionState() {
        elapsedTime = 0
        sessionProgress = 0
        breathingPhase = .inhale
        phaseCountdown = 0
        breathingScale = 1.0
        circleSize = 120
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
enum BreathingPhase: String, CaseIterable {
    case inhale = "Inhale"
    case hold = "Hold"
    case exhale = "Exhale"
    case pause = "Pause"
}

@available(iOS 18.0, *)
enum BreathingSpeed: CaseIterable {
    case slow, normal, fast

    var multiplier: Double {
        switch self {
        case .slow: return 1.5
        case .normal: return 1.0
        case .fast: return 0.8
        }
    }
}
