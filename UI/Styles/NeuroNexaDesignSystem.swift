import Foundation
import SwiftUI

// MARK: - Missing Types for Build Success
@available(iOS 18.0, *)
public struct CognitiveProgressViewStyle: ProgressViewStyle {
    public func makeBody(configuration: Configuration) -> some View {
        ProgressView(configuration)
            .progressViewStyle(.circular)
            .tint(.blue)
    }

    public init() {}
}

// CognitiveLoadService already exists in Core/Services/CognitiveLoadService.swift

// Import core model types for environment keys
// Note: These types are defined in Core/Models/ and must be accessible
// Import the module containing SensoryPreferences and other core types

// MARK: - NeuroNexa Design System - iOS 26 Enhanced

/// Core design system for neurodiversity-first UI/UX
@available(iOS 18.0, *)
struct NeuroNexaDesignSystem {
    // MARK: - Colors
    struct Colors {
        // Primary Colors - Calming and accessible
        static let primaryBlue = Color(red: 0.2, green: 0.4, blue: 0.8)      // #3366CC
        static let primaryGreen = Color(red: 0.2, green: 0.7, blue: 0.4)     // #33B366
        static let primaryPurple = Color(red: 0.5, green: 0.3, blue: 0.8)    // #804DCC

        // Neutral Palette
        static let neutralGray = Color(red: 0.5, green: 0.5, blue: 0.5)      // #808080
        static let lightGray = Color(red: 0.9, green: 0.9, blue: 0.9)        // #E6E6E6
        static let darkGray = Color(red: 0.2, green: 0.2, blue: 0.2)         // #333333

        // Semantic Colors
        static let successGreen = Color(red: 0.1, green: 0.8, blue: 0.3)     // #1ACC4D
        static let warningOrange = Color(red: 1.0, green: 0.6, blue: 0.0)    // #FF9900
        static let errorRed = Color(red: 0.9, green: 0.2, blue: 0.2)         // #E63333

        // Cognitive Load Adaptive Colors
        static func adaptiveColor(for cognitiveLoad: CognitiveLoadLevel) -> Color {
            switch cognitiveLoad {
            case .low: return primaryBlue.opacity(0.6)
            case .medium: return primaryBlue.opacity(0.8)
            case .high: return primaryBlue
            case .overload: return neutralGray
            }
        }

        // Accessibility Color Contrast
        static func textColor(for contrast: ColorContrast) -> Color {
            switch contrast {
            case .normal: return .primary
            case .increased: return .black
            case .high: return .black
            case .maximum: return .black
            }
        }

        static func backgroundColor(for contrast: ColorContrast) -> Color {
            switch contrast {
            case .normal: return .white
            case .increased: return Color(white: 0.95)
            case .high: return .white
            case .maximum: return .white
            }
        }
    }

    // MARK: - Typography
    struct Typography {
        // Dyslexia-friendly font stack
        static let primaryFont = "SF Pro Display"
        static let readingFont = "OpenDyslexic" // Optional
        static let monoFont = "SF Mono"

        // Size scale (1.25 ratio for clear hierarchy)
        static let title1 = Font.system(size: 32, weight: .bold, design: .default)
        static let title2 = Font.system(size: 26, weight: .semibold, design: .default)
        static let title3 = Font.system(size: 20, weight: .medium, design: .default)
        static let headline = Font.system(size: 18, weight: .semibold, design: .default)
        static let body = Font.system(size: 16, weight: .regular, design: .default)
        static let callout = Font.system(size: 14, weight: .medium, design: .default)
        static let caption = Font.system(size: 12, weight: .regular, design: .default)

        // Cognitive load adaptive sizing
        static func adaptiveFont(base: Font, cognitiveLoad: CognitiveLoadLevel) -> Font {
            // Adjust font weight based on cognitive load for better readability
            switch cognitiveLoad {
            case .low:
                return base.weight(.regular)
            case .medium:
                return base.weight(.medium)
            case .high, .overload:
                return base.weight(.semibold)
            }
        }
    }

    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4      // 4pt
        static let sm: CGFloat = 8      // 8pt
        static let md: CGFloat = 16     // 16pt
        static let lg: CGFloat = 24     // 24pt
        static let xl: CGFloat = 32     // 32pt
        static let xxl: CGFloat = 48    // 48pt

        // Cognitive load adaptive spacing
        static func adaptiveSpacing(base: CGFloat, cognitiveLoad: CognitiveLoadLevel) -> CGFloat {
            let multiplier: CGFloat = switch cognitiveLoad {
            case .low: 0.8
            case .medium: 1.0
            case .high: 1.2
            case .overload: 1.5
            }

            return base * multiplier
        }
    }

    // MARK: - Shadows
    struct Shadows {
        static let subtle = Shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        static let soft = Shadow(color: .black.opacity(0.08), radius: 4, x: 0, y: 2)
        static let medium = Shadow(color: .black.opacity(0.12), radius: 8, x: 0, y: 4)
        static let strong = Shadow(color: .black.opacity(0.16), radius: 12, x: 0, y: 6)

        static func adaptiveShadow(for sensoryPrefs: SensoryPreferences) -> Shadow {
            let intensity = sensoryPrefs.lightSensitivity // Already a Double from 0.0 to 1.0
            return Shadow(
                color: .black.opacity(0.08 * intensity),
                radius: 4,
                x: 0,
                y: 2
            )
        }
    }

    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 4
        static let medium: CGFloat = 8
        static let large: CGFloat = 12
        static let extraLarge: CGFloat = 16
        static let round: CGFloat = 50

        static func adaptive(for cognitiveLoad: CognitiveLoadLevel) -> CGFloat {
            switch cognitiveLoad {
            case .low: return small
            case .medium: return medium
            case .high: return large
            case .overload: return extraLarge
            }
        }
    }

    // MARK: - Animation
    struct Animation {
        static let quick = SwiftUI.Animation.easeInOut(duration: 0.2)
        static let standard = SwiftUI.Animation.easeInOut(duration: 0.3)
        static let slow = SwiftUI.Animation.easeInOut(duration: 0.5)
        static let spring = SwiftUI.Animation.spring(response: 0.4, dampingFraction: 0.8)

        static func adaptive(for motionSensitivity: MotionSensitivity) -> SwiftUI.Animation? {
            switch motionSensitivity {
            case .none: return nil
            case .low: return .easeInOut(duration: 0.1)
            case .medium: return .easeInOut(duration: 0.3)
            case .high: return .easeInOut(duration: 0.5)
            case .extreme: return .easeInOut(duration: 0.8)
            }
        }
    }
}

// MARK: - Environment Keys for Neurodiversity Support

struct CognitiveLoadEnvironmentKey: EnvironmentKey {
    typealias Value = CognitiveLoadLevel
    nonisolated(unsafe) static let defaultValue: CognitiveLoadLevel = .medium
}

struct SensoryPreferencesEnvironmentKey: EnvironmentKey {
    typealias Value = SensoryPreferences
    nonisolated(unsafe) static let defaultValue = SensoryPreferences()
}

struct AccessibilitySettingsEnvironmentKey: EnvironmentKey {
    typealias Value = AccessibilitySettings
    static let defaultValue = AccessibilitySettings()
}

struct ExecutiveFunctionLevelEnvironmentKey: EnvironmentKey {
    typealias Value = ExecutiveFunctionLevel
    static let defaultValue: ExecutiveFunctionLevel = .medium
}

extension EnvironmentValues {
    var cognitiveLoadLevel: CognitiveLoadLevel {
        get { self[CognitiveLoadEnvironmentKey.self] }
        set { self[CognitiveLoadEnvironmentKey.self] = newValue }
    }

    var sensoryPreferences: SensoryPreferences {
        get { self[SensoryPreferencesEnvironmentKey.self] }
        set { self[SensoryPreferencesEnvironmentKey.self] = newValue }
    }

    var accessibilitySettings: AccessibilitySettings {
        get { self[AccessibilitySettingsEnvironmentKey.self] }
        set { self[AccessibilitySettingsEnvironmentKey.self] = newValue }
    }

    var executiveFunctionLevel: ExecutiveFunctionLevel {
        get { self[ExecutiveFunctionLevelEnvironmentKey.self] }
        set { self[ExecutiveFunctionLevelEnvironmentKey.self] = newValue }
    }
}

// MARK: - Color Extensions for compatibility

extension Color {
    static let neuroNexaError = NeuroNexaDesignSystem.Colors.errorRed
    static let neuroNexaWarning = NeuroNexaDesignSystem.Colors.warningOrange
}

// MARK: - Custom Shadow Structure
struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - View Extensions

extension View {
    func neuroNexaShadow(_ shadow: Shadow) -> some View {
        self.shadow(
            color: shadow.color,
            radius: shadow.radius,
            x: shadow.x,
            y: shadow.y
        )
    }
}
