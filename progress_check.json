warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/122)
Linting 'NeuroNexaApp.swift' (2/122)
Linting 'AppDelegate.swift' (3/122)
Linting 'SceneDelegate.swift' (4/122)
Linting 'SettingsViewModel.swift' (5/122)
Linting 'AITaskCoachViewModel.swift' (7/122)
Linting 'BreathingViewModel.swift' (6/122)
Linting 'DashboardViewModel.swift' (8/122)
Linting 'iOS26Extensions.swift' (9/122)
Linting 'NeuroNexaDesignSystem.swift' (10/122)
Linting 'TaskCard+Extensions.swift' (11/122)
Linting 'TaskCard+ViewSections.swift' (12/122)
Linting 'CognitiveProgressViewStyle.swift' (13/122)
Linting 'TaskCardConfiguration.swift' (14/122)
Linting 'CognitiveButton.swift' (15/122)
Linting 'TaskCard.swift' (16/122)
Linting 'UI/Views/BreathingView.swift' (17/122)
Linting 'UI/Views/DashboardView.swift' (18/122)
Linting 'UI/Views/Settings/SettingsView.swift' (19/122)
Linting 'UI/Views/SettingsView.swift' (20/122)
Linting 'UI/Views/Breathing/BreathingView.swift' (21/122)
Linting 'BreathingSupportingViews.swift' (22/122)
Linting 'BreathingHelpers.swift' (23/122)
Linting 'BreathingOverlayViews.swift' (24/122)
Linting 'AnxietyDetectionSheet.swift' (25/122)
Linting 'BreathingContentViews.swift' (26/122)
Linting 'UI/Views/Dashboard/DashboardView.swift' (27/122)
Linting 'AITaskCoachView.swift' (28/122)
Linting 'ContentView.swift' (29/122)
Linting 'UserRepository.swift' (30/122)
Linting 'RoutineRepository.swift' (32/122)
Linting 'TaskRepository.swift' (31/122)
Linting 'UserProfileRepository.swift' (33/122)
Linting 'BreathingSessionRepository.swift' (34/122)
Linting 'NeurodiversityEnums.swift' (35/122)
Linting 'BehaviorModels.swift' (36/122)
Linting 'CognitivePatternModels.swift' (37/122)
Linting 'OpenAIModels.swift' (38/122)
Linting 'SettingsModels.swift' (39/122)
Linting 'NeuroNexaModels.swift' (40/122)
Linting 'TaskEnums.swift' (41/122)
Linting 'BehaviorPredictionModels.swift' (42/122)
Linting 'TaskTimingModels.swift' (43/122)
Linting 'UserPreferences.swift' (44/122)
Linting 'BreathingModels.swift' (45/122)
Linting 'OpenAIUserContextModels.swift' (46/122)
Linting 'ViewPlaceholders.swift' (47/122)
Linting 'NeurodiversityServices.swift' (48/122)
Linting 'OpenAITaskModels.swift' (49/122)
Linting 'AccessibilitySettings.swift' (50/122)
Linting 'ExecutiveFunctionModels.swift' (51/122)
Linting 'SensoryOptimizationModels.swift' (52/122)
Linting 'OpenAIBreakModels.swift' (53/122)
Linting 'OpenAITypes.swift' (54/122)
Linting 'OpenAICoachModels.swift' (55/122)
Linting 'CognitiveOptimizationModels.swift' (56/122)
Linting 'User.swift' (57/122)
Linting 'OpenAICognitiveAdaptationModels.swift' (58/122)
Linting 'SensoryEnums.swift' (59/122)
Linting 'PersonalizedContentTypes.swift' (60/122)
Linting 'CognitivePreferencesModels.swift' (61/122)
Linting 'NeuroNexaServices.swift' (62/122)
Linting 'NeuroNexaTheme.swift' (63/122)
Linting 'NeurodiversityTypes.swift' (64/122)
Linting 'CognitiveSupportingTypes.swift' (65/122)
Linting 'SensoryModels.swift' (66/122)
Linting 'SharedTypes.swift' (67/122)
Linting 'UserProfileModels.swift' (68/122)
Linting 'CognitiveModels.swift' (69/122)
Linting 'OpenAIContentModels.swift' (70/122)
Linting 'BreakSuggestionTypes.swift' (71/122)
Linting 'OpenAITaskCoachModels.swift' (72/122)
Linting 'SensoryCognitiveModels.swift' (73/122)
Linting 'CognitiveAnalysisModels.swift' (74/122)
Linting 'SensoryAdaptationModels.swift' (76/122)
Linting 'CognitiveAdaptationTypes.swift' (75/122)
Linting 'BreathingEnums.swift' (77/122)
Linting 'TaskModels.swift' (78/122)
Linting 'PrivacySettings.swift' (79/122)
Linting 'WellnessEnums.swift' (80/122)
Linting 'BehaviorAnalysisModels.swift' (81/122)
Linting 'BehaviorInsightsModels.swift' (82/122)
Linting 'DependencyContainer.swift' (83/122)
Linting 'ViewModel.swift' (84/122)
Linting 'Coordinator.swift' (85/122)
Linting 'ServiceProtocols.swift' (86/122)
Linting 'CognitiveLoadService.swift' (87/122)
Linting 'CognitiveAnalysisServiceHelpers.swift' (88/122)
Linting 'CognitiveAnalysisServiceExtensions.swift' (89/122)
Linting 'ExecutiveFunctionService.swift' (90/122)
Linting 'CoreDataService.swift' (91/122)
Linting 'UserService.swift' (92/122)
Linting 'BreathingServiceHelpers.swift' (93/122)
Linting 'PersonalizedTaskServiceExtensions.swift' (94/122)
Linting 'SensoryAdaptationService.swift' (95/122)
Linting 'WatchConnectivityService.swift' (96/122)
Linting 'HealthKitService.swift' (97/122)
Linting 'BasicServiceImplementations.swift' (98/122)
Linting 'OpenAIErrors.swift' (99/122)
Linting 'OpenAIService.swift' (100/122)
Linting 'OpenAITaskCoach.swift' (101/122)
Linting 'OpenAITaskCoachParsing.swift' (102/122)
Linting 'OpenAITaskCoachExtensions.swift' (104/122)
Linting 'OpenAITaskCoachPrompts.swift' (105/122)
Linting 'OpenAITaskCoachHelpers.swift' (103/122)
Linting 'CognitiveAnalysisService.swift' (106/122)
Linting 'PersonalizedTaskHelpers.swift' (107/122)
Linting 'CognitiveAnalysisHelpers.swift' (108/122)
Linting 'SettingsService.swift' (109/122)
Linting 'BreathingService.swift' (110/122)
Linting 'PersonalizedTaskService.swift' (111/122)
Linting 'PersonalizedTaskServiceGeneration.swift' (112/122)
Linting 'AuthenticationService.swift' (113/122)
Linting 'CloudKitService.swift' (114/122)
Linting 'BreathingExerciseServiceTests.swift' (116/122)
Linting 'NeuroNexaTests.swift' (115/122)
Linting 'AccessibilityAuditTests.swift' (117/122)
Linting 'NeurodiversityAccessibilityTests.swift' (118/122)
Linting 'AITaskCoachServiceTests.swift' (119/122)
Linting 'BreathingSessionTests.swift' (120/122)
Linting 'AITaskCoachServiceTestsExtensions.swift' (121/122)
Linting 'NeuroNexaUITests.swift' (122/122)
[
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveProgressViewStyle.swift",
    "line" : 118,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton.swift",
    "line" : 8,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 305 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/AITaskCoachView.swift",
    "line" : 604,
    "reason" : "File should contain 500 lines or less: currently contains 604",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Dashboard/DashboardView.swift",
    "line" : 547,
    "reason" : "File should contain 500 lines or less: currently contains 547",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 38,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 125,
    "reason" : "Unused parameter in a closure should be replaced with _",
    "rule_id" : "unused_closure_parameter",
    "severity" : "Warning",
    "type" : "Unused Closure Parameter"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 423,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "File should contain 500 lines or less: currently contains 501",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+ViewSections.swift",
    "line" : 177,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+ViewSections.swift",
    "line" : 7,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+Extensions.swift",
    "line" : 211,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 216,
    "reason" : "Variable name 'n' should be between 2 and 50 characters long",
    "rule_id" : "identifier_name",
    "severity" : "Warning",
    "type" : "Identifier Name"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 227,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 8,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 320,
    "reason" : "`where` clauses are preferred over a single `if` inside a `for`",
    "rule_id" : "for_where",
    "severity" : "Warning",
    "type" : "Prefer For-Where"
  },
  {
    "character" : 37,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 262,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 41,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 264,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  }
]
Done linting! Found 17 violations, 0 serious in 122 files.
