warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/117)
Linting 'NeuroNexaApp.swift' (2/117)
Linting 'AppDelegate.swift' (3/117)
Linting 'SceneDelegate.swift' (4/117)
Linting 'SettingsViewModel.swift' (5/117)
Linting 'BreathingViewModel.swift' (6/117)
Linting 'AITaskCoachViewModel.swift' (7/117)
Linting 'DashboardViewModel.swift' (8/117)
Linting 'iOS26Extensions.swift' (9/117)
Linting 'NeuroNexaDesignSystem.swift' (10/117)
Linting 'CognitiveProgressViewStyle.swift' (11/117)
Linting 'TaskCardConfiguration.swift' (12/117)
Linting 'CognitiveButton.swift' (13/117)
Linting 'TaskCard.swift' (14/117)
Linting 'BreathingHelpers.swift' (15/117)
Linting 'BreathingOverlayViews.swift' (17/117)
Linting 'BreathingSupportingViews.swift' (16/117)
Linting 'AnxietyDetectionSheet.swift' (18/117)
Linting 'BreathingContentViews.swift' (19/117)
Linting 'UI/Views/SettingsView.swift' (21/117)
Linting 'UI/Views/Settings/SettingsView.swift' (22/117)
Linting 'UI/Views/Breathing/BreathingView.swift' (23/117)
Linting 'UI/Views/DashboardView.swift' (20/117)
Linting 'UI/Views/BreathingView.swift' (24/117)
Linting 'UI/Views/Dashboard/DashboardView.swift' (25/117)
Linting 'AITaskCoachView.swift' (26/117)
Linting 'ContentView.swift' (27/117)
Linting 'UserRepository.swift' (28/117)
Linting 'TaskRepository.swift' (29/117)
Linting 'RoutineRepository.swift' (30/117)
Linting 'BreathingSessionRepository.swift' (32/117)
Linting 'UserProfileRepository.swift' (31/117)
Linting 'NeurodiversityEnums.swift' (33/117)
Linting 'BehaviorModels.swift' (34/117)
Linting 'OpenAIModels.swift' (35/117)
Linting 'CognitivePatternModels.swift' (36/117)
Linting 'SettingsModels.swift' (37/117)
Linting 'NeuroNexaModels.swift' (38/117)
Linting 'TaskEnums.swift' (39/117)
Linting 'BehaviorPredictionModels.swift' (40/117)
Linting 'TaskTimingModels.swift' (41/117)
Linting 'UserPreferences.swift' (42/117)
Linting 'BreathingModels.swift' (43/117)
Linting 'OpenAIUserContextModels.swift' (44/117)
Linting 'ViewPlaceholders.swift' (45/117)
Linting 'NeurodiversityServices.swift' (46/117)
Linting 'OpenAITaskModels.swift' (47/117)
Linting 'AccessibilitySettings.swift' (48/117)
Linting 'ExecutiveFunctionModels.swift' (49/117)
Linting 'SensoryOptimizationModels.swift' (50/117)
Linting 'OpenAIBreakModels.swift' (51/117)
Linting 'OpenAITypes.swift' (52/117)
Linting 'OpenAICoachModels.swift' (53/117)
Linting 'User.swift' (54/117)
Linting 'CognitiveOptimizationModels.swift' (55/117)
Linting 'OpenAICognitiveAdaptationModels.swift' (56/117)
Linting 'SensoryEnums.swift' (57/117)
Linting 'PersonalizedContentTypes.swift' (58/117)
Linting 'CognitivePreferencesModels.swift' (59/117)
Linting 'NeuroNexaServices.swift' (60/117)
Linting 'NeuroNexaTheme.swift' (61/117)
Linting 'NeurodiversityTypes.swift' (62/117)
Linting 'CognitiveSupportingTypes.swift' (63/117)
Linting 'SensoryModels.swift' (64/117)
Linting 'SharedTypes.swift' (65/117)
Linting 'UserProfileModels.swift' (66/117)
Linting 'CognitiveModels.swift' (67/117)
Linting 'OpenAIContentModels.swift' (68/117)
Linting 'BreakSuggestionTypes.swift' (69/117)
Linting 'OpenAITaskCoachModels.swift' (70/117)
Linting 'CognitiveAnalysisModels.swift' (71/117)
Linting 'SensoryCognitiveModels.swift' (72/117)
Linting 'CognitiveAdaptationTypes.swift' (73/117)
Linting 'SensoryAdaptationModels.swift' (74/117)
Linting 'BreathingEnums.swift' (75/117)
Linting 'TaskModels.swift' (76/117)
Linting 'PrivacySettings.swift' (77/117)
Linting 'WellnessEnums.swift' (78/117)
Linting 'BehaviorAnalysisModels.swift' (79/117)
Linting 'BehaviorInsightsModels.swift' (80/117)
Linting 'DependencyContainer.swift' (81/117)
Linting 'ViewModel.swift' (82/117)
Linting 'Coordinator.swift' (83/117)
Linting 'ServiceProtocols.swift' (84/117)
Linting 'CognitiveLoadService.swift' (85/117)
Linting 'CognitiveAnalysisServiceExtensions.swift' (86/117)
Linting 'ExecutiveFunctionService.swift' (87/117)
Linting 'CoreDataService.swift' (88/117)
Linting 'UserService.swift' (89/117)
Linting 'PersonalizedTaskServiceExtensions.swift' (90/117)
Linting 'WatchConnectivityService.swift' (91/117)
Linting 'SensoryAdaptationService.swift' (92/117)
Linting 'HealthKitService.swift' (93/117)
Linting 'BasicServiceImplementations.swift' (95/117)
Linting 'OpenAIErrors.swift' (94/117)
Linting 'OpenAIService.swift' (96/117)
Linting 'OpenAITaskCoachParsing.swift' (97/117)
Linting 'OpenAITaskCoach.swift' (98/117)
Linting 'OpenAITaskCoachHelpers.swift' (99/117)
Linting 'OpenAITaskCoachExtensions.swift' (100/117)
Linting 'OpenAITaskCoachPrompts.swift' (101/117)
Linting 'PersonalizedTaskHelpers.swift' (102/117)
Linting 'CognitiveAnalysisService.swift' (103/117)
Linting 'PersonalizedTaskService.swift' (104/117)
Linting 'CognitiveAnalysisHelpers.swift' (105/117)
Linting 'SettingsService.swift' (106/117)
Linting 'BreathingService.swift' (107/117)
Linting 'AuthenticationService.swift' (108/117)
Linting 'CloudKitService.swift' (109/117)
Linting 'NeuroNexaUITests.swift' (110/117)
Linting 'NeuroNexaTests.swift' (111/117)
Linting 'NeurodiversityAccessibilityTests.swift' (112/117)
Linting 'AccessibilityAuditTests.swift' (113/117)
Linting 'AITaskCoachServiceTestsExtensions.swift' (114/117)
Linting 'BreathingExerciseServiceTests.swift' (115/117)
Linting 'BreathingSessionTests.swift' (116/117)
Linting 'AITaskCoachServiceTests.swift' (117/117)
[
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskService.swift",
    "line" : 20,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 359 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 522,
    "reason" : "File should contain 500 lines or less: currently contains 522",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 522,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisService.swift",
    "line" : 20,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 347 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 423,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoachPrompts.swift",
    "line" : 8,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  }
]
Done linting! Found 6 violations, 0 serious in 117 files.
