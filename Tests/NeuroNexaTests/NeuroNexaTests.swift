import XCTest

@testable import NeuroNexa

@available(iOS 18.0, *)
final class NeuroNexaTests: XCTestCase {
    func testAppStateInitialization() {
        let appState = AppState.shared
        XCTAssertNotNil(appState)
        XCTAssertFalse(appState.isAuthenticated)
        XCTAssertEqual(appState.cognitiveLoadLevel, .normal)
    }

    func testDependencyContainerInitialization() {
        let container = DependencyContainer.shared
        XCTAssertNotNil(container)
        XCTAssertNotNil(container.authenticationService)
        XCTAssertNotNil(container.aiTaskCoach)
    }

    func testCognitiveLoadLevels() {
        let levels = CognitiveLoadLevel.allCases
        XCTAssertEqual(levels.count, 3)
        XCTAssertTrue(levels.contains(.low))
        XCTAssertTrue(levels.contains(.normal))
        XCTAssertTrue(levels.contains(.high))
    }
}
