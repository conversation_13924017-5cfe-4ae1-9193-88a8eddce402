import Combine
import HealthKit
@testable import NeuroNexa
import XCTest

// MARK: - Breathing Anxiety Detection and Biometric Tests

/// Unit tests for BreathingExerciseService anxiety detection and biometric integration
@available(iOS 26.0, *)
final class BreathingExerciseServiceTests: XCTestCase {
    // MARK: - Properties
    private var sut: BreathingExerciseService!
    private var mockHealthKitService: MockHealthKitService!
    private var mockWatchConnectivityService: MockWatchConnectivityService!
    private var mockSensoryAdaptationService: MockSensoryAdaptationService!
    private var cancellables: Set<AnyCancellable>!

    // MARK: - Setup & Teardown

    override func setUp() {
        super.setUp()

        mockHealthKitService = MockHealthKitService()
        mockWatchConnectivityService = MockWatchConnectivityService()
        mockSensoryAdaptationService = MockSensoryAdaptationService()
        cancellables = Set<AnyCancellable>()

        sut = BreathingExerciseService(
            healthKitService: mockHealthKitService,
            watchConnectivityService: mockWatchConnectivityService,
            sensoryAdaptationService: mockSensoryAdaptationService
        )
    }

    override func tearDown() {
        cancellables = nil
        sut = nil
        mockSensoryAdaptationService = nil
        mockWatchConnectivityService = nil
        mockHealthKitService = nil
        super.tearDown()
    }

    // MARK: - Anxiety Detection Tests

    func testDetectAnxietyOverwhelm_ElevatedHeartRate_DetectsAnxiety() async {
        // Given
        let userProfile = createMockUserProfile(baselineHeartRate: 70)

        // Simulate elevated heart rate
        mockHealthKitService.simulateHeartRateReading(95.0) // 35% above baseline
        mockHealthKitService.simulateHeartRateReading(98.0)
        mockHealthKitService.simulateHeartRateReading(92.0)

        // When
        let detection = await sut.detectAnxietyOverwhelm(for: userProfile)

        // Then
        XCTAssertGreaterThan(detection.anxietyScore, 0.3, "Should detect elevated anxiety")
        XCTAssertNotEqual(detection.overwhelmLevel, .calm, "Should not be calm with elevated heart rate")
        XCTAssertFalse(detection.recommendations.isEmpty, "Should provide recommendations")
        XCTAssertFalse(detection.suggestedExercises.isEmpty, "Should suggest exercises")
    }

    func testDetectAnxietyOverwhelm_NormalVitals_DetectsCalm() async {
        // Given
        let userProfile = createMockUserProfile(baselineHeartRate: 70)

        // Simulate normal heart rate
        mockHealthKitService.simulateHeartRateReading(72.0)
        mockHealthKitService.simulateHeartRateReading(68.0)
        mockHealthKitService.simulateHeartRateReading(71.0)

        // When
        let detection = await sut.detectAnxietyOverwhelm(for: userProfile)

        // Then
        XCTAssertLessThan(detection.anxietyScore, 0.3, "Should detect low anxiety")
        XCTAssertEqual(detection.overwhelmLevel, .calm, "Should be calm with normal vitals")
    }

    func testDetectAnxietyOverwhelm_SevereAnxiety_ReturnsEmergencyRecommendations() async {
        // Given
        let userProfile = createMockUserProfile(baselineHeartRate: 70)

        // Simulate very elevated heart rate and low HRV
        mockHealthKitService.simulateHeartRateReading(120.0) // 71% above baseline
        mockHealthKitService.simulateHeartRateReading(125.0)
        mockHealthKitService.simulateHRVReading(15.0) // Very low HRV

        // When
        let detection = await sut.detectAnxietyOverwhelm(for: userProfile)

        // Then
        XCTAssertEqual(detection.overwhelmLevel, .severe, "Should detect severe overwhelm")
        XCTAssertTrue(detection.recommendations.contains { $0.contains("immediate action") },
                      "Should include emergency recommendations")
        XCTAssertTrue(detection.suggestedExercises.allSatisfy { $0.duration <= 180 },
                      "Severe anxiety exercises should be 3 minutes or less")
    }

    // MARK: - Biometric Integration Tests

    func testHeartRateUpdates_DuringSession_UpdatesCurrentData() async throws {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()
        do {
            try await sut.startBreathingSession(exercise, userProfile: userProfile)
        } catch {
            XCTFail("Failed to start breathing session: \(error)")
            return
        }

        let expectation = XCTestExpectation(description: "Heart rate updates")
        var receivedHeartRates: [Double] = []

        sut.$heartRateData
            .sink { heartRateData in
                if !heartRateData.isEmpty, let lastHeartRate = heartRateData.last {
                    receivedHeartRates.append(lastHeartRate.value)
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        // When
        mockHealthKitService.simulateHeartRateReading(75.0)

        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        XCTAssertEqual(receivedHeartRates.last, 75.0, "Should receive heart rate update")
    }

    func testHRVUpdates_DuringSession_UpdatesCurrentData() async throws {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()
        do {
            try await sut.startBreathingSession(exercise, userProfile: userProfile)
        } catch {
            XCTFail("Failed to start breathing session: \(error)")
            return
        }

        let expectation = XCTestExpectation(description: "HRV updates")
        var receivedHRV: [Double] = []

        sut.$hrvData
            .sink { hrvData in
                if !hrvData.isEmpty, let lastHRV = hrvData.last {
                    receivedHRV.append(lastHRV.value)
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        // When
        mockHealthKitService.simulateHRVReading(45.0)

        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        XCTAssertEqual(receivedHRV.last, 45.0, "Should receive HRV update")
    }

    // MARK: - Sensory Adaptation Tests

    func testStartBreathingSession_HighSensoryUser_AdaptsExercise() async throws {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile(
            sensoryPreferences: SensoryPreferences(
                motionSensitivity: .high,
                colorContrast: .high,
                soundSensitivity: .high
            )
        )

        // When
        try await sut.startBreathingSession(exercise, userProfile: userProfile)

        // Then
        XCTAssertTrue(mockSensoryAdaptationService.adaptBreathingExerciseCalled,
                      "Should adapt exercise for sensory preferences")
        XCTAssertEqual(mockSensoryAdaptationService.lastSensoryPreferences?.motionSensitivity,
                       .high, "Should pass correct sensory preferences")
    }

    // MARK: - Error Handling Tests

    func testStartBreathingSession_HealthKitError_ThrowsError() async {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()
        mockHealthKitService.shouldThrowError = true

        // When/Then
        do {
            try await sut.startBreathingSession(exercise, userProfile: userProfile)
            XCTFail("Should throw error when HealthKit fails")
        } catch {
            XCTAssertTrue(error is HealthKitError, "Should throw HealthKitError")
        }
    }

    // MARK: - Helper Methods

    private func createMockUserProfile(
        neurodiversityTypes: [NeurodiversityType] = [.adhd],
        baselineHeartRate: Double = 70,
        sensoryPreferences: SensoryPreferences = .default
    ) -> UserProfile {
        UserProfile(
            id: UUID(),
            email: "<EMAIL>",
            cognitiveProfile: CognitiveProfile(
                neurodiversityType: neurodiversityTypes,
                cognitiveStrengths: [.visualProcessing],
                challenges: [.timeManagement],
                sensoryPreferences: sensoryPreferences
            ),
            executiveFunctionLevel: .medium,
            baselineHeartRate: baselineHeartRate
        )
    }

    private func createMockBreathingExercise() -> BreathingExercise {
        BreathingExercise(
            name: "Test Breathing",
            description: "Test breathing exercise",
            pattern: BreathingPattern(
                name: "Test Pattern",
                description: "Test pattern",
                inhaleTime: 4,
                holdTime: 4,
                exhaleTime: 4,
                restTime: 0,
                defaultDuration: 300,
                difficulty: .beginner,
                benefits: ["Test benefit"],
                suitableFor: [.adhd, .anxiety],
                neurodiversitySupport: [.adhd, .anxiety],
                isGeneral: true
            ),
            duration: 300,
            difficulty: .beginner,
            benefits: ["Test benefit"],
            neurodiversitySupport: [.adhd, .anxiety]
        )
    }
}

// MARK: - Mock Services

class MockHealthKitService: HealthKitServiceProtocol {
    var startHeartRateMonitoringCalled = false
    var startHRVMonitoringCalled = false
    var stopHeartRateMonitoringCalled = false
    var stopHRVMonitoringCalled = false
    var shouldThrowError = false

    private let heartRateSubject = PassthroughSubject<HeartRateReading, Never>()
    private let hrvSubject = PassthroughSubject<HRVReading, Never>()

    var heartRateUpdates: AnyPublisher<HeartRateReading, Never> {
        heartRateSubject.eraseToAnyPublisher()
    }

    var hrvUpdates: AnyPublisher<HRVReading, Never> {
        hrvSubject.eraseToAnyPublisher()
    }

    func startHeartRateMonitoring() async throws {
        if shouldThrowError {
            throw HealthKitError.authorizationDenied
        }
        startHeartRateMonitoringCalled = true
    }

    func startHRVMonitoring() async throws {
        if shouldThrowError {
            throw HealthKitError.authorizationDenied
        }
        startHRVMonitoringCalled = true
    }

    func stopHeartRateMonitoring() async {
        stopHeartRateMonitoringCalled = true
    }

    func stopHRVMonitoring() async {
        stopHRVMonitoringCalled = true
    }

    func saveBreathingSession(_ result: BreathingSessionResult) async throws {
        // Mock implementation
    }

    func simulateHeartRateReading(_ value: Double) {
        let reading = HeartRateReading(value: value, timestamp: Date())
        heartRateSubject.send(reading)
    }

    func simulateHRVReading(_ value: Double) {
        let reading = HRVReading(value: value, timestamp: Date())
        hrvSubject.send(reading)
    }
}

class MockWatchConnectivityService: WatchConnectivityServiceProtocol {
    var mockIsConnected = false
    var sendMessageCalled = false
    var lastMessage: [String: Any]?

    private let isConnectedSubject = CurrentValueSubject<Bool, Never>(false)

    var isConnected: AnyPublisher<Bool, Never> {
        isConnectedSubject.eraseToAnyPublisher()
    }

    func sendMessage(_ message: [String: Any]) async {
        sendMessageCalled = true
        lastMessage = message
    }

    init() {
        isConnectedSubject.send(mockIsConnected)
    }
}

class MockSensoryAdaptationService: SensoryAdaptationServiceProtocol {
    var adaptBreathingExerciseCalled = false
    var lastSensoryPreferences: SensoryPreferences?

    func adaptBreathingExercise(
        _ exercise: BreathingExercise,
        for preferences: SensoryPreferences
    ) async -> SensoryAdaptations {
        adaptBreathingExerciseCalled = true
        lastSensoryPreferences = preferences

        return SensoryAdaptations(
            visualCues: VisualCues.default,
            audioGuidance: AudioGuidance.default,
            hapticFeedback: HapticFeedback.default
        )
    }
}

// MARK: - Supporting Types

enum HealthKitError: Error {
    case authorizationDenied
    case dataUnavailable
}

struct HeartRateReading {
    let value: Double
    let timestamp: Date
}

struct HRVReading {
    let value: Double
    let timestamp: Date
}

extension BreathingPattern {
    var isStructured: Bool {
        holdTime > 0 && restTime >= 0
    }
}
