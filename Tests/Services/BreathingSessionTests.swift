import Combine
import HealthKit
@testable import NeuroNexa
import XCTest

// MARK: - Breathing Session Management Tests

/// Unit tests for BreathingExerciseService session management and personalization
@available(iOS 26.0, *)
final class BreathingSessionTests: XCTestCase {
    // MARK: - Properties
    private var sut: BreathingExerciseService!
    private var mockHealthKitService: MockHealthKitService!
    private var mockWatchConnectivityService: MockWatchConnectivityService!
    private var mockSensoryAdaptationService: MockSensoryAdaptationService!
    private var cancellables: Set<AnyCancellable>!

    // MARK: - Setup & Teardown

    override func setUp() {
        super.setUp()

        mockHealthKitService = MockHealthKitService()
        mockWatchConnectivityService = MockWatchConnectivityService()
        mockSensoryAdaptationService = MockSensoryAdaptationService()
        cancellables = Set<AnyCancellable>()

        sut = BreathingExerciseService(
            healthKitService: mockHealthKitService,
            watchConnectivityService: mockWatchConnectivityService,
            sensoryAdaptationService: mockSensoryAdaptationService
        )
    }

    override func tearDown() {
        cancellables = nil
        sut = nil
        mockSensoryAdaptationService = nil
        mockWatchConnectivityService = nil
        mockHealthKitService = nil
        super.tearDown()
    }

    // MARK: - Session Management Tests

    func testStartBreathingSession_ValidExercise_StartsSession() async throws {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()

        // When
        try await sut.startBreathingSession(exercise, userProfile: userProfile)

        // Then
        XCTAssertTrue(sut.isSessionActive, "Session should be active")
        XCTAssertEqual(sut.currentExercise?.name, exercise.name, "Current exercise should be set")
        XCTAssertTrue(mockHealthKitService.startHeartRateMonitoringCalled,
                      "Should start heart rate monitoring")
        XCTAssertTrue(mockHealthKitService.startHRVMonitoringCalled,
                      "Should start HRV monitoring")
    }

    func testStartBreathingSession_SessionAlreadyActive_ThrowsError() async {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()

        // Start first session
        do {
            try await sut.startBreathingSession(exercise, userProfile: userProfile)
        } catch {
            XCTFail("Failed to start initial breathing session: \(error)")
            return
        }

        // When/Then
        do {
            try await sut.startBreathingSession(exercise, userProfile: userProfile)
            XCTFail("Should throw error when session already active")
        } catch BreathingServiceError.sessionAlreadyActive {
            // Expected error
        } catch {
            XCTFail("Should throw sessionAlreadyActive error, got \(error)")
        }
    }

    func testStartBreathingSession_WithWatchConnected_StartsWatchSession() async throws {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()
        mockWatchConnectivityService.mockIsConnected = true

        // When
        try await sut.startBreathingSession(exercise, userProfile: userProfile)

        // Then
        XCTAssertTrue(mockWatchConnectivityService.sendMessageCalled,
                      "Should send message to watch")
        XCTAssertEqual(mockWatchConnectivityService.lastMessage?["action"] as? String,
                       "startBreathing", "Should send start breathing message")
    }

    func testPauseSession_ActiveSession_PausesSession() async {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()
        do {
            try await sut.startBreathingSession(exercise, userProfile: userProfile)
        } catch {
            XCTFail("Failed to start breathing session: \(error)")
            return
        }

        // When
        await sut.pauseSession()

        // Then
        // Note: We can't directly test isPaused as it's internal to the session
        // But we can verify watch communication
        if mockWatchConnectivityService.mockIsConnected {
            XCTAssertTrue(mockWatchConnectivityService.sendMessageCalled)
        }
    }

    func testEndSession_ActiveSession_ReturnsResults() async throws {
        // Given
        let exercise = createMockBreathingExercise()
        let userProfile = createMockUserProfile()
        do {
            try await sut.startBreathingSession(exercise, userProfile: userProfile)
        } catch {
            XCTFail("Failed to start breathing session: \(error)")
            return
        }

        // Add some mock heart rate data
        mockHealthKitService.simulateHeartRateReading(75.0)
        mockHealthKitService.simulateHeartRateReading(70.0)

        // When
        let result = await sut.endSession()

        // Then
        XCTAssertFalse(sut.isSessionActive, "Session should no longer be active")
        XCTAssertNil(sut.currentExercise, "Current exercise should be cleared")
        XCTAssertEqual(result.exercise.name, exercise.name, "Result should contain correct exercise")
        XCTAssertGreaterThan(result.duration, 0, "Result should have positive duration")
        XCTAssertTrue(mockHealthKitService.stopHeartRateMonitoringCalled,
                      "Should stop heart rate monitoring")
    }

    // MARK: - Exercise Personalization Tests

    func testGetPersonalizedExercises_ADHDUser_ReturnsADHDSuitableExercises() async {
        // Given
        let userProfile = createMockUserProfile(neurodiversityTypes: [.adhd])

        // When
        let exercises = await sut.getPersonalizedExercises(for: userProfile)

        // Then
        XCTAssertFalse(exercises.isEmpty, "Should return exercises for ADHD user")
        XCTAssertTrue(exercises.allSatisfy { $0.neurodiversitySupport.contains(.adhd) },
                      "All exercises should support ADHD")
        XCTAssertTrue(exercises.allSatisfy { $0.duration <= 600 },
                      "ADHD exercises should be 10 minutes or less")
    }

    func testGetPersonalizedExercises_AutismUser_ReturnsStructuredExercises() async {
        // Given
        let userProfile = createMockUserProfile(neurodiversityTypes: [.autism])

        // When
        let exercises = await sut.getPersonalizedExercises(for: userProfile)

        // Then
        XCTAssertFalse(exercises.isEmpty, "Should return exercises for autism user")
        XCTAssertTrue(exercises.allSatisfy { $0.neurodiversitySupport.contains(.autism) },
                      "All exercises should support autism")
        XCTAssertTrue(exercises.allSatisfy { $0.pattern.isStructured },
                      "Autism exercises should have structured patterns")
    }

    func testGetPersonalizedExercises_AnxietyUser_ReturnsGentleExercises() async {
        // Given
        let userProfile = createMockUserProfile(neurodiversityTypes: [.anxiety])

        // When
        let exercises = await sut.getPersonalizedExercises(for: userProfile)

        // Then
        XCTAssertFalse(exercises.isEmpty, "Should return exercises for anxiety user")
        XCTAssertTrue(exercises.allSatisfy { $0.neurodiversitySupport.contains(.anxiety) },
                      "All exercises should support anxiety")
        XCTAssertTrue(exercises.allSatisfy { $0.difficulty == .beginner || $0.difficulty == .intermediate },
                      "Anxiety exercises should not be advanced")
    }

    // MARK: - Helper Methods

    private func createMockUserProfile(
        neurodiversityTypes: [NeurodiversityType] = [.adhd],
        baselineHeartRate: Double = 70,
        sensoryPreferences: SensoryPreferences = .default
    ) -> UserProfile {
        UserProfile(
            id: UUID(),
            name: "Test User",
            neurodiversityTypes: neurodiversityTypes,
            cognitiveProfile: CognitiveProfile.default,
            sensoryPreferences: sensoryPreferences,
            baselineHeartRate: baselineHeartRate
        )
    }

    private func createMockBreathingExercise() -> BreathingExercise {
        BreathingExercise(
            name: "Test Exercise",
            type: .boxBreathing,
            duration: 300,
            pattern: BreathingPattern.boxBreathing,
            difficulty: .beginner,
            neurodiversitySupport: [.adhd, .anxiety]
        )
    }
}
