@testable import NeuroNexa
import XCTest

// MARK: - AI Task Coach Service Tests Extensions
// Additional test methods for AITaskCoachService

@available(iOS 18.0, *)
extension AITaskCoachServiceTests {
    // MARK: - Additional Test Methods
    
    func testTaskCoachInitialization() {
        XCTAssertNotNil(taskCoach)
        XCTAssertFalse(taskCoach.isInitialized)
    }
    
    func testTaskCoachAvailability() async {
        let isAvailable = await taskCoach.isAvailable()
        XCTAssertTrue(isAvailable)
    }
    
    func testCognitiveLoadAdaptation() {
        let lowLoadAdaptation = taskCoach.adaptUIForCognitiveLoad(.low)
        XCTAssertEqual(lowLoadAdaptation.uiComplexity, .minimal)
        
        let highLoadAdaptation = taskCoach.adaptUIForCognitiveLoad(.high)
        XCTAssertEqual(highLoadAdaptation.uiComplexity, .simplified)
    }
    
    func testBreakSuggestionGeneration() async {
        let breakSuggestion = await taskCoach.suggestCognitiveBreak()
        XCTAssertNotNil(breakSuggestion)
        XCTAssertEqual(breakSuggestion?.type, .cognitive)
        XCTAssertGreaterThan(breakSuggestion?.duration ?? 0, 0)
    }
    
    func testPersonalizedContentGeneration() async throws {
        let context = createTestUserContext()
        let content = try await taskCoach.generatePersonalizedContent(for: context)
        
        XCTAssertNotNil(content)
        XCTAssertFalse(content.content.isEmpty)
        XCTAssertEqual(content.personalizedFor, context.userProfile.id)
    }
    
    func testBehaviorAnalysis() async throws {
        let behaviorData = createTestBehaviorData()
        let insights = try await taskCoach.analyzeUserBehavior(behaviorData)
        
        XCTAssertNotNil(insights)
        XCTAssertEqual(insights.userId, behaviorData.userId)
        XCTAssertFalse(insights.patterns.isEmpty)
        XCTAssertGreaterThan(insights.confidenceScore, 0.0)
    }
    
    func testUserNeedsPrediction() async throws {
        let context = createTestUserContext()
        let needs = try await taskCoach.predictUserNeeds(context)
        
        XCTAssertNotNil(needs)
        XCTAssertFalse(needs.isEmpty)
        
        let highPriorityNeeds = needs.filter { $0.priority == .high }
        XCTAssertFalse(highPriorityNeeds.isEmpty)
    }
    
    // MARK: - Helper Methods
    
    private func createTestUserContext() -> UserContext {
        let userProfile = UserProfile(
            id: UUID(),
            name: "Test User",
            cognitiveProfile: CognitiveProfile(
                neurodiversityType: [.adhd, .autism],
                cognitiveStrengths: [.visualProcessing, .patternRecognition],
                supportNeeds: [.executiveFunction, .sensoryRegulation]
            )
        )
        
        return UserContext(
            userProfile: userProfile,
            currentCognitiveLoad: 0.6,
            currentCognitiveLoadLevel: .medium,
            energyLevel: 7,
            focusDuration: 25,
            environment: "home_office",
            timeOfDay: "morning",
            recentTaskCompletions: [],
            recentActivities: ["coding", "reading"]
        )
    }
    
    private func createTestBehaviorData() -> UserBehaviorData {
        UserBehaviorData(
            userId: UUID(),
            taskCompletions: [
                TaskCompletion(taskId: UUID(), completedAt: Date(), duration: 1_800),
                TaskCompletion(taskId: UUID(), completedAt: Date().addingTimeInterval(-3_600), duration: 2_400)
            ],
            averageSessionLength: 30.0,
            cognitiveLoadHistory: [0.3, 0.5, 0.7, 0.4, 0.6],
            productivityPatterns: [],
            breakPatterns: []
        )
    }
}

// MARK: - Supporting Test Types

struct TaskCompletion {
    let taskId: UUID
    let completedAt: Date
    let duration: TimeInterval
}

struct UserBehaviorData {
    let userId: UUID
    let taskCompletions: [TaskCompletion]
    let averageSessionLength: Double
    let cognitiveLoadHistory: [Double]
    let productivityPatterns: [String]
    let breakPatterns: [String]
}

struct UserContext {
    let userProfile: UserProfile
    let currentCognitiveLoad: Double
    let currentCognitiveLoadLevel: CognitiveLoadLevel
    let energyLevel: Int
    let focusDuration: Int
    let environment: String
    let timeOfDay: String
    let recentTaskCompletions: [TaskCompletion]
    let recentActivities: [String]
}

struct UserProfile {
    let id: UUID
    let name: String
    let cognitiveProfile: CognitiveProfile
}

struct CognitiveProfile {
    let neurodiversityType: [NeurodiversityType]
    let cognitiveStrengths: [CognitiveStrength]
    let supportNeeds: [SupportNeed]
}

enum NeurodiversityType: String {
    case adhd, autism, dyslexia, anxiety
}

enum CognitiveStrength: String {
    case visualProcessing, patternRecognition, creativity, systemicThinking
}

enum SupportNeed: String {
    case executiveFunction, sensoryRegulation, socialCommunication, timeManagement
}
