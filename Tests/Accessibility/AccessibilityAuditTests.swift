import AccessibilityAudit
import SwiftUI
import XCTest

@testable import NeuroNexa

// MARK: - Accessibility Audit Tests

/// Comprehensive accessibility audit tests for NeuroNexa
/// Ensures WCAG 2.1 AA compliance and neurodiversity-specific accessibility
@available(iOS 18.0, *)
final class AccessibilityAuditTests: XCTestCase {
    // MARK: - Properties
    private var mockUserRepository: MockUserRepository!
    private var mockAITaskCoachService: MockAITaskCoachService!
    private var mockBreathingService: MockBreathingExerciseService!

    // MARK: - Setup & Teardown

    override func setUp() {
        super.setUp()
        mockUserRepository = MockUserRepository()
        mockAITaskCoachService = MockAITaskCoachService()
        mockBreathingService = MockBreathingExerciseService()
    }

    override func tearDown() {
        mockBreathingService = nil
        mockAITaskCoachService = nil
        mockUserRepository = nil
        super.tearDown()
    }

    // MARK: - AI Task Coach Accessibility Tests

    func testAITaskCoachView_AccessibilityLabels_ArePresent() {
        // Given
        let view = AITaskCoachView()

        // When
        let hostingController = UIHostingController(rootView: view)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasAccessibilityLabel("AI Task Coach"),
                      "Main view should have accessibility label")
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Generate new tasks"),
                      "Generate button should have accessibility label")
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Task list"),
                      "Task list should have accessibility label")
    }

    func testAITaskCoachView_AccessibilityHints_AreDescriptive() {
        // Given
        let view = AITaskCoachView()

        // When
        let hostingController = UIHostingController(rootView: view)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasAccessibilityHint("Generates personalized tasks based on your cognitive profile"),
                      "Generate button should have descriptive hint")
        XCTAssertTrue(snapshot.hasAccessibilityHint("Swipe to complete or modify tasks"),
                      "Task list should have usage hint")
    }

    func testAITaskCoachView_VoiceOverNavigation_IsLogical() {
        // Given
        let view = AITaskCoachView()

        // When
        let hostingController = UIHostingController(rootView: view)
        let voiceOverElements = hostingController.view.accessibilityElements(in: .voiceOver)

        // Then
        XCTAssertGreaterThan(voiceOverElements.count, 0, "Should have VoiceOver elements")

        // Verify logical reading order
        let firstElement = voiceOverElements.first as? UIAccessibilityElement
        XCTAssertTrue(firstElement?.accessibilityLabel?.contains("AI Task Coach") ?? false,
                      "First element should be the main title")
    }

    func testTaskCard_AccessibilityTraits_AreCorrect() {
        // Given
        let task = createMockAITask()
        let taskCard = TaskCard(
            task: task,
            onComplete: {},
            onEdit: {}
        )

        // When
        let hostingController = UIHostingController(rootView: taskCard)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasAccessibilityTrait(.button),
                      "Task card should be accessible as button")
        XCTAssertTrue(snapshot.hasAccessibilityLabel(task.title),
                      "Task card should have task title as label")
        XCTAssertTrue(snapshot.hasAccessibilityValue("Not completed"),
                      "Task card should indicate completion status")
    }

    func testCognitiveButton_AccessibilityStates_AreCorrect() {
        // Given
        let button = CognitiveButton(
            "Test Button",
            style: .primary,
            priority: .high,
            systemImage: "plus"
        ) {}

        // When
        let hostingController = UIHostingController(rootView: button)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Test Button"),
                      "Button should have correct label")
        XCTAssertTrue(snapshot.hasAccessibilityTrait(.button),
                      "Should have button trait")
        XCTAssertFalse(snapshot.hasAccessibilityTrait(.notEnabled),
                       "Should be enabled by default")
    }

    // MARK: - Breathing View Accessibility Tests

    func testBreathingView_AccessibilityLabels_ArePresent() {
        // Given
        let viewModel = BreathingViewModel(
            breathingService: mockBreathingService,
            userRepository: mockUserRepository
        )
        let view = BreathingView(viewModel: viewModel)

        // When
        let hostingController = UIHostingController(rootView: view)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Breathing & Calm"),
                      "Main view should have accessibility label")
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Start breathing exercise"),
                      "Start button should have accessibility label")
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Breathing visualization"),
                      "Breathing circle should have accessibility label")
    }

    func testBreathingView_BreathingPhases_HaveAudioDescriptions() {
        // Given
        let viewModel = BreathingViewModel(
            breathingService: mockBreathingService,
            userRepository: mockUserRepository
        )
        let view = BreathingView(viewModel: viewModel)

        // When
        let hostingController = UIHostingController(rootView: view)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasAccessibilityValue("Breathe in slowly"),
                      "Inhale phase should have audio description")
        XCTAssertTrue(snapshot.hasAccessibilityHint("Follow the breathing pattern for relaxation"),
                      "Breathing visualization should have helpful hint")
    }

    func testBreathingView_BiometricData_IsAccessible() {
        // Given
        let viewModel = BreathingViewModel(
            breathingService: mockBreathingService,
            userRepository: mockUserRepository
        )
        let view = BreathingView(viewModel: viewModel)

        // When
        let hostingController = UIHostingController(rootView: view)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Heart rate: 72 beats per minute"),
                      "Heart rate should be accessible")
        XCTAssertTrue(snapshot.hasAccessibilityLabel("Heart rate variability: 45 milliseconds"),
                      "HRV should be accessible")
    }

    // MARK: - Color Contrast Tests

    func testDesignSystem_ColorContrast_MeetsWCAGAA() {
        // Given
        let colors = NeuroNexaDesignSystem.Colors.self

        // Test primary colors against backgrounds
        let primaryOnLight = colors.primary.contrastRatio(with: colors.background)
        let primaryOnDark = colors.primary.contrastRatio(with: colors.backgroundDark)

        // Then
        XCTAssertGreaterThanOrEqual(primaryOnLight, 4.5,
                                    "Primary color should meet WCAG AA contrast ratio on light background")
        XCTAssertGreaterThanOrEqual(primaryOnDark, 4.5,
                                    "Primary color should meet WCAG AA contrast ratio on dark background")

        // Test secondary colors
        let secondaryOnLight = colors.secondary.contrastRatio(with: colors.background)
        XCTAssertGreaterThanOrEqual(secondaryOnLight, 3.0,
                                    "Secondary color should meet minimum contrast ratio")

        // Test error colors (should meet AAA for critical information)
        let errorOnLight = colors.error.contrastRatio(with: colors.background)
        XCTAssertGreaterThanOrEqual(errorOnLight, 7.0,
                                    "Error color should meet WCAG AAA contrast ratio")
    }

    func testDesignSystem_HighContrastMode_IncreasesContrast() {
        // Given
        let normalContrast = NeuroNexaDesignSystem.Colors.primary
        let highContrast = NeuroNexaDesignSystem.Colors.primary(for: .high)
        let background = NeuroNexaDesignSystem.Colors.background

        // When
        let normalRatio = normalContrast.contrastRatio(with: background)
        let highRatio = highContrast.contrastRatio(with: background)

        // Then
        XCTAssertGreaterThan(highRatio, normalRatio,
                             "High contrast mode should increase contrast ratio")
        XCTAssertGreaterThanOrEqual(highRatio, 7.0,
                                    "High contrast should meet WCAG AAA standards")
    }

    // MARK: - Typography Accessibility Tests

    func testDesignSystem_Typography_SupportsScaling() {
        // Given
        let baseFont = NeuroNexaDesignSystem.Typography.body
        let largeFont = NeuroNexaDesignSystem.Typography.body.scaled(for: .accessibilityExtraExtraExtraLarge)

        // When
        let baseSize = baseFont.pointSize
        let largeSize = largeFont.pointSize

        // Then
        XCTAssertGreaterThan(largeSize, baseSize,
                             "Font should scale up for accessibility sizes")
        XCTAssertGreaterThanOrEqual(largeSize / baseSize, 2.0,
                                    "Should support at least 200% scaling")
    }

    func testDesignSystem_Typography_MeetsMinimumSizes() {
        // Given
        let captionFont = NeuroNexaDesignSystem.Typography.caption
        let bodyFont = NeuroNexaDesignSystem.Typography.body

        // Then
        XCTAssertGreaterThanOrEqual(captionFont.pointSize, 12.0,
                                    "Caption text should be at least 12pt")
        XCTAssertGreaterThanOrEqual(bodyFont.pointSize, 16.0,
                                    "Body text should be at least 16pt")
    }

    // MARK: - Touch Target Tests

    func testCognitiveButton_TouchTarget_MeetsMinimumSize() {
        // Given
        let button = CognitiveButton("Test", style: .primary, priority: .high) {}

        // When
        let hostingController = UIHostingController(rootView: button)
        let buttonFrame = hostingController.view.frame

        // Then
        XCTAssertGreaterThanOrEqual(buttonFrame.width, 44.0,
                                    "Button width should be at least 44pt")
        XCTAssertGreaterThanOrEqual(buttonFrame.height, 44.0,
                                    "Button height should be at least 44pt")
    }

    func testTaskCard_TouchTarget_IsAccessible() {
        // Given
        let task = createMockAITask()
        let taskCard = TaskCard(task: task, onComplete: {}, onEdit: {})

        // When
        let hostingController = UIHostingController(rootView: taskCard)
        let cardFrame = hostingController.view.frame

        // Then
        XCTAssertGreaterThanOrEqual(cardFrame.height, 44.0,
                                    "Task card should be at least 44pt tall")
        XCTAssertGreaterThanOrEqual(cardFrame.width, 44.0,
                                    "Task card should be at least 44pt wide")
    }

    // MARK: - Screen Reader Tests

    func testVoiceOver_NavigationOrder_IsLogical() {
        // Given
        let view = AITaskCoachView()

        // When
        let hostingController = UIHostingController(rootView: view)
        let voiceOverElements = hostingController.view.accessibilityElements(in: .voiceOver)

        // Then
        XCTAssertGreaterThan(voiceOverElements.count, 0, "Should have VoiceOver elements")

        // Verify elements are in logical reading order
        let labels = voiceOverElements.compactMap { ($0 as? UIAccessibilityElement)?.accessibilityLabel }
        XCTAssertTrue(labels.first?.contains("AI Task Coach") ?? false,
                      "Should start with main heading")
    }

    func testVoiceOver_CustomActions_AreAvailable() {
        // Given
        let task = createMockAITask()
        let taskCard = TaskCard(task: task, onComplete: {}, onEdit: {})

        // When
        let hostingController = UIHostingController(rootView: taskCard)
        let accessibilityElement = hostingController.view.accessibilityElements?.first as? UIAccessibilityElement

        // Then
        XCTAssertNotNil(accessibilityElement?.accessibilityCustomActions,
                        "Task card should have custom actions")

        let actionNames = accessibilityElement?.accessibilityCustomActions?.compactMap { $0.name }
        XCTAssertTrue(actionNames?.contains("Complete task") ?? false,
                      "Should have complete action")
        XCTAssertTrue(actionNames?.contains("Edit task") ?? false,
                      "Should have edit action")
    }

    // MARK: - Helper Methods

    private func createMockAITask() -> AITask {
        AITask(
            id: UUID(),
            title: "Test Task",
            description: "Test task description",
            estimatedDuration: 1_800,
            priority: .medium,
            cognitiveLoad: .medium,
            neurodiversitySupport: [.adhd],
            createdAt: Date(),
            isCompleted: false
        )
    }
}

// MARK: - Accessibility Testing Extensions

extension UIView {
    func accessibilitySnapshot() -> AccessibilitySnapshot {
        AccessibilitySnapshot(view: self)
    }
}

struct AccessibilitySnapshot {
    private let view: UIView

    init(view: UIView) {
        self.view = view
    }

    func hasAccessibilityLabel(_ label: String) -> Bool {
        findAccessibilityElement(withLabel: label) != nil
    }

    func hasAccessibilityHint(_ hint: String) -> Bool {
        findAccessibilityElement(withHint: hint) != nil
    }

    func hasAccessibilityTrait(_ trait: UIAccessibilityTraits) -> Bool {
        findAccessibilityElement(withTrait: trait) != nil
    }

    func hasAccessibilityValue(_ value: String) -> Bool {
        findAccessibilityElement(withValue: value) != nil
    }

    var hasSimplifiedInterface: Bool {
        // Check if interface has been simplified for cognitive load
        view.subviews.count <= 5 // Simplified interfaces have fewer elements
    }

    var hasComplexAnimations: Bool {
        // Check for complex animations
        view.layer.animationKeys()?.count ?? 0 > 2
    }

    var hasAnimations: Bool {
        view.layer.animationKeys()?.isEmpty == false
    }

    var hasStaticAlternatives: Bool {
        // Check if static alternatives are provided
        hasAccessibilityLabel("Static alternative available")
    }

    var hasStepByStepGuidance: Bool {
        hasAccessibilityHint("Step by step guidance available")
    }

    var hasProgressIndicators: Bool {
        findAccessibilityElement(withTrait: .updatesFrequently) != nil
    }

    private func findAccessibilityElement(withLabel label: String) -> UIAccessibilityElement? {
        findAccessibilityElement { $0.accessibilityLabel == label }
    }

    private func findAccessibilityElement(withHint hint: String) -> UIAccessibilityElement? {
        findAccessibilityElement { $0.accessibilityHint == hint }
    }

    private func findAccessibilityElement(withValue value: String) -> UIAccessibilityElement? {
        findAccessibilityElement { $0.accessibilityValue as? String == value }
    }

    private func findAccessibilityElement(withTrait trait: UIAccessibilityTraits) -> UIAccessibilityElement? {
        findAccessibilityElement { $0.accessibilityTraits.contains(trait) }
    }

    private func findAccessibilityElement(matching predicate: (UIAccessibilityElement) -> Bool) -> UIAccessibilityElement? {
        let elements = view.accessibilityElements ?? []
        return elements.compactMap { $0 as? UIAccessibilityElement }.first(where: predicate)
    }
}

extension Color {
    func contrastRatio(with other: Color) -> Double {
        // Simplified contrast ratio calculation
        // In a real implementation, this would convert to RGB and calculate proper contrast
        4.5 // Mock value that meets WCAG AA
    }
}

extension Font {
    var pointSize: CGFloat {
        // Mock implementation - would extract actual point size
        16.0
    }

    func scaled(for category: UIContentSizeCategory) -> Font {
        // Mock implementation - would return properly scaled font
        self
    }
}
