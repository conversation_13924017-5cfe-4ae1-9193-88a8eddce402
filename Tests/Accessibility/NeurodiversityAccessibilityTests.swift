import AccessibilityAudit
@testable import NeuroNexa
import SwiftUI
import XCTest

// MARK: - Neurodiversity-Specific Accessibility Tests

/// Accessibility tests focused on neurodiversity-specific features and adaptations
@available(iOS 26.0, *)
final class NeurodiversityAccessibilityTests: XCTestCase {
    // MARK: - Properties
    private var mockAITaskCoachService: MockAITaskCoachService!
    private var mockBreathingService: MockBreathingService!
    private var mockUserRepository: MockUserRepository!

    // MARK: - Setup & Teardown

    override func setUp() {
        super.setUp()
        mockAITaskCoachService = MockAITaskCoachService()
        mockBreathingService = MockBreathingService()
        mockUserRepository = MockUserRepository()
    }

    override func tearDown() {
        mockUserRepository = nil
        mockBreathingService = nil
        mockAITaskCoachService = nil
        super.tearDown()
    }

    // MARK: - Cognitive Load Adaptation Tests

    func testCognitiveLoadAdaptation_ReducesComplexity() {
        // Given
        let highLoadButton = CognitiveButton(
            "Complex Action",
            style: .primary,
            priority: .high
        ) {}
        .environment(\.cognitiveLoadLevel, .high)

        // When
        let hostingController = UIHostingController(rootView: highLoadButton)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasSimplifiedInterface,
                      "High cognitive load should simplify interface")
        XCTAssertFalse(snapshot.hasComplexAnimations,
                       "High cognitive load should reduce animations")
    }

    func testCognitiveLoadAdaptation_AdjustsTextSize() {
        // Given
        let overloadButton = CognitiveButton(
            "Important Action",
            style: .primary,
            priority: .urgent
        ) {}
        .environment(\.cognitiveLoadLevel, .overload)

        // When
        let hostingController = UIHostingController(rootView: overloadButton)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasLargerText,
                      "Cognitive overload should increase text size")
        XCTAssertTrue(snapshot.hasIncreasedSpacing,
                      "Cognitive overload should increase spacing")
    }

    // MARK: - Sensory Adaptation Tests

    func testSensoryAdaptation_ReducesMotion() {
        // Given
        let motionSensitiveView = BreathingView(
            viewModel: BreathingViewModel(
                breathingService: mockBreathingService,
                userRepository: mockUserRepository
            )
        )
        .environment(\.sensoryPreferences, SensoryPreferences(
            motionSensitivity: .high,
            colorContrast: .medium,
            soundSensitivity: .medium
        ))

        // When
        let hostingController = UIHostingController(rootView: motionSensitiveView)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertFalse(snapshot.hasAnimations,
                       "High motion sensitivity should disable animations")
        XCTAssertTrue(snapshot.hasStaticAlternatives,
                      "Should provide static alternatives to animations")
    }

    func testSensoryAdaptation_AdjustsColorContrast() {
        // Given
        let highContrastButton = CognitiveButton(
            "Test Button",
            style: .primary,
            priority: .medium
        ) {}
        .environment(\.sensoryPreferences, SensoryPreferences(
            motionSensitivity: .low,
            colorContrast: .maximum,
            soundSensitivity: .low
        ))

        // When
        let hostingController = UIHostingController(rootView: highContrastButton)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasHighContrast,
                      "Maximum contrast should apply high contrast colors")
        XCTAssertGreaterThanOrEqual(snapshot.contrastRatio, 21.0,
                                    "Should meet WCAG AAA contrast requirements")
    }

    // MARK: - Executive Function Support Tests

    func testExecutiveFunctionSupport_ProvidesStructure() {
        // Given
        let task = createMockAITask()
        let taskCard = TaskCard(task: task, onComplete: {}, onEdit: {})
            .environment(\.executiveFunctionLevel, .low)

        // When
        let hostingController = UIHostingController(rootView: taskCard)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasStepByStepGuidance,
                      "Low executive function should provide step-by-step guidance")
        XCTAssertTrue(snapshot.hasProgressIndicators,
                      "Should show clear progress indicators")
    }

    func testExecutiveFunctionSupport_ProvidesReminders() {
        // Given
        let task = createMockAITask()
        let taskCard = TaskCard(task: task, onComplete: {}, onEdit: {})
            .environment(\.executiveFunctionLevel, .low)

        // When
        let hostingController = UIHostingController(rootView: taskCard)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasReminderElements,
                      "Should provide reminder elements for low executive function")
        XCTAssertTrue(snapshot.hasTimeEstimates,
                      "Should show time estimates for planning")
    }

    // MARK: - ADHD-Specific Accessibility Tests

    func testADHDSupport_ReducesDistraction() {
        // Given
        let adhdTaskCard = TaskCard(
            task: createMockAITask(neurodiversitySupport: [.adhd]),
            onComplete: {},
            onEdit: {}
        )
        .environment(\.cognitiveLoadLevel, .medium)

        // When
        let hostingController = UIHostingController(rootView: adhdTaskCard)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasMinimalVisualNoise,
                      "ADHD support should reduce visual distractions")
        XCTAssertTrue(snapshot.hasFocusIndicators,
                      "Should provide clear focus indicators")
    }

    func testADHDSupport_ProvidesBreakdowns() {
        // Given
        let adhdTask = createMockAITask(
            neurodiversitySupport: [.adhd],
            estimatedDuration: 3_600 // 1 hour
        )
        let taskCard = TaskCard(task: adhdTask, onComplete: {}, onEdit: {})

        // When
        let hostingController = UIHostingController(rootView: taskCard)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasTaskBreakdown,
                      "ADHD tasks should be broken down into smaller steps")
        XCTAssertTrue(snapshot.hasTimeSegments,
                      "Should show time segments for long tasks")
    }

    // MARK: - Autism-Specific Accessibility Tests

    func testAutismSupport_ProvidesStructure() {
        // Given
        let autismTaskCard = TaskCard(
            task: createMockAITask(neurodiversitySupport: [.autism]),
            onComplete: {},
            onEdit: {}
        )

        // When
        let hostingController = UIHostingController(rootView: autismTaskCard)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasPredictableLayout,
                      "Autism support should provide predictable layouts")
        XCTAssertTrue(snapshot.hasConsistentNavigation,
                      "Should maintain consistent navigation patterns")
    }

    func testAutismSupport_ReducesSensoryOverload() {
        // Given
        let autismView = BreathingView(
            viewModel: BreathingViewModel(
                breathingService: mockBreathingService,
                userRepository: mockUserRepository
            )
        )
        .environment(\.sensoryPreferences, SensoryPreferences(
            motionSensitivity: .high,
            colorContrast: .enhanced,
            soundSensitivity: .high
        ))

        // When
        let hostingController = UIHostingController(rootView: autismView)
        let snapshot = hostingController.view.accessibilitySnapshot()

        // Then
        XCTAssertTrue(snapshot.hasReducedSensoryInput,
                      "Should reduce sensory input for autism support")
        XCTAssertTrue(snapshot.hasCalmerColors,
                      "Should use calmer, less stimulating colors")
    }

    // MARK: - Helper Methods

    private func createMockAITask(
        neurodiversitySupport: [NeurodiversityType] = [.adhd],
        estimatedDuration: TimeInterval = 1_800
    ) -> AITask {
        AITask(
            id: UUID(),
            title: "Test Task",
            description: "Test task description",
            estimatedDuration: estimatedDuration,
            priority: .medium,
            cognitiveLoad: .medium,
            neurodiversitySupport: neurodiversitySupport,
            createdAt: Date(),
            isCompleted: false
        )
    }
}
