# Podfile for NeuroNexa iOS 26 Project
# Updated: July 2, 2025

platform :ios, '26.0'
use_frameworks!
inhibit_all_warnings!

# Global pod configurations
install! 'cocoapods', :deterministic_uuids => false

target 'NeuroNexa' do
  # Firebase - Backend services
  pod 'Firebase/Analytics', '~> 10.0'
  pod 'Firebase/Crashlytics', '~> 10.0'
  pod 'Firebase/RemoteConfig', '~> 10.0'
  pod 'Firebase/Auth', '~> 10.0'
  pod 'Firebase/Firestore', '~> 10.0'
  pod 'Firebase/Storage', '~> 10.0'
  
  # UI & Animation
  pod 'lottie-ios', '~> 4.0'
  pod 'SnapKit', '~> 5.0'
  pod 'Hero', '~> 1.6'
  
  # Development Tools (only in Debug)
  pod 'SwiftLint', '~> 0.52', :configurations => ['Debug']
  pod 'SwiftFormat/CLI', '~> 0.51', :configurations => ['Debug']
  
  # Testing targets
  target 'NeuroNexaTests' do
    inherit! :search_paths
    pod 'Quick', '~> 7.0'
    pod 'Nimble', '~> 12.0'
  end
  
  target 'NeuroNexaUITests' do
    inherit! :search_paths
  end
end

# Apple Watch target
target 'NeuroNexaWatch' do
  platform :watchos, '26.0'
  
  # Watch-specific dependencies
  pod 'Firebase/Analytics', '~> 10.0'
  pod 'lottie-ios', '~> 4.0'
end

# Post-install configurations
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # Set deployment targets
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '26.0'
      config.build_settings['WATCHOS_DEPLOYMENT_TARGET'] = '26.0'
      
      # Set Swift version
      config.build_settings['SWIFT_VERSION'] = '6.0'
      
      # Enable strict concurrency checking
      config.build_settings['SWIFT_STRICT_CONCURRENCY'] = 'complete'
      
      # Optimize for iOS 26
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['ENABLE_TESTABILITY'] = 'YES'
      
      # Security settings
      config.build_settings['ENABLE_HARDENED_RUNTIME'] = 'YES'
      config.build_settings['ENABLE_LIBRARY_VALIDATION'] = 'YES'
    end
  end
  
  # Fix for Xcode 26 compatibility
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
      end
    end
  end
end
