# NeuroNexa Development Environment Rules

## 🚨 MANDATORY REQUIREMENTS

### **RULE #1: iOS 26 EXCLUSIVE DEVELOPMENT**
- **REQUIREMENT**: All development MUST target iOS 26.0 exclusively
- **PROHIBITION**: No development targeting iOS 25.x, iOS 24.x, or earlier versions
- **ENFORCEMENT**: Build configurations must specify iOS 26.0 as minimum deployment target
- **VALIDATION**: All features must be tested on iOS 26 devices/simulators only

### **RULE #2: XCODE BETA 26 EXCLUSIVE TOOLCHAIN**
- **REQUIREMENT**: All development MUST use Xcode Beta 26.0 exclusively
- **PROHIBITION**: No development using Xcode 16.x stable, Xcode 15.x, or earlier versions
- **ENFORCEMENT**: Project files must be compatible with Xcode Beta 26 toolchain
- **VALIDATION**: All builds must be executed through Xcode Beta 26

## 📋 COMPLIANCE CHECKLIST

### Pre-Development Verification
- [ ] Xcode Beta 26.0 installed and active
- [ ] iOS 26.0 SDK available and configured
- [ ] Project deployment target set to iOS 26.0
- [ ] Simulator running iOS 26.0
- [ ] No legacy iOS versions in project settings

### Build Configuration Requirements
- [ ] IPHONEOS_DEPLOYMENT_TARGET = 26.0
- [ ] TARGETED_DEVICE_FAMILY = iPhone + iPad
- [ ] SWIFT_VERSION = 6.0 (iOS 26 compatible)
- [ ] ENABLE_PREVIEWS = YES (SwiftUI iOS 26)

### Testing Requirements
- [ ] All tests run on iOS 26 simulators
- [ ] Physical device testing on iOS 26 devices
- [ ] No compatibility testing with earlier iOS versions
- [ ] watchOS 26 integration testing (when applicable)

## 🛠 TECHNICAL SPECIFICATIONS

### Supported Xcode Beta 26 Features
- SwiftUI iOS 26 enhancements
- Swift 6.0 language features
- iOS 26 SDK frameworks
- Enhanced accessibility APIs
- Advanced neurodiversity support features

### Prohibited Legacy Features
- iOS 25.x specific APIs
- Deprecated iOS 24.x methods
- Legacy UIKit patterns (use SwiftUI iOS 26)
- Outdated accessibility implementations

## 🔧 ENFORCEMENT MECHANISMS

### Automated Checks
1. **Build Script Validation**: Verify Xcode Beta 26 before builds
2. **Deployment Target Verification**: Ensure iOS 26.0 minimum
3. **SDK Version Checking**: Confirm iOS 26 SDK usage
4. **CI/CD Pipeline**: Reject builds from non-compliant environments

### Manual Verification
1. **Code Review**: Verify iOS 26 exclusive features
2. **Testing Protocol**: Confirm iOS 26 device/simulator usage
3. **Documentation Review**: Ensure iOS 26 compliance in docs
4. **Release Preparation**: Validate App Store iOS 26 requirements

## 📱 DEVICE COMPATIBILITY

### Supported iOS 26 Devices
- iPhone 16 series (all models)
- iPhone 15 series (all models)
- iPhone 14 series (all models)
- iPad Pro (all iOS 26 compatible models)
- iPad Air (all iOS 26 compatible models)
- iPad mini (iOS 26 compatible models)

### watchOS 26 Integration
- Apple Watch Series 10
- Apple Watch Series 9
- Apple Watch Ultra 2
- Apple Watch SE (2nd generation)

## 🚫 VIOLATION CONSEQUENCES

### Development Violations
- **Immediate**: Build rejection and error reporting
- **Process**: Mandatory environment correction before proceeding
- **Documentation**: Violation logging in development records

### Release Violations
- **Blocking**: App Store submission rejection
- **Remediation**: Complete rebuild with compliant environment
- **Validation**: Full compliance verification required

## 📊 COMPLIANCE MONITORING

### Daily Checks
- Xcode version verification
- iOS deployment target confirmation
- Build environment validation
- Testing device iOS version verification

### Weekly Reviews
- Project configuration audit
- Dependency iOS 26 compatibility check
- Feature implementation iOS 26 compliance
- Documentation accuracy verification

## 🎯 SUCCESS CRITERIA

### Environment Compliance
- ✅ 100% Xcode Beta 26 usage
- ✅ 100% iOS 26 targeting
- ✅ Zero legacy iOS version references
- ✅ Complete iOS 26 feature utilization

### Build Success Metrics
- ✅ All builds execute on Xcode Beta 26
- ✅ All tests pass on iOS 26 simulators
- ✅ App Store validation with iOS 26 requirements
- ✅ watchOS 26 integration functional

---

**ENFORCEMENT DATE**: Immediate
**REVIEW DATE**: Weekly
**COMPLIANCE OFFICER**: Development Team Lead
**ESCALATION**: Project Manager for violations

*This document establishes mandatory development environment requirements for the NeuroNexa project to ensure optimal iOS 26 and Xcode Beta 26 utilization.*
