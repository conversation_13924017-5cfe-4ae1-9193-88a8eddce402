# 🛠️ **NeuroNexa CLI Tools Reference Guide**

## 📋 **Overview**
This comprehensive reference guide covers all CLI tools installed and configured for NeuroNexa iOS 26 development, integrated with MCP (Monitoring, Control, Planning) tools for optimal development workflow.

---

## ✅ **Installed CLI Tools Status**

### **Core iOS Development Tools**
- ✅ **Xcode Command Line Tools**: `xcode-select version 2409`
- ✅ **CocoaPods**: `1.16.2` - Dependency management for iOS
- ✅ **Fastlane**: `2.228.0` - iOS automation and deployment
- ✅ **SwiftLint**: `0.59.1` - Swift code linting and style enforcement
- ✅ **SwiftFormat**: `0.56.4` - Swift code formatting
- ✅ **xcbeautify**: `2.28.0` - Xcode build output formatting
- ✅ **Periphery**: `2.21.2` - Unused code detection

### **Backend & Infrastructure Tools**
- ✅ **GitHub CLI**: `2.74.2` - GitHub repository management
- ✅ **Firebase CLI**: `14.2.0` - Firebase project management
- ✅ **Supabase CLI**: `2.30.4` - Supabase project management
- ✅ **Homebrew**: `4.5.8` - Package manager

---

## 🎯 **Phase-Specific CLI Usage Plan**

### **Phase 2: Authentication & User Management**
```bash
# Supabase setup for authentication
supabase init
supabase start
supabase gen types swift --project-id your-project-id > NeuroNexa/Models/Database.swift

# GitHub repository setup
gh repo create neuronexa-ios --private
gh auth login

# Fastlane setup for CI/CD
fastlane init
```

### **Phase 3-8: Development & Deployment**
```bash
# Code quality enforcement
swiftlint --config .swiftlint.yml
swiftformat . --config .swiftformat

# Build and test automation
fastlane scan  # Run tests
fastlane beta  # Deploy to TestFlight
fastlane release  # Deploy to App Store

# Unused code detection
periphery scan --project NeuroNexa.xcodeproj
```

---

## 📚 **Fastlane Integration Guide**

### **Basic Fastfile Configuration**
```ruby
# fastlane/Fastfile
default_platform(:ios)

platform :ios do
  desc "Run tests"
  lane :test do
    run_tests(
      scheme: "NeuroNexa",
      devices: ["iPhone 15 Pro", "iPad Pro (12.9-inch)"],
      clean: true
    )
  end

  desc "Deploy to TestFlight"
  lane :beta do
    increment_build_number
    sync_code_signing(type: "appstore")
    build_app(scheme: "NeuroNexa")
    upload_to_testflight(
      changelog: changelog_from_git_commits
    )
    slack(message: "NeuroNexa beta build uploaded to TestFlight")
  end

  desc "Deploy to App Store"
  lane :release do
    capture_screenshots
    sync_code_signing(type: "appstore")
    build_app(scheme: "NeuroNexa")
    upload_to_app_store
    slack(message: "NeuroNexa released to App Store")
  end
end
```

### **Advanced Testing Configuration**
```ruby
# fastlane/Scanfile
scheme("NeuroNexa")
devices([
  "iPhone 15 Pro",
  "iPhone 15",
  "iPad Pro (12.9-inch)",
  "iPad Air (5th generation)"
])

clean(true)
output_types("html", "junit")
output_directory("./test_output")

# Neurodiversity-specific testing
launch_arguments([
  "-ADHD_MODE YES",
  "-AUTISM_MODE YES", 
  "-ACCESSIBILITY_TESTING YES"
])
```

---

## 🔧 **SwiftLint Configuration**

### **.swiftlint.yml**
```yaml
# NeuroNexa SwiftLint Configuration
disabled_rules:
  - trailing_whitespace
  - line_length

opt_in_rules:
  - empty_count
  - explicit_init
  - closure_spacing
  - accessibility_label_for_image

included:
  - NeuroNexa
  - NeuroNexaTests

excluded:
  - Pods
  - fastlane
  - Documentation

line_length:
  warning: 120
  error: 150

identifier_name:
  min_length:
    warning: 3
  max_length:
    warning: 40
    error: 50

# Neurodiversity-specific rules
custom_rules:
  accessibility_label_required:
    name: "Accessibility Label Required"
    regex: '\.accessibilityLabel\s*=\s*nil'
    message: "Accessibility labels are required for neurodiversity support"
    severity: error
```

---

## 🎨 **SwiftFormat Configuration**

### **.swiftformat**
```
# NeuroNexa SwiftFormat Configuration
--swiftversion 6.0
--indent 4
--tabwidth 4
--maxwidth 120
--wraparguments before-first
--wrapcollections before-first
--closingparen balanced
--commas inline
--decimalgrouping 3,4
--exponentcase lowercase
--fractiongrouping disabled
--hexgrouping 4,8
--hexliteralcase uppercase
--octalgrouping 4,8
--operatorfunc spaced
--ranges spaced
--semicolons never
--stripunusedargs closure-only
--trimwhitespace always
--ifdef no-indent
--redundanttype inferred
--self remove
--importgrouping testable-bottom
--patternlet inline
```

---

## 🔍 **Periphery Configuration**

### **.periphery.yml**
```yaml
# NeuroNexa Periphery Configuration
project: NeuroNexa.xcodeproj
schemes:
  - NeuroNexa
targets:
  - NeuroNexa
  - NeuroNexaTests

# Exclude generated files
exclude:
  - "*/Generated/*"
  - "*/Pods/*"
  - "*/fastlane/*"

# Retain accessibility-related code
retain_public: true
retain_objc_accessible: true

# Custom retention rules for neurodiversity features
retain_codable: true
retain_unused_protocol_func_params: true
```

---

## 📊 **MCP Tools Integration**

### **Monitoring Commands**
```bash
# Code quality monitoring
swiftlint --reporter json > reports/swiftlint.json
periphery scan --format json > reports/unused_code.json

# Test coverage monitoring  
fastlane scan --code_coverage true
xcrun xccov view --report --json DerivedData/*/Logs/Test/*.xcresult > reports/coverage.json

# Build performance monitoring
xcbeautify --report json < xcodebuild.log > reports/build_performance.json
```

### **Control Commands**
```bash
# Automated code formatting
swiftformat . --config .swiftformat
swiftlint --fix

# Automated testing
fastlane test

# Automated deployment
fastlane beta  # TestFlight
fastlane release  # App Store
```

### **Planning Commands**
```bash
# Project analysis
periphery scan --verbose
swiftlint analyze --compiler-log-path build.log

# Dependency analysis
pod outdated
pod update --dry-run

# Release planning
fastlane action increment_build_number
git log --oneline --since="1 week ago"
```

---

## 🚀 **Phase 2 Implementation Commands**

### **Authentication Setup**
```bash
# 1. Initialize Supabase project
supabase init
supabase login
supabase projects create neuronexa-ios

# 2. Setup authentication tables
supabase db reset
supabase gen types swift --project-id [PROJECT_ID] > NeuroNexa/Models/Database.swift

# 3. Configure GitHub repository
gh repo create neuronexa-ios --private --description "NeuroNexa iOS 26 Native App"
gh secret set SUPABASE_URL --body "[SUPABASE_URL]"
gh secret set SUPABASE_ANON_KEY --body "[SUPABASE_ANON_KEY]"

# 4. Setup Fastlane for CI/CD
fastlane init
fastlane match init  # Code signing
```

### **Development Workflow**
```bash
# Daily development cycle
swiftlint --fix  # Fix linting issues
swiftformat .    # Format code
fastlane test    # Run tests
periphery scan   # Check for unused code

# Pre-commit hooks
git add .
swiftlint --strict
swiftformat --lint .
fastlane test
git commit -m "feat: implement authentication system"
```

---

## 📈 **Performance Optimization**

### **Build Optimization**
```bash
# Parallel builds
xcodebuild -jobs $(sysctl -n hw.ncpu)

# Build cache optimization
xcbeautify --quiet < xcodebuild.log

# Dependency optimization
pod install --repo-update
```

### **Test Optimization**
```bash
# Parallel testing
fastlane scan --max_concurrent_simulators 4

# Test result caching
fastlane scan --result_bundle true
```

---

## 🔐 **Security & Compliance**

### **Code Security Scanning**
```bash
# SwiftLint security rules
swiftlint --config .swiftlint-security.yml

# Dependency vulnerability scanning
pod audit

# Secrets detection
git secrets --scan
```

### **HIPAA Compliance Checks**
```bash
# Data handling verification
grep -r "UserDefaults\|Keychain\|CoreData" NeuroNexa/
swiftlint --config .swiftlint-hipaa.yml
```

---

## 📝 **Documentation Generation**

### **Code Documentation**
```bash
# Generate Swift documentation
swift-doc generate NeuroNexa/ --output Documentation/API/

# Generate test reports
fastlane scan --output_types html,junit
```

### **Project Documentation**
```bash
# Update README with latest CLI versions
echo "## CLI Tools Versions" > CLI_VERSIONS.md
echo "- Fastlane: $(fastlane --version)" >> CLI_VERSIONS.md
echo "- SwiftLint: $(swiftlint version)" >> CLI_VERSIONS.md
echo "- Supabase: $(supabase --version)" >> CLI_VERSIONS.md
```

---

## 🎯 **Next Steps for Phase 2**

1. **Initialize Authentication Infrastructure**
   ```bash
   supabase init
   supabase start
   ```

2. **Setup Code Quality Pipeline**
   ```bash
   swiftlint --generate-config > .swiftlint.yml
   swiftformat --config .swiftformat
   ```

3. **Configure CI/CD Pipeline**
   ```bash
   fastlane init
   fastlane match init
   ```

4. **Begin Authentication Implementation**
   - User registration/login flows
   - Biometric authentication
   - HIPAA-compliant data handling
   - Secure token management

This CLI tools reference provides the foundation for efficient, automated development workflow throughout all remaining phases of NeuroNexa development.
