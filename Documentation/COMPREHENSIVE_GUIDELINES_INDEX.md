# 📚 **NeuroNexa Comprehensive Guidelines & Rules Index**

**Version:** 1.0  
**Created:** January 8, 2025  
**Project:** NeuroNexa iOS 26 Native App  
**Purpose:** Central index of all project guidelines, rules, and policies  

---

## 🚨 **MANDATORY CORE REQUIREMENTS**

### **1. Development Environment (ENFORCED)**
- **File**: `Documentation/NeuroNexa-Augment-Rules.md`
- **File**: `Documentation/Development-Environment-Rules.md`
- **Validation**: `Scripts/validate-environment.sh`

**RULE #1**: iOS 26.0 EXCLUSIVE development only
**RULE #2**: Xcode Beta 26 EXCLUSIVE toolchain only
**RULE #3**: NO development on iOS 25.x, 24.x, or earlier versions
**ENFORCEMENT**: Automated validation scripts prevent non-compliant development

### **2. Apple Intelligence Prohibition (ENFORCED)**
- **File**: `Documentation/APPLE_INTELLIGENCE_PROHIBITION_POLICY.md`
- **Validation**: `Scripts/validate-apple-intelligence-prohibition.sh`

**POLICY**: OpenAI EXCLUSIVE for all AI features
**PROHIBITION**: NO Apple Intelligence integrations permitted
**ENFORCEMENT**: SwiftLint rules + pre-commit hooks + automated scanning

### **3. SwiftLint 100% Compliance (ENFORCED)**
- **File**: `.swiftlint.yml`
- **Validation**: Continuous SwiftLint checking

**REQUIREMENT**: 100% SwiftLint compliance mandatory
**ENFORCEMENT**: Build fails on SwiftLint violations

---

## 🏗️ **ARCHITECTURE & DEVELOPMENT RULES**

### **4. iOS Development Standards**
- **File**: `Documentation/iOS_DEVELOPMENT_RULES.md`

**Core Principles**:
- Neurodiversity-First Development (ALWAYS prioritize cognitive accessibility)
- Privacy & Security First (AES-256 encryption, Keychain storage)
- Performance Standards (<2s app launch, <16ms main thread blocking)
- MVVM-C Architecture Implementation

### **5. UI/UX Design System**
- **File**: `Documentation/UI_UX_DESIGN_SYSTEM.md`

**Design Philosophy**: Neurodiversity-First
**Accessibility Standard**: WCAG 2.2 AAA
**Target Users**: ADHD, Autism Spectrum, Executive Dysfunction
**Core Principles**: Cognitive Load Optimization, Sensory Adaptation, Executive Function Support

### **6. watchOS 26 Integration**
- **File**: `Documentation/watchOS-26-Integration-Reference-Library.md`

**Requirements**: iOS 26.0 + watchOS 26.0 exclusive targeting
**Framework**: SwiftUI cross-platform development
**Architecture**: Shared data models with platform-specific implementations

---

## 🛠️ **DEVELOPMENT WORKFLOW & TOOLS**

### **7. MCP Tools Framework**
- **File**: `Documentation/MCP_TOOLS_GUIDELINES.md`

**Framework**: Monitoring, Control, and Planning
**Components**: Development progress tracking, code quality monitoring, strategic planning
**Task Management**: Systematic task state management with clear transition rules

### **8. iOS 26 Enhanced Development**
- **File**: `Documentation/iOS_26_ENHANCED_LIBRARY.md`

**SDK**: iOS 26.0 (iphoneos26.0)
**Simulator**: iOS 26.0 (iphonesimulator26.0)
**Features**: iOS 26 new features for NeuroNexa development

---

## 🔧 **CONFIGURATION FILES**

### **9. SwiftLint Configuration**
- **File**: `.swiftlint.yml`

**Included Paths**: App, UI, Core, Data, Tests, WatchOS
**Excluded Paths**: Pods, Build, .build, fastlane, Reports
**Custom Rules**: Apple Intelligence prohibition, accessibility requirements
**Opt-in Rules**: 50+ additional quality rules enabled

### **10. Validation Scripts**
- **Environment**: `Scripts/validate-environment.sh`
- **Apple Intelligence**: `Scripts/validate-apple-intelligence-prohibition.sh`
- **Claude Permissions**: `Scripts/validate-claude-permissions.sh`
- **Pre-commit**: `Scripts/pre-commit-validation.sh`

---

## 📋 **PROJECT MANAGEMENT & DOCUMENTATION**

### **11. Development Workflow**
- **File**: `Documentation/APP_STORE_DEPLOYMENT_WORKFLOW.md`
- **File**: `Documentation/PHASED_DEVELOPMENT_PLAN.md`

**Goal**: 100% App Store deployment readiness
**Approach**: Systematic incremental development with task management
**Testing**: Comprehensive testing including accessibility requirements

### **12. Claude Code Integration**
- **File**: `Documentation/CLAUDE_CODE_INTEGRATION_WORKFLOW.md`
- **File**: `Documentation/CLAUDE_APPROVAL_WORKFLOW.md`

**Purpose**: Controlled write permissions for SwiftUI optimizations
**Scope**: UI/Views, UI/Components, accessibility enhancements
**Safety**: Approval workflow and safety boundaries maintained

---

## ✅ **COMPLIANCE CHECKLIST**

### **Pre-Development Verification**
- [ ] Xcode Beta 26.0 installed and active
- [ ] iOS 26.0 SDK available and configured
- [ ] Project deployment target set to iOS 26.0
- [ ] SwiftLint configuration validated
- [ ] Apple Intelligence prohibition verified
- [ ] Environment validation script passes

### **Development Standards**
- [ ] Neurodiversity-first design principles followed
- [ ] WCAG 2.2 AAA accessibility compliance
- [ ] MVVM-C architecture implementation
- [ ] OpenAI exclusive AI integration
- [ ] 100% SwiftLint compliance maintained

### **Quality Gates**
- [ ] All validation scripts pass
- [ ] SwiftLint violations = 0
- [ ] Accessibility tests pass
- [ ] Performance benchmarks met
- [ ] Security requirements satisfied

---

## 🎯 **QUICK REFERENCE**

**Environment Validation**: `./Scripts/validate-environment.sh`
**Apple Intelligence Check**: `./Scripts/validate-apple-intelligence-prohibition.sh`
**SwiftLint Check**: `swiftlint --strict`
**Build Validation**: `xcodebuild -scheme NeuroNexa build`

**Emergency Contacts**: See project documentation for escalation procedures
**Policy Updates**: All guideline changes require documentation update and team notification

---

## 📚 **CONTEXT7 REFERENCE LIBRARIES INDEXED**

### **13. SwiftUI iOS Development Library**
- **Context7 ID**: `/liveview-native/liveview-client-swiftui`
- **Focus**: SwiftUI iOS development best practices
- **Content**: 5000+ tokens of SwiftUI patterns, LiveView Native integration
- **Key Topics**: SwiftUI architecture, view modifiers, form controls, navigation

**Key SwiftUI Patterns Indexed**:
- iOS 26 SwiftUI best practices and modern patterns
- NavigationStack implementation and navigation patterns
- Form controls with @FormState and FormValue protocols
- Custom view modifiers and StylesheetResolvable types
- Accessibility implementation with WCAG compliance
- Performance optimization techniques for SwiftUI views
- LiveElement and custom component creation
- Event handling and coordinator patterns

---

**Status**: ✅ **COMPREHENSIVE INDEX COMPLETE WITH CONTEXT7 INTEGRATION**
**Last Updated**: January 8, 2025
**Next Review**: Weekly during active development
