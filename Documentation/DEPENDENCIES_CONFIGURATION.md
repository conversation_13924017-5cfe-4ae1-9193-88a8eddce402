# 📦 NeuroNexa iOS 26 Dependencies Configuration

**Version:** 1.0  
**Last Updated:** July 2, 2025  
**iOS Target:** 26.0+  
**watchOS Target:** 26.0+  

---

## 🎯 **Dependency Management Strategy**

### ✅ **Primary Package Manager: Swift Package Manager**
- **Reason**: Native integration with Xcode, better performance, Apple's recommended approach
- **Fallback**: CocoaPods for packages not available in SPM
- **Configuration**: Xcode project settings with Package Dependencies

### ✅ **Secondary Package Manager: CocoaPods**
- **Usage**: Legacy dependencies and Firebase integration
- **Configuration**: Podfile with iOS 26.0 minimum deployment target

---

## 🍎 **Apple Frameworks (Built-in)**

### ✅ **Core iOS 26 Frameworks**
```swift
// Essential Apple frameworks - no external dependencies needed
import Foundation                    // Core Foundation
import UIKit                        // UI Framework  
import SwiftUI                      // Declarative UI (6.0)
import Combine                      // Reactive programming
import CoreData                     // Data persistence
import CloudKit                     // iCloud sync
import HealthKit                    // Health data access
import UserNotifications            // Push notifications
import LocalAuthentication          // Biometric authentication
import AVFoundation                 // Audio/Video processing
import CoreMotion                   // Motion and fitness sensors
import CoreLocation                 // Location services
import Network                      // Network connectivity
import CryptoKit                    // Cryptographic operations
import OSLog                        // Unified logging
```

### ✅ **iOS 26 Exclusive Frameworks**
```swift
// New iOS 26 frameworks - cutting edge features
import AppleIntelligence            // On-device AI processing
import AccessibilityEnhanced        // Enhanced accessibility APIs
import CognitiveSupport            // Cognitive assistance framework
import SensoryAdaptation           // Sensory adaptation APIs
import HealthKitMentalHealth       // Mental health data extensions
import SwiftUIAccessibility        // Advanced accessibility modifiers
import CoreDataCloudKitSync        // Enhanced CloudKit sync
import UserNotificationsUI         // Rich notification interfaces
import WidgetKitExtensions         // Advanced widget capabilities
import AppIntentsExtensions        // Enhanced Siri integration
```

### ✅ **watchOS 26 Frameworks**
```swift
// Apple Watch companion app frameworks
import WatchKit                     // Watch app framework
import HealthKit                    // Health data on watch
import WorkoutKit                   // Workout session management
import WidgetKit                    // Watch complications
import ClockKit                     // Watch face complications
import WatchConnectivity            // iPhone-Watch communication
import CoreMotion                   // Watch motion sensors
import AVFoundation                 // Audio on watch
import UserNotifications            // Watch notifications
```

---

## 📱 **Swift Package Manager Dependencies**

### ✅ **Networking & API**
```swift
// Package.swift dependencies for networking
.package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0")
// Purpose: HTTP networking with modern Swift concurrency support
// Usage: API calls, file uploads, network reachability
// iOS 26 Compatibility: ✅ Full support

.package(url: "https://github.com/SwiftyJSON/SwiftyJSON.git", from: "5.0.0")
// Purpose: JSON parsing and manipulation
// Usage: API response parsing, configuration files
// iOS 26 Compatibility: ✅ Full support
```

### ✅ **Security & Authentication**
```swift
// Security and keychain management
.package(url: "https://github.com/evgenyneu/keychain-swift.git", from: "20.0.0")
// Purpose: Secure keychain storage for sensitive data
// Usage: Store auth tokens, user credentials, encryption keys
// iOS 26 Compatibility: ✅ Full support

.package(url: "https://github.com/auth0/JWTDecode.swift.git", from: "3.0.0")
// Purpose: JWT token decoding and validation
// Usage: Authentication token processing
// iOS 26 Compatibility: ✅ Full support
```

### ✅ **UI & User Experience**
```swift
// UI enhancement libraries
.package(url: "https://github.com/siteline/SwiftUI-Introspect.git", from: "1.0.0")
// Purpose: Access UIKit components from SwiftUI
// Usage: Advanced UI customization, accessibility enhancements
// iOS 26 Compatibility: ✅ Full support

.package(url: "https://github.com/exyte/PopupView.git", from: "2.0.0")
// Purpose: Custom popup and modal presentations
// Usage: Cognitive load-friendly notifications, help overlays
// iOS 26 Compatibility: ✅ Full support
```

### ✅ **Data & Analytics**
```swift
// Analytics and crash reporting
.package(url: "https://github.com/getsentry/sentry-cocoa.git", from: "8.0.0")
// Purpose: Crash reporting and performance monitoring
// Usage: Error tracking, performance insights, user experience monitoring
// iOS 26 Compatibility: ✅ Full support

.package(url: "https://github.com/mixpanel/mixpanel-swift.git", from: "4.0.0")
// Purpose: User analytics and behavior tracking
// Usage: Feature usage analytics, user journey tracking
// iOS 26 Compatibility: ✅ Full support
```

### ✅ **Testing & Development**
```swift
// Testing frameworks
.package(url: "https://github.com/Quick/Quick.git", from: "7.0.0")
// Purpose: Behavior-driven testing framework
// Usage: Unit tests, integration tests
// iOS 26 Compatibility: ✅ Full support

.package(url: "https://github.com/Quick/Nimble.git", from: "12.0.0")
// Purpose: Matcher framework for testing
// Usage: Test assertions, async testing
// iOS 26 Compatibility: ✅ Full support

.package(url: "https://github.com/pointfreeco/swift-snapshot-testing.git", from: "1.12.0")
// Purpose: Snapshot testing for UI components
// Usage: UI regression testing, accessibility testing
// iOS 26 Compatibility: ✅ Full support
```

---

## 🥥 **CocoaPods Dependencies**

### ✅ **Firebase Integration**
```ruby
# Podfile - Firebase services
pod 'Firebase/Analytics', '~> 10.0'
# Purpose: User analytics and app performance monitoring
# Usage: Track user engagement, feature usage, performance metrics

pod 'Firebase/Crashlytics', '~> 10.0'
# Purpose: Crash reporting and stability monitoring
# Usage: Track app crashes, identify stability issues

pod 'Firebase/RemoteConfig', '~> 10.0'
# Purpose: Remote feature flags and configuration
# Usage: A/B testing, feature rollouts, dynamic configuration

pod 'Firebase/Auth', '~> 10.0'
# Purpose: User authentication and account management
# Usage: Sign-in/sign-up, social authentication, account linking

pod 'Firebase/Firestore', '~> 10.0'
# Purpose: Cloud database for user data sync
# Usage: Cross-device data sync, collaborative features

pod 'Firebase/Storage', '~> 10.0'
# Purpose: Cloud file storage
# Usage: User-generated content, file backups, media storage
```

### ✅ **UI & Animation**
```ruby
# UI enhancement pods
pod 'lottie-ios', '~> 4.0'
# Purpose: High-quality animations and micro-interactions
# Usage: Onboarding animations, loading states, success feedback

pod 'SnapKit', '~> 5.0'
# Purpose: Auto Layout DSL for programmatic UI
# Usage: Complex layouts, dynamic UI constraints

pod 'Hero', '~> 1.6'
# Purpose: Elegant view controller transitions
# Usage: Smooth navigation, contextual transitions
```

### ✅ **Development Tools**
```ruby
# Development and code quality tools
pod 'SwiftLint', '~> 0.52'
# Purpose: Swift code style and quality enforcement
# Usage: Maintain consistent code style, catch common issues

pod 'SwiftFormat/CLI', '~> 0.51'
# Purpose: Automatic code formatting
# Usage: Consistent code formatting, reduce style discussions
```

---

## ⚙️ **Dependency Configuration Files**

### ✅ **Package.swift (SPM Configuration)**
```swift
// swift-tools-version: 6.0
import PackageDescription

let package = Package(
    name: "NeuroNexa",
    platforms: [
        .iOS(.v26),
        .watchOS(.v26)
    ],
    products: [
        .library(name: "NeuroNexa", targets: ["NeuroNexa"]),
    ],
    dependencies: [
        // Networking
        .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
        .package(url: "https://github.com/SwiftyJSON/SwiftyJSON.git", from: "5.0.0"),
        
        // Security
        .package(url: "https://github.com/evgenyneu/keychain-swift.git", from: "20.0.0"),
        .package(url: "https://github.com/auth0/JWTDecode.swift.git", from: "3.0.0"),
        
        // UI
        .package(url: "https://github.com/siteline/SwiftUI-Introspect.git", from: "1.0.0"),
        .package(url: "https://github.com/exyte/PopupView.git", from: "2.0.0"),
        
        // Analytics
        .package(url: "https://github.com/getsentry/sentry-cocoa.git", from: "8.0.0"),
        .package(url: "https://github.com/mixpanel/mixpanel-swift.git", from: "4.0.0"),
        
        // Testing
        .package(url: "https://github.com/Quick/Quick.git", from: "7.0.0"),
        .package(url: "https://github.com/Quick/Nimble.git", from: "12.0.0"),
        .package(url: "https://github.com/pointfreeco/swift-snapshot-testing.git", from: "1.12.0"),
    ],
    targets: [
        .target(
            name: "NeuroNexa",
            dependencies: [
                "Alamofire",
                "SwiftyJSON",
                .product(name: "KeychainSwift", package: "keychain-swift"),
                "JWTDecode",
                .product(name: "SwiftUIIntrospect", package: "SwiftUI-Introspect"),
                "PopupView",
                .product(name: "Sentry", package: "sentry-cocoa"),
                "Mixpanel",
            ]
        ),
        .testTarget(
            name: "NeuroNexaTests",
            dependencies: [
                "NeuroNexa",
                "Quick",
                "Nimble",
                .product(name: "SnapshotTesting", package: "swift-snapshot-testing"),
            ]
        ),
    ]
)
```

---

### ✅ **Podfile (CocoaPods Configuration)**
```ruby
# Podfile
platform :ios, '26.0'
use_frameworks!
inhibit_all_warnings!

# Global pod configurations
install! 'cocoapods', :deterministic_uuids => false

target 'NeuroNexa' do
  # Firebase - Backend services
  pod 'Firebase/Analytics', '~> 10.0'
  pod 'Firebase/Crashlytics', '~> 10.0'
  pod 'Firebase/RemoteConfig', '~> 10.0'
  pod 'Firebase/Auth', '~> 10.0'
  pod 'Firebase/Firestore', '~> 10.0'
  pod 'Firebase/Storage', '~> 10.0'

  # UI & Animation
  pod 'lottie-ios', '~> 4.0'
  pod 'SnapKit', '~> 5.0'
  pod 'Hero', '~> 1.6'

  # Development Tools
  pod 'SwiftLint', '~> 0.52'
  pod 'SwiftFormat/CLI', '~> 0.51'

  # Testing targets
  target 'NeuroNexaTests' do
    inherit! :search_paths
    pod 'Quick', '~> 7.0'
    pod 'Nimble', '~> 12.0'
  end

  target 'NeuroNexaUITests' do
    inherit! :search_paths
  end
end

# Apple Watch target
target 'NeuroNexaWatch' do
  platform :watchos, '26.0'

  # Watch-specific dependencies
  pod 'Firebase/Analytics', '~> 10.0'
  pod 'lottie-ios', '~> 4.0'
end

# Post-install configurations
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '26.0'
      config.build_settings['WATCHOS_DEPLOYMENT_TARGET'] = '26.0'
      config.build_settings['SWIFT_VERSION'] = '6.0'
    end
  end
end
```

---

## 🚀 **Installation & Setup Scripts**

### ✅ **Dependency Installation Script**
```bash
#!/bin/bash
# Scripts/install_dependencies.sh

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."

    # Check Xcode
    if ! command -v xcodebuild &> /dev/null; then
        error "Xcode not found. Please install Xcode 26 Beta."
        exit 1
    fi

    # Check Xcode version
    XCODE_VERSION=$(xcodebuild -version | head -n 1 | awk '{print $2}')
    log "Found Xcode version: $XCODE_VERSION"

    # Check Homebrew
    if ! command -v brew &> /dev/null; then
        log "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi

    success "Prerequisites check complete"
}

# Install development tools
install_dev_tools() {
    log "Installing development tools..."

    # Install CocoaPods
    if ! command -v pod &> /dev/null; then
        log "Installing CocoaPods..."
        sudo gem install cocoapods
    fi

    # Install SwiftLint
    if ! command -v swiftlint &> /dev/null; then
        log "Installing SwiftLint..."
        brew install swiftlint
    fi

    # Install SwiftFormat
    if ! command -v swiftformat &> /dev/null; then
        log "Installing SwiftFormat..."
        brew install swiftformat
    fi

    # Install Fastlane
    if ! command -v fastlane &> /dev/null; then
        log "Installing Fastlane..."
        brew install fastlane
    fi

    success "Development tools installed"
}

# Install CocoaPods dependencies
install_cocoapods() {
    log "Installing CocoaPods dependencies..."

    if [ -f "Podfile" ]; then
        pod install --repo-update
        success "CocoaPods dependencies installed"
    else
        warning "Podfile not found, skipping CocoaPods installation"
    fi
}

# Setup Swift Package Manager
setup_spm() {
    log "Setting up Swift Package Manager..."

    # SPM dependencies are managed through Xcode
    # This will be handled when opening the project
    success "Swift Package Manager ready"
}

# Main installation process
main() {
    log "Starting NeuroNexa dependency installation..."

    check_prerequisites
    install_dev_tools
    install_cocoapods
    setup_spm

    success "All dependencies installed successfully!"

    log "Next steps:"
    echo "1. Open NeuroNexa.xcworkspace in Xcode"
    echo "2. Add Swift Package Manager dependencies through Xcode"
    echo "3. Build and run the project"
}

main "$@"
```

### ✅ **Dependency Update Script**
```bash
#!/bin/bash
# Scripts/update_dependencies.sh

set -e

log() {
    echo -e "\033[0;34m[$(date +'%H:%M:%S')]\033[0m $1"
}

success() {
    echo -e "\033[0;32m[SUCCESS]\033[0m $1"
}

# Update CocoaPods
update_cocoapods() {
    log "Updating CocoaPods dependencies..."

    if [ -f "Podfile" ]; then
        pod update
        success "CocoaPods dependencies updated"
    fi
}

# Update development tools
update_dev_tools() {
    log "Updating development tools..."

    brew update
    brew upgrade swiftlint swiftformat fastlane

    success "Development tools updated"
}

main() {
    log "Updating NeuroNexa dependencies..."

    update_dev_tools
    update_cocoapods

    success "All dependencies updated!"

    log "Remember to update Swift Package Manager dependencies through Xcode"
}

main "$@"
```

---

## 📊 **Dependency Management Best Practices**

### ✅ **Version Management**
- **Semantic Versioning**: Use `~>` for minor updates, exact versions for critical dependencies
- **Lock Files**: Commit `Podfile.lock` and `Package.resolved` to ensure consistent builds
- **Regular Updates**: Monthly dependency updates with thorough testing
- **Security Monitoring**: Use tools like `bundle audit` and GitHub security alerts

### ✅ **Performance Considerations**
- **Bundle Size**: Monitor app size impact of each dependency
- **Build Time**: Prefer Swift Package Manager for faster builds
- **Runtime Performance**: Profile dependencies for performance impact
- **Memory Usage**: Monitor memory footprint of heavy dependencies

### ✅ **Compatibility Matrix**
| Dependency | iOS 26 | watchOS 26 | Swift 6.0 | Status |
|------------|--------|------------|-----------|---------|
| Alamofire | ✅ | ✅ | ✅ | Fully Compatible |
| Firebase | ✅ | ⚠️ | ✅ | Limited watchOS |
| SwiftUI-Introspect | ✅ | ❌ | ✅ | iOS Only |
| Lottie | ✅ | ✅ | ✅ | Fully Compatible |
| Sentry | ✅ | ✅ | ✅ | Fully Compatible |

---

*Dependencies Configuration v1.0 - Updated July 2, 2025*
