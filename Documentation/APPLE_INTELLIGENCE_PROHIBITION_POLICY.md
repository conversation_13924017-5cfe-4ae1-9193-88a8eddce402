# Apple Intelligence Prohibition Policy
**NeuroNexa iOS 26 Project**

## 🚫 **STRICT PROHIBITION: NO APPLE INTELLIGENCE INTEGRATIONS**

### Policy Statement
The NeuroNexa project has made a strategic decision to use **OpenAI exclusively** for all AI-powered features. Apple Intelligence integrations are **STRICTLY PROHIBITED** in this codebase.

### Rationale
1. **Consistency**: All AI features use OpenAI for unified behavior and performance
2. **Control**: OpenAI provides more granular control over AI responses for neurodiversity-first design
3. **Customization**: OpenAI allows custom prompts optimized for neurodivergent users
4. **Reliability**: OpenAI service availability is more predictable than Apple Intelligence
5. **Testing**: OpenAI integration is easier to mock and test in automated test suites

## 🛡️ **Enforcement Mechanisms**

### 1. SwiftLint Custom Rules
Added custom SwiftLint rules to automatically detect and reject Apple Intelligence code:

```yaml
# .swiftlint.yml
custom_rules:
  no_apple_intelligence:
    name: "No Apple Intelligence"
    regex: "(Apple.*Intelligence|AppleIntelligence|import.*Intelligence)"
    message: "Apple Intelligence is prohibited. Use OpenAI implementation instead."
    severity: error
    
  no_apple_intelligence_imports:
    name: "No Apple Intelligence Imports"
    regex: "import.*Intelligence"
    message: "Apple Intelligence imports are prohibited. Use OpenAI services."
    severity: error
```

### 2. Pre-commit Hooks
Git pre-commit hooks automatically scan for Apple Intelligence references:

```bash
#!/bin/bash
# .git/hooks/pre-commit

echo "🔍 Scanning for prohibited Apple Intelligence code..."

# Check for Apple Intelligence references
if grep -r "Apple.*Intelligence\|AppleIntelligence" --include="*.swift" .; then
    echo "❌ COMMIT REJECTED: Apple Intelligence code detected"
    echo "   Use OpenAI implementation instead"
    echo "   See Documentation/APPLE_INTELLIGENCE_PROHIBITION_POLICY.md"
    exit 1
fi

echo "✅ No Apple Intelligence code detected"
```

### 3. CI/CD Pipeline Validation
GitHub Actions workflow includes Apple Intelligence detection:

```yaml
# .github/workflows/apple-intelligence-check.yml
name: Apple Intelligence Prohibition Check
on: [push, pull_request]

jobs:
  check-apple-intelligence:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Check for Apple Intelligence
        run: |
          if grep -r "Apple.*Intelligence\|AppleIntelligence" --include="*.swift" .; then
            echo "❌ Apple Intelligence code detected"
            exit 1
          fi
          echo "✅ No Apple Intelligence code found"
```

### 4. Code Review Guidelines
All code reviews must verify:
- [ ] No Apple Intelligence imports
- [ ] No Apple Intelligence service references
- [ ] All AI features use OpenAI implementation
- [ ] Tests use OpenAI mocks, not Apple Intelligence mocks

## 📋 **Approved AI Implementation**

### ✅ **Use This: OpenAI Integration**
```swift
// ✅ CORRECT: Use OpenAI service
import Foundation

class MyAIService {
    private let openAIService = OpenAITaskCoach()
    
    func generateContent() async throws -> PersonalizedContent {
        return try await openAIService.generatePersonalizedContent(for: context)
    }
}
```

### ❌ **DON'T Use This: Apple Intelligence**
```swift
// ❌ PROHIBITED: Apple Intelligence
import Intelligence  // ❌ FORBIDDEN

class MyAIService {
    private let appleAI = AppleIntelligenceService()  // ❌ FORBIDDEN
    
    func generateContent() async throws -> PersonalizedContent {
        return try await appleAI.generatePersonalizedContent(for: context)  // ❌ FORBIDDEN
    }
}
```

## 🔄 **Migration Completed**

### What Was Removed
- [x] `Core/Services/AppleIntelligenceService.swift` - Deleted
- [x] `AppleIntelligenceServiceProtocol` - Replaced with `OpenAIIntelligenceServiceProtocol`
- [x] All Apple Intelligence service registrations in `DependencyContainer.swift`
- [x] Apple Intelligence references in `BasicServiceImplementations.swift`
- [x] Apple Intelligence references in `NeuroNexaServices.swift`
- [x] Apple Intelligence mocks in test files
- [x] All "Apple Intelligence" comments and documentation

### What Was Added
- [x] Enhanced `OpenAITaskCoach` with full Apple Intelligence replacement
- [x] `OpenAIIntelligenceServiceProtocol` with all required methods
- [x] OpenAI-powered implementations for:
  - Personalized content generation
  - User behavior analysis
  - User needs prediction
  - Cognitive load UI adaptation
  - Break suggestions
- [x] Comprehensive error handling with `OpenAIIntelligenceError`
- [x] Updated test mocks to use OpenAI services

## 🚨 **Violation Response Protocol**

### If Apple Intelligence Code Is Detected:

1. **Immediate Action**:
   - Stop the build/deployment process
   - Reject the commit/pull request
   - Notify the developer of the policy violation

2. **Developer Response Required**:
   - Remove all Apple Intelligence references
   - Replace with OpenAI implementation
   - Update tests to use OpenAI mocks
   - Verify SwiftLint compliance

3. **Code Review**:
   - Extra scrutiny for AI-related changes
   - Verify OpenAI implementation follows neurodiversity-first principles
   - Ensure proper error handling and fallbacks

## 📚 **Developer Resources**

### OpenAI Implementation Guide
- **Service**: Use `OpenAITaskCoach` class
- **Protocol**: Implement `OpenAIIntelligenceServiceProtocol`
- **Error Handling**: Use `OpenAIIntelligenceError` enum
- **Testing**: Use `MockOpenAIIntelligenceService`

### Key Files for AI Features
- `Core/Services/AI/OpenAITaskCoach.swift` - Main OpenAI service
- `Core/Architecture/DependencyContainer.swift` - Service registration
- `Services/AI/AITaskCoachService.swift` - AI task coaching implementation
- `Tests/Services/AITaskCoachServiceTests.swift` - Test examples

### Neurodiversity-First AI Prompts
All OpenAI prompts must follow neurodiversity-first principles:
- Use clear, direct language
- Avoid pathologizing terminology
- Focus on strengths and accommodations
- Provide specific, actionable guidance
- Include positive reinforcement

## 🎯 **Success Metrics**

### Policy Compliance
- [ ] 0% Apple Intelligence references in codebase
- [ ] 100% AI features use OpenAI implementation
- [ ] 100% SwiftLint compliance with custom rules
- [ ] All CI/CD checks passing
- [ ] All tests using OpenAI mocks

### Quality Assurance
- [ ] All AI features maintain neurodiversity-first approach
- [ ] OpenAI integration provides equivalent or better functionality
- [ ] Performance metrics maintained or improved
- [ ] User experience remains consistent or enhanced

---

**Policy Status**: ✅ **ACTIVE AND ENFORCED**
**Last Updated**: 2025-01-08
**Next Review**: 2025-04-08
**Compliance**: 100% Apple Intelligence Free
