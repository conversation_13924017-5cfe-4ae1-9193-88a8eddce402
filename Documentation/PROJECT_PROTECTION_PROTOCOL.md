# 🛡️ NeuroNexa Project Protection Protocol

**Document Version**: 1.0  
**Created**: July 6, 2025  
**Last Updated**: July 6, 2025  
**Status**: ✅ ACTIVE

---

## 🚨 **CRITICAL INCIDENT REFERENCE**

**Date**: July 6, 2025  
**Incident**: Accidental project overwrite during recovery attempt  
**Impact**: Sophisticated NeuroNexa codebase (10,340+ lines) temporarily at risk  
**Resolution**: Successful recovery using GitHub repository backup  

**Root Cause**: Used `scaffold_ios_project_XcodeBuildMCP` on existing project with source code, which overwrote sophisticated app with basic template.

---

## 🔒 **MANDATORY PROTECTION RULES**

### **Rule 1: SOURCE CODE PRESERVATION FIRST**
- **NEVER** use scaffolding tools on projects with existing source code
- **ALWAYS** verify project state before destructive operations
- **REQUIRE** explicit confirmation before any file overwrite operations

### **Rule 2: PRE-BUILD VERIFICATION CHECKLIST**
Before ANY major project operation, complete this checklist:

```markdown
## ✅ PRE-BUILD VERIFICATION CHECKLIST
- [ ] Check git status: `git status`
- [ ] Verify source file count: `find . -name "*.swift" | wc -l`
- [ ] Check project structure: `ls -la | grep -E '\.(xcodeproj|xcworkspace)$'`
- [ ] Review build logs for previous state
- [ ] Confirm no uncommitted critical changes
- [ ] Backup current state if needed: `git add . && git commit -m "Pre-operation backup"`
```

### **Rule 3: SAFE PROJECT CREATION METHODS**
When creating Xcode project structure:

**✅ APPROVED METHODS:**
- `xcodegen generate` (with proper project.yml)
- Manual Xcode project creation
- `swift package init` for Swift packages

**❌ FORBIDDEN METHODS:**
- `scaffold_ios_project_XcodeBuildMCP` on existing projects
- Any scaffolding tool that overwrites existing files
- Direct file replacement without verification

### **Rule 4: BUILD FAILURE INVESTIGATION**
When investigating build failures:

1. **Check build logs first**: Review `Documentation/BUILD_LOG.md`
2. **Check git history**: `git log --oneline -10`
3. **Verify source integrity**: Count files and check key components
4. **Use safe diagnostic methods**: Read-only operations only
5. **Document findings**: Update build log with investigation results

---

## 🔧 **RECOVERY PROCEDURES**

### **If Project Structure Missing:**
1. ✅ Verify source files intact: `find . -name "*.swift" | wc -l`
2. ✅ Create project.yml for xcodegen
3. ✅ Run `xcodegen generate`
4. ✅ Test build without modifying source files

### **If Source Code Corrupted:**
1. ✅ Check git status and recent commits
2. ✅ Restore from GitHub repository if needed
3. ✅ Verify file integrity after restoration
4. ✅ Document incident in build log

### **If Build Fails:**
1. ✅ Check SwiftLint violations (acceptable)
2. ✅ Check compilation errors (investigate)
3. ✅ Review dependency issues
4. ✅ Use MCP tools for systematic debugging

---

## 📊 **SUCCESS METRICS**

### **Project Integrity Verification:**
- **Source Files**: Should maintain 37 Swift files
- **Code Lines**: Should maintain 10,340+ lines
- **Core Features**: All 8 screens and neurodiversity features
- **Architecture**: MVVM-C pattern preserved
- **Build Status**: Compiles successfully (SwiftLint warnings acceptable)

### **Protection Protocol Compliance:**
- ✅ Pre-build checklist completed
- ✅ Safe methods used for project operations
- ✅ Source code integrity verified
- ✅ Build logs updated with all changes
- ✅ Git history maintained

---

## 🎯 **IMPLEMENTATION GUIDELINES**

### **For Augment Agent:**
1. **Always reference this protocol** before major project operations
2. **Complete verification checklist** before destructive operations
3. **Use only approved methods** for project creation/modification
4. **Document all incidents** in build log immediately
5. **Prioritize source code preservation** above all other considerations

### **For Development Workflow:**
1. **Regular git commits** to maintain recovery points
2. **Build log updates** for all significant changes
3. **MCP tools integration** for systematic project management
4. **SwiftLint compliance** for code quality maintenance
5. **Continuous verification** of project integrity

---

## 📝 **INCIDENT REPORTING TEMPLATE**

```markdown
## 🚨 PROJECT PROTECTION INCIDENT

**Date**: [Date]
**Time**: [Time]
**Incident Type**: [Description]
**Impact Level**: [Low/Medium/High/Critical]

### Root Cause:
[Detailed explanation]

### Actions Taken:
1. [Action 1]
2. [Action 2]
3. [Action 3]

### Resolution:
[How the issue was resolved]

### Prevention Measures:
[What was implemented to prevent recurrence]

### Lessons Learned:
[Key takeaways for future development]
```

---

**This protocol is MANDATORY for all NeuroNexa development operations.**  
**Violation of these rules may result in critical data loss.**
