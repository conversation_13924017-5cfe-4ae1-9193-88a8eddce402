# 🎨 NeuroNexa UI/UX Design System Library

**Version:** 1.0  
**Created:** July 2, 2025  
**iOS Target:** 26.0+  
**Design Philosophy:** Neurodiversity-First  
**Accessibility Standard:** WCAG 2.2 AAA  

---

## 🧠 **Design Philosophy**

NeuroNexa's design system is built on **Neurodiversity-First** principles, recognizing that cognitive differences are natural variations that require thoughtful, adaptive design solutions.

### ✅ **Core Principles**
1. **Cognitive Load Optimization** - Reduce mental effort required to use the app
2. **Sensory Adaptation** - Accommodate sensory sensitivities and preferences
3. **Executive Function Support** - Assist with planning, organization, and task management
4. **Predictable Interactions** - Consistent, learnable interface patterns
5. **Flexible Customization** - User-controlled adaptation to individual needs

---

## 🎯 **Target User Profiles**

### **ADHD Users**
- **Needs**: Focus assistance, distraction reduction, dopamine-friendly interactions
- **Challenges**: Attention regulation, task switching, time perception
- **Solutions**: Clear visual hierarchy, progress indicators, gentle notifications

### **Autism Spectrum Users**
- **Needs**: Predictable interfaces, sensory control, routine support
- **Challenges**: Sensory overload, change adaptation, social communication
- **Solutions**: Consistent layouts, sensory preferences, clear feedback

### **Executive Dysfunction Users**
- **Needs**: Task breakdown, memory support, decision assistance
- **Challenges**: Planning, organization, working memory, initiation
- **Solutions**: Step-by-step guidance, visual cues, cognitive scaffolding

---

## 🎨 **Visual Design System**

### ✅ **Color Palette**

#### **Primary Colors**
```swift
struct NeuroNexaColors {
    // Calming primary palette
    static let primaryBlue = Color(red: 0.2, green: 0.4, blue: 0.8)      // #3366CC
    static let primaryGreen = Color(red: 0.2, green: 0.7, blue: 0.4)     // #33B366
    static let primaryPurple = Color(red: 0.5, green: 0.3, blue: 0.8)    // #804DCC
    
    // Neutral palette
    static let neutralGray = Color(red: 0.5, green: 0.5, blue: 0.5)      // #808080
    static let lightGray = Color(red: 0.9, green: 0.9, blue: 0.9)        // #E6E6E6
    static let darkGray = Color(red: 0.2, green: 0.2, blue: 0.2)         // #333333
    
    // Semantic colors
    static let successGreen = Color(red: 0.1, green: 0.8, blue: 0.3)     // #1ACC4D
    static let warningOrange = Color(red: 1.0, green: 0.6, blue: 0.0)    // #FF9900
    static let errorRed = Color(red: 0.9, green: 0.2, blue: 0.2)         // #E63333
    
    // Cognitive load adaptive colors
    static func adaptiveColor(for cognitiveLoad: CognitiveLoadLevel) -> Color {
        switch cognitiveLoad {
        case .low: return primaryBlue.opacity(0.6)
        case .medium: return primaryBlue.opacity(0.8)
        case .high: return primaryBlue
        case .overload: return neutralGray
        }
    }
}
```

#### **Accessibility Color Contrast**
```swift
enum ColorContrast: CaseIterable {
    case standard    // 4.5:1 ratio
    case enhanced    // 7:1 ratio
    case maximum     // 21:1 ratio (black/white only)
    
    var textColor: Color {
        switch self {
        case .standard: return .primary
        case .enhanced: return .black
        case .maximum: return .black
        }
    }
    
    var backgroundColor: Color {
        switch self {
        case .standard: return .white
        case .enhanced: return Color(white: 0.95)
        case .maximum: return .white
        }
    }
}
```

### ✅ **Typography System**

#### **Font Hierarchy**
```swift
struct NeuroNexaFonts {
    // Dyslexia-friendly font stack
    static let primaryFont = "SF Pro Display"  // iOS system font
    static let readingFont = "OpenDyslexic"    // Optional dyslexia-friendly
    static let monoFont = "SF Mono"            // Code/data display
    
    // Size scale (1.25 ratio for clear hierarchy)
    static let title1 = Font.system(size: 32, weight: .bold, design: .default)
    static let title2 = Font.system(size: 26, weight: .semibold, design: .default)
    static let title3 = Font.system(size: 20, weight: .medium, design: .default)
    static let headline = Font.system(size: 18, weight: .semibold, design: .default)
    static let body = Font.system(size: 16, weight: .regular, design: .default)
    static let callout = Font.system(size: 14, weight: .medium, design: .default)
    static let caption = Font.system(size: 12, weight: .regular, design: .default)
    
    // Cognitive load adaptive sizing
    static func adaptiveFont(base: Font, cognitiveLoad: CognitiveLoadLevel) -> Font {
        let scaleFactor: CGFloat = switch cognitiveLoad {
        case .low: 0.9
        case .medium: 1.0
        case .high: 1.1
        case .overload: 1.2
        }
        
        return base.weight(.medium) // Slightly bolder for better readability
    }
}
```

### ✅ **Spacing System**

#### **Consistent Spacing Scale**
```swift
struct NeuroNexaSpacing {
    static let xs: CGFloat = 4      // 4pt
    static let sm: CGFloat = 8      // 8pt
    static let md: CGFloat = 16     // 16pt
    static let lg: CGFloat = 24     // 24pt
    static let xl: CGFloat = 32     // 32pt
    static let xxl: CGFloat = 48    // 48pt
    
    // Cognitive load adaptive spacing
    static func adaptiveSpacing(base: CGFloat, cognitiveLoad: CognitiveLoadLevel) -> CGFloat {
        let multiplier: CGFloat = switch cognitiveLoad {
        case .low: 0.8
        case .medium: 1.0
        case .high: 1.2
        case .overload: 1.5
        }
        
        return base * multiplier
    }
}
```

---

## 🧩 **Component Library**

### ✅ **Buttons**

#### **Primary Action Button**
```swift
struct NeuroNexaPrimaryButton: View {
    let title: String
    let action: () -> Void
    let isLoading: Bool = false
    
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPrefs
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: NeuroNexaSpacing.sm) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                }
                
                Text(title)
                    .font(NeuroNexaFonts.adaptiveFont(base: .headline, cognitiveLoad: cognitiveLoad))
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding(.horizontal, NeuroNexaSpacing.lg)
            .padding(.vertical, NeuroNexaSpacing.md)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(NeuroNexaColors.adaptiveColor(for: cognitiveLoad))
                    .shadow(
                        color: .black.opacity(sensoryPrefs.shadowIntensity),
                        radius: 4,
                        x: 0,
                        y: 2
                    )
            )
        }
        .disabled(isLoading)
        .scaleEffect(isLoading ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
        .accessibilityLabel(title)
        .accessibilityHint("Double tap to \(title.lowercased())")
        .accessibilityAddTraits(.isButton)
    }
}
```

#### **Secondary Button**
```swift
struct NeuroNexaSecondaryButton: View {
    let title: String
    let action: () -> Void
    
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(NeuroNexaFonts.adaptiveFont(base: .callout, cognitiveLoad: cognitiveLoad))
                .fontWeight(.medium)
                .foregroundColor(NeuroNexaColors.primaryBlue)
                .padding(.horizontal, NeuroNexaSpacing.md)
                .padding(.vertical, NeuroNexaSpacing.sm)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(NeuroNexaColors.primaryBlue, lineWidth: 1.5)
                        .background(Color.clear)
                )
        }
        .accessibilityLabel(title)
        .accessibilityAddTraits(.isButton)
    }
}
```

### ✅ **Cards & Containers**

#### **Task Card Component**
```swift
struct NeuroNexaTaskCard: View {
    let task: AITask
    @Binding var isCompleted: Bool
    
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: NeuroNexaSpacing.md) {
            // Header with priority and completion
            HStack {
                PriorityIndicator(priority: task.priority)
                
                Text(task.title)
                    .font(NeuroNexaFonts.adaptiveFont(base: .headline, cognitiveLoad: cognitiveLoad))
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                Spacer()
                
                CompletionCheckbox(isCompleted: $isCompleted)
            }
            
            // Description (if available)
            if !task.description.isEmpty {
                Text(task.description)
                    .font(NeuroNexaFonts.body)
                    .foregroundColor(.secondary)
                    .lineLimit(cognitiveLoad.descriptionLineLimit)
            }
            
            // Metadata row
            HStack {
                EstimatedTimeChip(duration: task.estimatedDuration)
                
                Spacer()
                
                CognitiveLoadIndicator(level: task.cognitiveLoad)
            }
            
            // Progress bar
            if task.progress > 0 {
                ProgressView(value: task.progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: NeuroNexaColors.primaryGreen))
                    .scaleEffect(y: 2.0) // Thicker progress bar
            }
        }
        .padding(NeuroNexaSpacing.lg)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(
                    color: .black.opacity(0.08),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(task.priority.color.opacity(0.3), lineWidth: 2)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            // Handle task tap
        }
        .onLongPressGesture(
            minimumDuration: 0.1,
            maximumDistance: 50
        ) { _ in
            isPressed = true
        } onPressingChanged: { pressing in
            isPressed = pressing
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(task.title), \(task.priority.accessibilityLabel)")
        .accessibilityValue(isCompleted ? "Completed" : "Not completed")
        .accessibilityHint("Double tap to view details, long press for options")
    }
}
```

### ✅ **Input Components**

#### **Accessible Text Field**
```swift
struct NeuroNexaTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let isRequired: Bool = false
    
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @FocusState private var isFocused: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: NeuroNexaSpacing.sm) {
            HStack {
                Text(title)
                    .font(NeuroNexaFonts.adaptiveFont(base: .callout, cognitiveLoad: cognitiveLoad))
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                if isRequired {
                    Text("*")
                        .foregroundColor(NeuroNexaColors.errorRed)
                }
            }
            
            TextField(placeholder, text: $text)
                .font(NeuroNexaFonts.adaptiveFont(base: .body, cognitiveLoad: cognitiveLoad))
                .padding(NeuroNexaSpacing.md)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemGray6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(
                                    isFocused ? NeuroNexaColors.primaryBlue : Color.clear,
                                    lineWidth: 2
                                )
                        )
                )
                .focused($isFocused)
                .accessibilityLabel(title)
                .accessibilityHint(isRequired ? "Required field" : "Optional field")
        }
    }
}
```

---

## 🎭 **Animation & Motion**

### ✅ **Motion Sensitivity Levels**
```swift
enum MotionSensitivity: CaseIterable {
    case none       // No animations
    case reduced    // Minimal, essential animations only
    case standard   // Normal animations
    case enhanced   // Rich, engaging animations
    
    var animationDuration: Double {
        switch self {
        case .none: return 0.0
        case .reduced: return 0.1
        case .standard: return 0.3
        case .enhanced: return 0.5
        }
    }
    
    var springResponse: Double {
        switch self {
        case .none: return 0.0
        case .reduced: return 0.2
        case .standard: return 0.4
        case .enhanced: return 0.6
        }
    }
}
```

### ✅ **Cognitive Load Animations**
```swift
struct CognitiveLoadAnimation: ViewModifier {
    let cognitiveLoad: CognitiveLoadLevel
    let motionSensitivity: MotionSensitivity
    
    func body(content: Content) -> some View {
        content
            .animation(
                .easeInOut(duration: motionSensitivity.animationDuration),
                value: cognitiveLoad
            )
            .transition(
                .asymmetric(
                    insertion: .scale.combined(with: .opacity),
                    removal: .opacity
                )
            )
    }
}

extension View {
    func cognitiveLoadAnimated(
        cognitiveLoad: CognitiveLoadLevel,
        motionSensitivity: MotionSensitivity
    ) -> some View {
        modifier(CognitiveLoadAnimation(
            cognitiveLoad: cognitiveLoad,
            motionSensitivity: motionSensitivity
        ))
    }
}
```

---

## 📱 **Responsive Design**

### ✅ **Device Adaptation**
```swift
struct DeviceAdaptation {
    static func spacing(for sizeClass: UserInterfaceSizeClass?) -> CGFloat {
        switch sizeClass {
        case .compact: return NeuroNexaSpacing.sm
        case .regular: return NeuroNexaSpacing.md
        case .none: return NeuroNexaSpacing.md
        @unknown default: return NeuroNexaSpacing.md
        }
    }
    
    static func columns(for sizeClass: UserInterfaceSizeClass?) -> Int {
        switch sizeClass {
        case .compact: return 1
        case .regular: return 2
        case .none: return 1
        @unknown default: return 1
        }
    }
}
```

---

*UI/UX Design System v1.0 - Neurodiversity-First Design*
