# Claude Code Integration Workflow for NeuroNexa

## Overview
This document defines the comprehensive workflow for integrating Claude Code as a secondary agent with controlled write capabilities under Augment Code supervision for the NeuroNexa iOS 26 project.

## Agent Hierarchy
```
Augment Code (PRIMARY AGENT)
    ├── Full write access
    ├── Task management
    ├── Git operations
    ├── Architecture decisions
    └── Final validation
        │
        └── Claude Code (SECONDARY AGENT - CONTROLLED WRITE)
            ├── Controlled write access (approved categories)
            ├── Code analysis & implementation
            ├── Diff preview generation
            ├── Approval workflow compliance
            └── Change logging & rollback capability
```

## 1. Setup and Configuration

### Initial Setup Commands
```bash
# Install Claude Code CLI
npm install -g @anthropic-ai/claude-code

# Initialize in project
claude init

# Verify installation
claude --version
```

### Configuration Files
- `.claude-config.json` - Project-specific Claude Code configuration
- `AUGMENT_RULES.md` - Integration rules and permissions
- `.claudeignore` - Files to exclude from Claude Code analysis

## 2. Workflow Phases

### Phase 1: Augment Code Initiation
```bash
# Augment Code starts review session
claude review --scope="UI/Views/Breathing/" --focus="accessibility"
```

### Phase 2: Claude Code Analysis
- Claude Code analyzes specified files
- Generates suggestions with `// Claude:` prefix
- Identifies optimization opportunities
- Flags potential issues

### Phase 3: Suggestion Review
- Augment Code reviews Claude suggestions
- Validates against project requirements
- Approves or rejects recommendations
- Implements approved changes

### Phase 4: Validation
- Augment Code runs tests
- Verifies SwiftLint compliance
- Confirms accessibility standards
- Updates documentation

## 3. Command Reference

### Review Commands
```bash
# Full project review
claude review --comprehensive

# Specific component review
claude review --path="UI/Views/AITaskCoach/" --focus="performance"

# Accessibility audit
claude review --accessibility --neurodiversity-check

# SwiftUI optimization
claude review --swiftui-optimize --path="UI/Views/"

# ViewModel analysis
claude review --viewmodel-logic --path="UI/ViewModels/"
```

### Analysis Commands
```bash
# Performance analysis
claude analyze --performance --memory-usage

# Code quality check
claude analyze --quality --swiftlint-compliance

# Test coverage analysis
claude analyze --test-coverage --gaps

# Documentation review
claude analyze --documentation --completeness
```

### Suggestion Commands
```bash
# Generate refactoring suggestions
claude suggest --refactor --path="Services/AI/"

# Optimization recommendations
claude suggest --optimize --target="memory,performance"

# Accessibility improvements
claude suggest --accessibility --wcag-aaa
```

## 4. Integration Patterns

### Pattern 1: SwiftUI View Optimization
```swift
// Original code
struct BreathingView: View {
    @StateObject private var viewModel = BreathingViewModel()
    
    var body: some View {
        VStack {
            // Complex view hierarchy
        }
    }
}

// Claude: Consider extracting subviews for better performance and readability
// Claude: Recommend using @ObservedObject instead of @StateObject for injected dependencies
// Claude: Suggest implementing accessibility identifiers for testing
```

### Pattern 2: ViewModel Logic Review
```swift
// Original code
class BreathingViewModel: ObservableObject {
    @Published var isActive = false
    
    func startSession() {
        // Complex logic
    }
}

// Claude: Consider separating business logic into service layer
// Claude: Recommend adding error handling for async operations
// Claude: Suggest implementing proper cancellation for long-running tasks
```

### Pattern 3: Accessibility Enhancement
```swift
// Original code
Button("Start") {
    startBreathing()
}

// Claude: Add accessibility label for screen readers
// Claude: Consider haptic feedback for neurodiversity support
// Claude: Recommend voice control compatibility
```

## 5. Quality Gates

### Pre-Review Checklist
- [ ] Environment validation passed
- [ ] SwiftLint compliance at 100%
- [ ] All tests passing
- [ ] No compilation errors

### Post-Review Validation
- [ ] Claude suggestions reviewed by Augment
- [ ] Approved changes implemented
- [ ] Tests updated and passing
- [ ] Documentation updated
- [ ] SwiftLint compliance maintained

## 6. Escalation Protocols

### Automatic Escalation Triggers
- Security vulnerabilities detected
- Performance degradation > 10%
- Accessibility compliance failures
- Architecture pattern violations
- Neurodiversity design conflicts

### Manual Escalation Process
1. Claude identifies complex issue
2. Claude flags for Augment review
3. Augment evaluates suggestion
4. Augment makes final decision
5. Implementation by Augment only

## 7. Monitoring and Metrics

### Success Metrics
- Code quality improvements
- Performance optimizations implemented
- Accessibility compliance maintained
- SwiftLint violations reduced
- Test coverage increased

### Tracking
- Review session logs
- Suggestion acceptance rate
- Implementation success rate
- Quality improvement metrics
- Time savings achieved

## 8. Best Practices

### For Augment Code
- Clearly define review scope
- Provide context for Claude analysis
- Review all suggestions thoroughly
- Maintain final authority on changes
- Document decisions and rationale

### For Claude Code
- Focus on specified areas only
- Provide clear, actionable suggestions
- Include rationale for recommendations
- Respect read-only limitations
- Escalate complex decisions

## 9. Troubleshooting

### Common Issues
- Authentication failures
- Permission errors
- Configuration conflicts
- Analysis timeouts

### Solutions
- Re-authenticate with `claude auth`
- Check `.claude-config.json` settings
- Verify file permissions
- Adjust analysis scope

## 10. Future Enhancements

### Planned Improvements
- Automated suggestion prioritization
- Integration with CI/CD pipeline
- Custom rule definitions
- Performance benchmarking
- Accessibility scoring automation
