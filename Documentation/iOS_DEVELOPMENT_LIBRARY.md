# 📱 NeuroNexa iOS Development Library & Guidelines

## 🎯 **Project Overview**
**NeuroNexa** - AI-powered productivity assistant for neurodiverse users (ADHD, Autism Spectrum, executive dysfunction)
- **Target Platform**: Native iOS (iPhone + Apple Watch)
- **iOS Version**: iOS 16.0+
- **Xcode Version**: Xcode 16
- **Architecture**: MVVM-C (Model-View-ViewModel-Coordinator)
- **Language**: Swift 5.9+
- **UI Framework**: SwiftUI + UIKit (hybrid approach)

---

## 🏗️ **Architecture & Design Patterns**

### **MVVM-C Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Coordinator   │────│   ViewController │────│      View       │
│   (Navigation)  │    │   (UIKit/SwiftUI)│    │   (SwiftUI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   ViewModel     │────│     Model       │
                       │ (Business Logic)│    │ (Data Entities) │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Repository    │
                       │ (Data Sources)  │
                       └─────────────────┘
```

### **Core Components**
1. **Models**: Data entities and business objects
2. **Views**: SwiftUI views with accessibility-first design
3. **ViewModels**: ObservableObject classes handling business logic
4. **Coordinators**: Navigation and flow management
5. **Repositories**: Data access layer abstraction
6. **Services**: External API communication and utilities

---

## 📁 **Project Structure**

```
NeuroNexa/
├── App/
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   └── NeuroNexaApp.swift
├── Core/
│   ├── Architecture/
│   │   ├── Coordinator.swift
│   │   ├── ViewModel.swift
│   │   └── Repository.swift
│   ├── Extensions/
│   ├── Utilities/
│   ├── Constants/
│   └── Networking/
├── Features/
│   ├── Authentication/
│   ├── Dashboard/
│   ├── AITaskCoach/
│   ├── BreathingExercises/
│   ├── RoutineBuilder/
│   ├── Settings/
│   └── Onboarding/
├── Shared/
│   ├── Components/
│   ├── Styles/
│   ├── Resources/
│   └── Models/
├── Services/
│   ├── AIService/
│   ├── HealthKitService/
│   ├── NotificationService/
│   └── DataPersistence/
├── WatchApp/
│   ├── Views/
│   ├── ViewModels/
│   └── Complications/
└── Tests/
    ├── UnitTests/
    ├── IntegrationTests/
    └── UITests/
```

---

## 🛠️ **Technology Stack**

### **Core Frameworks**
- **SwiftUI**: Primary UI framework for modern, declarative interfaces
- **UIKit**: Legacy components and complex navigation
- **Combine**: Reactive programming and data binding
- **Core Data**: Local data persistence
- **CloudKit**: iCloud synchronization
- **HealthKit**: Health and wellness data integration
- **WatchKit**: Apple Watch companion app

### **Third-Party Dependencies**
- **Alamofire**: HTTP networking
- **KeychainAccess**: Secure credential storage
- **Lottie**: Animations and micro-interactions
- **Charts**: Data visualization
- **OpenAI Swift**: AI integration

### **Development Tools**
- **SwiftLint**: Code style enforcement
- **SwiftFormat**: Automatic code formatting
- **Fastlane**: CI/CD automation
- **XCTest**: Unit and UI testing
- **Instruments**: Performance profiling

---

## 🎨 **Design System**

### **Color Palette (Neurodiversity-Focused)**
```swift
struct NeuroNexaColors {
    // Primary Colors
    static let primaryBlue = Color(hex: "#4A90E2")
    static let primaryGreen = Color(hex: "#7ED321")
    static let primaryPurple = Color(hex: "#9013FE")
    
    // Calming Colors
    static let calmingTeal = Color(hex: "#50E3C2")
    static let softLavender = Color(hex: "#B19CD9")
    static let warmBeige = Color(hex: "#F5F5DC")
    
    // Status Colors
    static let successGreen = Color(hex: "#4CAF50")
    static let warningOrange = Color(hex: "#FF9800")
    static let errorRed = Color(hex: "#F44336")
    static let focusYellow = Color(hex: "#FFC107")
}
```

### **Typography**
```swift
struct NeuroNexaFonts {
    static let largeTitle = Font.system(size: 34, weight: .bold, design: .rounded)
    static let title1 = Font.system(size: 28, weight: .semibold, design: .rounded)
    static let title2 = Font.system(size: 22, weight: .semibold, design: .rounded)
    static let headline = Font.system(size: 17, weight: .semibold, design: .rounded)
    static let body = Font.system(size: 17, weight: .regular, design: .rounded)
    static let callout = Font.system(size: 16, weight: .regular, design: .rounded)
    static let caption = Font.system(size: 12, weight: .regular, design: .rounded)
}
```

### **Spacing System**
```swift
struct Spacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
}
```

---

## 🔒 **Security & Privacy**

### **HIPAA Compliance Requirements**
1. **Data Encryption**: AES-256 encryption for all sensitive data
2. **Secure Storage**: Keychain for credentials, encrypted Core Data
3. **Network Security**: Certificate pinning, TLS 1.3
4. **Access Controls**: Biometric authentication, session management
5. **Audit Logging**: Comprehensive activity tracking
6. **Data Minimization**: Collect only necessary information

### **Privacy Implementation**
```swift
// Example: Secure data storage
class SecureDataManager {
    private let keychain = Keychain(service: "com.neuronexa.app")
    
    func storeSecurely<T: Codable>(_ data: T, key: String) throws {
        let encoded = try JSONEncoder().encode(data)
        try keychain.set(encoded, key: key)
    }
    
    func retrieveSecurely<T: Codable>(_ type: T.Type, key: String) throws -> T? {
        guard let data = try keychain.getData(key) else { return nil }
        return try JSONDecoder().decode(type, from: data)
    }
}
```

---

## ♿ **Accessibility Standards**

### **Neurodiversity-First Design Principles**
1. **Cognitive Load Reduction**: Simplified interfaces, clear hierarchy
2. **Sensory Considerations**: Reduced motion options, high contrast
3. **Customizable Experience**: Adjustable text size, color themes
4. **Clear Communication**: Simple language, visual cues
5. **Flexible Interaction**: Multiple input methods, generous touch targets

### **Implementation Guidelines**
```swift
// Example: Accessible button implementation
struct AccessibleButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.headline)
                .foregroundColor(.white)
                .frame(minHeight: 44) // Minimum touch target
                .padding(.horizontal, Spacing.md)
                .background(NeuroNexaColors.primaryBlue)
                .cornerRadius(12)
        }
        .accessibilityLabel(title)
        .accessibilityHint("Double tap to \(title.lowercased())")
        .accessibilityAddTraits(.isButton)
    }
}
```

---

## 🧪 **Testing Strategy**

### **Testing Pyramid**
1. **Unit Tests (70%)**: ViewModels, business logic, utilities
2. **Integration Tests (20%)**: Repository interactions, API calls
3. **UI Tests (10%)**: Critical user flows, accessibility

### **Testing Tools & Frameworks**
- **XCTest**: Native testing framework
- **Quick/Nimble**: BDD-style testing
- **Mockingbird**: Mock generation
- **Accessibility Inspector**: Accessibility testing

---

## 🚀 **Performance Optimization**

### **Key Performance Metrics**
- **App Launch Time**: < 2 seconds cold start
- **Memory Usage**: < 150MB average
- **Battery Impact**: Minimal background processing
- **Network Efficiency**: Optimized API calls, caching

### **Optimization Techniques**
1. **Lazy Loading**: On-demand content loading
2. **Image Optimization**: WebP format, progressive loading
3. **Core Data Optimization**: Batch operations, fetch limits
4. **Background Processing**: Efficient task scheduling

---

## 📊 **Analytics & Monitoring**

### **Key Metrics to Track**
- **User Engagement**: Session duration, feature usage
- **Performance**: Crash rates, load times
- **Accessibility**: VoiceOver usage, text scaling
- **Health Integration**: HealthKit data correlation

### **Privacy-Compliant Analytics**
- **Local Analytics**: On-device processing
- **Anonymized Data**: No PII collection
- **User Consent**: Explicit opt-in for data sharing

---

## 🤖 **AI Integration Guidelines**

### **OpenAI Integration**
```swift
class AIService: ObservableObject {
    private let client = OpenAI(apiToken: APIKeys.openAI)

    func generateTaskBreakdown(task: String) async throws -> TaskBreakdown {
        let prompt = """
        Break down this task for someone with ADHD into smaller, manageable steps:
        Task: \(task)

        Provide 3-5 specific, actionable steps with estimated time for each.
        """

        let query = ChatQuery(
            model: .gpt4,
            messages: [.user(.init(content: prompt))],
            temperature: 0.7
        )

        let result = try await client.chats(query: query)
        return try parseTaskBreakdown(from: result.choices.first?.message.content)
    }
}
```

### **Crisis Detection Protocol**
```swift
struct CrisisDetectionService {
    func analyzeMoodPattern(_ entries: [MoodEntry]) -> CrisisRisk {
        let recentEntries = entries.suffix(7) // Last 7 days
        let averageMood = recentEntries.map(\.score).reduce(0, +) / recentEntries.count

        if averageMood < 3.0 && hasConsistentDecline(recentEntries) {
            return .high
        } else if averageMood < 4.0 {
            return .moderate
        }
        return .low
    }

    func triggerCrisisSupport(risk: CrisisRisk) {
        switch risk {
        case .high:
            presentEmergencyContacts()
            scheduleWellnessCheck()
        case .moderate:
            suggestBreathingExercise()
            recommendProfessionalHelp()
        case .low:
            break
        }
    }
}
```

---

## 🏥 **HealthKit Integration**

### **Health Data Types**
```swift
enum NeuroNexaHealthData: CaseIterable {
    case heartRate
    case sleepAnalysis
    case mindfulMinutes
    case stepCount
    case activeEnergyBurned

    var healthKitType: HKQuantityType {
        switch self {
        case .heartRate:
            return HKQuantityType.quantityType(forIdentifier: .heartRate)!
        case .sleepAnalysis:
            return HKCategoryType.categoryType(forIdentifier: .sleepAnalysis)!
        case .mindfulMinutes:
            return HKQuantityType.quantityType(forIdentifier: .appleExerciseTime)!
        case .stepCount:
            return HKQuantityType.quantityType(forIdentifier: .stepCount)!
        case .activeEnergyBurned:
            return HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned)!
        }
    }
}
```

### **Health Data Service**
```swift
class HealthDataService: ObservableObject {
    private let healthStore = HKHealthStore()

    func requestAuthorization() async throws {
        let typesToRead = Set(NeuroNexaHealthData.allCases.map(\.healthKitType))
        let typesToWrite = Set([HKQuantityType.quantityType(forIdentifier: .mindfulMinutes)!])

        try await healthStore.requestAuthorization(toShare: typesToWrite, read: typesToRead)
    }

    func fetchHeartRateData(for date: Date) async throws -> [HKQuantitySample] {
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        let predicate = HKQuery.predicateForSamples(withStart: date, end: date.addingTimeInterval(86400))

        return try await withCheckedThrowingContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: heartRateType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: true)]
            ) { _, samples, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume(returning: samples as? [HKQuantitySample] ?? [])
                }
            }
            healthStore.execute(query)
        }
    }
}
```

---

## ⌚ **Apple Watch Development**

### **WatchOS App Structure**
```swift
// WatchApp/NeuroNexaWatchApp.swift
@main
struct NeuroNexaWatchApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(WatchConnectivityManager.shared)
        }
    }
}

// Watch-specific ViewModels
class WatchDashboardViewModel: ObservableObject {
    @Published var currentMood: Int = 5
    @Published var todaysTasks: [Task] = []
    @Published var breathingSessionActive = false

    func startBreathingSession() {
        breathingSessionActive = true
        // Trigger haptic feedback and breathing animation
    }
}
```

### **Watch Connectivity**
```swift
class WatchConnectivityManager: NSObject, ObservableObject, WCSessionDelegate {
    static let shared = WatchConnectivityManager()
    private let session = WCSession.default

    @Published var isReachable = false

    override init() {
        super.init()
        if WCSession.isSupported() {
            session.delegate = self
            session.activate()
        }
    }

    func sendTaskUpdate(_ task: Task) {
        guard session.isReachable else { return }

        let message = [
            "type": "taskUpdate",
            "task": task.toDictionary()
        ]

        session.sendMessage(message, replyHandler: nil) { error in
            print("Failed to send task update: \(error.localizedDescription)")
        }
    }
}
```

---

## 🔄 **State Management**

### **Combine + ObservableObject Pattern**
```swift
class AppState: ObservableObject {
    @Published var user: User?
    @Published var isAuthenticated = false
    @Published var currentTheme: Theme = .system
    @Published var accessibilitySettings = AccessibilitySettings()

    private var cancellables = Set<AnyCancellable>()

    init() {
        setupBindings()
    }

    private func setupBindings() {
        $user
            .map { $0 != nil }
            .assign(to: \.isAuthenticated, on: self)
            .store(in: &cancellables)
    }
}
```

### **Repository Pattern**
```swift
protocol TaskRepository {
    func fetchTasks() async throws -> [Task]
    func createTask(_ task: Task) async throws -> Task
    func updateTask(_ task: Task) async throws -> Task
    func deleteTask(id: UUID) async throws
}

class CoreDataTaskRepository: TaskRepository {
    private let context: NSManagedObjectContext

    init(context: NSManagedObjectContext) {
        self.context = context
    }

    func fetchTasks() async throws -> [Task] {
        let request: NSFetchRequest<TaskEntity> = TaskEntity.fetchRequest()
        let entities = try context.fetch(request)
        return entities.map { Task(from: $0) }
    }
}
```
