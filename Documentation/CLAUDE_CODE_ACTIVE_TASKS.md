# 🎨 Claude Code - ACTIVE TASK ASSIGNMENTS

## 🚨 **IMMEDIATE ACTION REQUIRED**
**Status**: ACTIVE PARALLEL DEVELOPMENT  
**Start Time**: NOW  
**Coordination**: Every 6-10 minutes with Augment Code

---

## 📋 **PRIORITY 1: SwiftUI View Optimization (Next 20 minutes)**

### **Specific File Assignments**:

#### **Task 1A: Main Views Optimization** (10 minutes)
**Files to optimize**:
- `UI/Views/ContentView.swift`
- `UI/Views/DashboardView.swift` 
- `UI/Views/BreathingView.swift`
- `UI/Views/AITaskCoachView.swift`

**Optimization targets**:
- Apply iOS 26 SwiftUI best practices
- Optimize view hierarchies and reduce unnecessary recomposition
- Implement proper state management with @State, @StateObject, @ObservedObject
- Add performance improvements for view rendering

#### **Task 1B: Breathing Views Optimization** (10 minutes)
**Files to optimize**:
- `UI/Views/Breathing/BreathingView.swift`
- `UI/Views/Breathing/BreathingContentViews.swift`
- `UI/Views/Breathing/BreathingSupportingViews.swift`
- `UI/Views/Breathing/BreathingOverlayViews.swift`

**Optimization targets**:
- Optimize animation performance for breathing exercises
- Ensure smooth 60fps animations
- Reduce memory usage during breathing sessions
- Implement proper view lifecycle management

---

## 📋 **PRIORITY 2: Accessibility Enhancement (Next 15 minutes)**

### **Accessibility Audit Tasks**:

#### **Task 2A: Core UI Components** (8 minutes)
**Files to audit**:
- `UI/Components/CognitiveButton.swift`
- `UI/Components/TaskCard.swift`
- `UI/Styles/NeuroNexaDesignSystem.swift`

**Accessibility requirements**:
- Add missing accessibility labels and hints
- Ensure VoiceOver navigation is logical and complete
- Verify color contrast meets WCAG 2.1 AA standards
- Add accessibility actions where appropriate

#### **Task 2B: Settings and Dashboard** (7 minutes)
**Files to audit**:
- `UI/Views/Settings/SettingsView.swift`
- `UI/Views/Dashboard/DashboardView.swift`

**Accessibility requirements**:
- Optimize VoiceOver reading order
- Add semantic accessibility traits
- Ensure all interactive elements are accessible
- Test with Dynamic Type scaling

---

## 📋 **PRIORITY 3: Code Style Consistency** (Next 10 minutes)

### **Style Improvement Tasks**:

#### **Task 3A: SwiftUI Modifiers** (5 minutes)
**Files to improve**:
- `UI/Modifiers/iOS26Extensions.swift`
- `UI/Modifiers/NeurodiversityModifiers.swift`

**Style targets**:
- Apply consistent SwiftUI coding patterns
- Ensure proper view modifier ordering
- Optimize modifier chains for performance

#### **Task 3B: View Model Integration** (5 minutes)
**Files to review**:
- `ViewModels/AITaskCoachViewModel.swift`
- `ViewModels/BreathingViewModel.swift`

**Integration targets**:
- Ensure proper SwiftUI + ViewModel binding patterns
- Optimize @Published property usage
- Verify proper memory management

---

## 🔄 **COORDINATION PROTOCOL**

### **Progress Reporting Schedule**:
- **6 minutes**: Report Task 1A completion status
- **12 minutes**: Report Task 1B completion status  
- **20 minutes**: Report Task 2A completion status
- **27 minutes**: Report Task 2B completion status
- **35 minutes**: Report Task 3 completion status

### **Approval Workflow**:
1. **Claude Code**: Propose specific changes for each file
2. **Augment Code**: Review and approve changes within 2 minutes
3. **Claude Code**: Implement approved changes
4. **Both**: Verify no build issues introduced

### **Communication Format**:
```
CLAUDE CODE UPDATE [X minutes]:
- Task: [Task ID]
- Files: [List of files worked on]
- Changes: [Brief description of optimizations made]
- Issues: [Any problems encountered]
- Next: [Next task to begin]
```

---

## 🎯 **SUCCESS METRICS**

### **Expected Outcomes**:
- **SwiftUI Performance**: 5+ views optimized for iOS 26
- **Accessibility Score**: Measurable WCAG compliance improvement
- **Code Quality**: Consistent SwiftUI patterns applied
- **Build Status**: No new build errors introduced

### **Quality Gates**:
- All changes must maintain existing functionality
- No SwiftLint violations introduced
- Accessibility improvements verified
- Performance improvements measurable

---

## 🚨 **IMMEDIATE START INSTRUCTIONS**

**Claude Code - BEGIN NOW**:
1. Start with `UI/Views/ContentView.swift` optimization
2. Apply iOS 26 SwiftUI best practices
3. Report progress in 6 minutes
4. Continue with assigned file sequence

**Augment Code - PARALLEL WORK**:
- Continue SwiftLint violation fixes
- Monitor Claude Code progress
- Provide approvals within 2 minutes
- Coordinate every 6-10 minutes

---

**STATUS**: 🟢 ACTIVE - PARALLEL DEVELOPMENT IN PROGRESS  
**NEXT UPDATE**: 6 minutes from now
