# NeuroNexa Project Requirements

## 🚨 MANDATORY DEVELOPMENT ENVIRONMENT (ENFORCED)
- **RULE #1**: NeuroNexa project requires EXCLUSIVE use of Xcode Beta 26 and iOS 26 ONLY
- **RULE #2**: NO development permitted on iOS 25.x, iOS 24.x, or any earlier versions
- **RULE #3**: NO development permitted using stable Xcode versions (16.x, 15.x, etc.)
- **ENFORCEMENT**: Automated validation scripts prevent non-compliant development
- **VALIDATION**: `./Scripts/validate-environment.sh` must pass before any development work

## Core Project Requirements
- NeuroNexa project requires mandatory use of Xcode Beta 26 and iOS 26 only, plus watchOS 26 integration support with reference library creation.
- NeuroNexa project requires 100% SwiftLint compliance.
- NeuroNexa project requires neurodiversity-first design principles adherence.
- NeuroNexa project requires systematic task management approach.
- NeuroNexa project requires systematic incremental development approach with task management tools, comprehensive documentation in project reference library, and prioritization of core types/services before refinements to achieve build success.
- NeuroNexa project requires comprehensive testing including accessibility requirements for all features.
- NeuroNexa project goal is to achieve 100% App Store deployment readiness.

# Development Tools and Practices
- NeuroNexa project prefers using Context7 to build reference libraries for iOS Swift development documentation and context.
- NeuroNexa project prefers using MCP tools for development efficiency: SwiftLintMCP for automatic violation fixes, GitHub MCP for repository access and best practices, and tool-assisted development over manual code fixes.

# AI Feature Implementation
- NeuroNexa project uses OpenAI for AI features, not Apple Intelligence.
- Remove all Apple Intelligence integrations and replace with OpenAI implementation.

# Environment Compliance Enforcement
- All development sessions must begin with environment validation
- CI/CD pipelines automatically reject non-compliant builds
- Pre-commit hooks prevent commits with legacy iOS version references
- Documentation must reflect iOS 26/Xcode Beta 26 exclusive requirements
