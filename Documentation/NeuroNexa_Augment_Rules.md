# 📱 Augment Rules for Building iOS Apps (Comprehensive & Extensive)

These Augment Rules define how to systematically and consistently build high-quality, production-grade iOS applications with a focus on performance, accessibility, neurodiversity-first design, modular architecture, and modern Apple standards (iOS 17+ and iOS 26 compatibility).

---

## 🚀 1. **Project Initialization Rules**

- Use the latest stable **Xcode** (or Xcode beta if targeting latest SDKs)
- Initialize project using **App protocol (SwiftUI)** and **MVVM-C** architecture
- Define modules:
  - `Core` (Models, Services)
  - `Views`
  - `ViewModels`
  - `UIComponents`
  - `Extensions`
  - `Resources`
- Create `.env` management system for API keys/secrets
- Configure base project with:
  - `SwiftLint`, `SwiftFormat`
  - `Prettier` for Markdown/docs
  - `Git` with `.gitignore`, `.gitattributes`
  - `README.md`, `CONTRIBUTING.md`

---

## 🧱 2. **Architecture & Modularity Rules**

- Use **MVVM-C** pattern with modular view separation
- Inject all dependencies using **DependencyContainer**
- Abstract all services (e.g., `AuthService`, `HealthService`) with protocols
- Use `@EnvironmentObject`, `@StateObject`, `@ObservedObject` correctly
- Avoid tight coupling between UI and business logic
- Break UI into reusable `ViewComponents` (buttons, headers, cards)

---

## 🎨 3. **Design System & Theming Rules**

- Define a **centralized Design System** in `DesignSystem.swift`
  - Colors, fonts, shadows, spacings, corner radius
  - Light & dark mode support
- Use `@Environment(\.colorScheme)` and `@Environment(\.accessibilityReduceMotion)`
- All views must use `NeuroNexaDesignSystem` (or relevant design kit)
- Provide a minimum of 3 font sizes (Small, Default, Large)
- Implement scalable icons using SF Symbols or Vector assets

---

## 🌐 4. **Network & API Rules**

- All network calls must use:
  - `URLSession` or `Alamofire` (optional)
  - Decodable models
  - Result types and async/await pattern
- Create `NetworkClient.swift` with unified request builder
- Centralize all API endpoints in `APIEndpoints.swift`
- Token management via secure `KeychainService`
- Mock APIs and responses for testing (`MockAPIService`)

---

## 🧠 5. **AI & Context Rules**

- All GPT/OpenAI usage must follow context-aware prompts
- Implement an `AIContextManager` that:
  - Stores user history
  - Adjusts prompts based on neurodiversity profile
- Use `GPT-4o` or fallback to `GPT-3.5` with controlled temperature
- Token usage must be monitored and capped
- Add context auditing to logs for debugging AI behaviors

---

## 🩺 6. **HealthKit & Biometric Rules**

- Always ask for HealthKit permissions in isolated `HealthAuthorizationService`
- Use `HKAnchoredObjectQuery` for heart rate tracking
- Persist all workout sessions in HealthKit **and** CoreData/Supabase
- Integrate biometrics (FaceID/TouchID) via `LocalAuthentication`
- Store session securely in `Keychain`

---

## 🧑‍🦽 7. **Accessibility & Neurodiversity Rules**

- All UI must support:
  - VoiceOver
  - Dynamic Type
  - Reduce Motion
  - High Contrast
  - Bold Text
- Create `NeuroAccessibilityManager.swift` to manage:
  - Sensory profile (visual, auditory, motion)
  - Cognitive load level
  - Executive function support flags
- Use `accessibilityLabel`, `accessibilityHint`, and `.accessibilityAddTraits` on all interactive elements
- Provide **cognitive scaffolding** in complex flows (e.g., AI breakdowns)

---

## 🔒 8. **Security & Privacy Rules**

- All auth tokens and sensitive data must be stored in **Keychain**
- Enforce **HIPAA-level** security in all medical/biometric apps
- Do not store any PII in plaintext or UserDefaults
- Add `AppTrackingTransparency` prompt if needed
- Use `NSPrivacyAccessedAPICategoryHealth` in `Info.plist` for HealthKit

---

## 📦 9. **Persistence & Offline Rules**

- Use **CoreData** for local storage (modular model files)
- Use `Supabase`, `Firebase`, or custom backend as remote layer
- Sync local → cloud with conflict resolution logic
- Implement `OfflineModeService.swift` to cache:
  - Tasks
  - Breathing sessions
  - AI chat logs
- Use `Codable` for all model objects

---

## 🧪 10. **Testing & CI/CD Rules**

- Use `XCTest`, `SnapshotTesting`, and `Quick/Nimble`
- All ViewModels must have unit tests
- Add `Playwright` or `XCUITest` for E2E UI tests
- Add GitHub Actions workflow:
  - Linting
  - Build checks
  - Test coverage
- Add `TestFlight` distribution setup with beta tester automation

---

## 🧩 11. **App Store & Deployment Rules**

- Add App Icons in all required sizes (`Assets.xcassets`)
- Include LaunchScreen with brand animation/logo
- Fill all required fields in App Store Connect
  - Privacy labels
  - Accessibility settings
  - Health data usage
- Enable background modes (if applicable):
  - HealthKit
  - Remote notifications
  - Background fetch

---

## 📘 12. **Documentation & Support Rules**

- Add full codebase documentation using `DocC`
- Create:
  - `ARCHITECTURE.md`
  - `DEPLOYMENT.md`
  - `ACCESSIBILITY_GUIDE.md`
  - `NEURODIVERSITY_GUIDE.md`
- Include built-in support chat or email
- Add `FAQ`, `Troubleshooting`, and `Changelog`
