# 📋 NeuroNexa iOS Development Rules & Guidelines

## 🎯 **Core Development Principles**

### **1. Neurodiversity-First Development**
- **ALWAYS** prioritize cognitive accessibility in every design decision
- **NEVER** assume neurotypical interaction patterns
- **ALWAYS** provide multiple ways to accomplish tasks
- **NEVER** use flashing animations or rapid transitions without user control
- **ALWAYS** include focus management and clear navigation paths

### **2. Privacy & Security First**
- **ALWAYS** encrypt sensitive data using AES-256
- **NEVER** store credentials in UserDefaults or plain text
- **ALWAYS** use Keychain for sensitive information
- **NEVER** log personal health information
- **ALWAYS** implement certificate pinning for API calls
- **NEVER** transmit data without explicit user consent

### **3. Performance Standards**
- **ALWAYS** target < 2 second app launch time
- **NEVER** block the main thread for > 16ms
- **ALWAYS** implement lazy loading for large datasets
- **NEVER** load all data at app startup
- **ALWAYS** optimize images and use appropriate formats
- **NEVER** ignore memory warnings

---

## 🏗️ **Architecture Rules**

### **MVVM-C Implementation**
```swift
// ✅ CORRECT: Proper ViewModel structure
class TaskListViewModel: ObservableObject {
    @Published var tasks: [Task] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let repository: TaskRepository
    private let coordinator: TaskCoordinator
    
    init(repository: TaskRepository, coordinator: TaskCoordinator) {
        self.repository = repository
        self.coordinator = coordinator
    }
    
    @MainActor
    func loadTasks() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            tasks = try await repository.fetchTasks()
        } catch {
            errorMessage = error.localizedDescription
        }
    }
}

// ❌ INCORRECT: View handling business logic
struct TaskListView: View {
    @State private var tasks: [Task] = []
    
    var body: some View {
        List(tasks, id: \.id) { task in
            // ❌ Don't put business logic in views
            if task.priority == .high && task.dueDate < Date() {
                // Complex business logic belongs in ViewModel
            }
        }
    }
}
```

### **Dependency Injection Rules**
- **ALWAYS** inject dependencies through initializers
- **NEVER** use singletons except for truly global state
- **ALWAYS** use protocols for testability
- **NEVER** create dependencies inside ViewModels

```swift
// ✅ CORRECT: Protocol-based dependency injection
protocol AIService {
    func generateTaskBreakdown(_ task: String) async throws -> TaskBreakdown
}

class TaskViewModel: ObservableObject {
    private let aiService: AIService
    
    init(aiService: AIService) {
        self.aiService = aiService
    }
}

// ❌ INCORRECT: Direct dependency creation
class TaskViewModel: ObservableObject {
    private let aiService = OpenAIService() // ❌ Hard to test
}
```

---

## 🎨 **UI/UX Development Rules**

### **SwiftUI Best Practices**
```swift
// ✅ CORRECT: Accessible, well-structured view
struct TaskRowView: View {
    let task: Task
    let onToggle: () -> Void
    
    var body: some View {
        HStack(spacing: Spacing.md) {
            Button(action: onToggle) {
                Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(task.isCompleted ? .green : .gray)
            }
            .accessibilityLabel(task.isCompleted ? "Mark as incomplete" : "Mark as complete")
            
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(task.title)
                    .font(.headline)
                    .strikethrough(task.isCompleted)
                
                if let dueDate = task.dueDate {
                    Text("Due: \(dueDate, style: .date)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            PriorityIndicator(priority: task.priority)
        }
        .padding(.vertical, Spacing.xs)
        .contentShape(Rectangle()) // Improves touch target
        .accessibilityElement(children: .combine)
    }
}

// ❌ INCORRECT: Poor accessibility and structure
struct BadTaskRowView: View {
    let task: Task
    
    var body: some View {
        HStack {
            // ❌ No accessibility labels
            Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
            // ❌ No proper spacing or typography hierarchy
            Text(task.title).font(.system(size: 14))
            // ❌ No touch target optimization
        }
    }
}
```

### **Accessibility Requirements**
- **ALWAYS** provide accessibility labels for interactive elements
- **ALWAYS** support Dynamic Type (font scaling)
- **ALWAYS** ensure minimum 44pt touch targets
- **ALWAYS** provide alternative text for images
- **ALWAYS** test with VoiceOver enabled
- **NEVER** rely solely on color to convey information

```swift
// ✅ CORRECT: Comprehensive accessibility
struct AccessibleButton: View {
    let title: String
    let action: () -> Void
    let isDestructive: Bool = false
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.headline)
                .foregroundColor(.white)
                .frame(minHeight: 44) // Minimum touch target
                .frame(maxWidth: .infinity)
                .background(isDestructive ? Color.red : NeuroNexaColors.primaryBlue)
                .cornerRadius(12)
        }
        .accessibilityLabel(title)
        .accessibilityHint(isDestructive ? "This action cannot be undone" : "")
        .accessibilityAddTraits(.isButton)
        .accessibilityRemoveTraits(isDestructive ? [] : .isButton)
    }
}
```

---

## 🔒 **Security Implementation Rules**

### **Data Protection**
```swift
// ✅ CORRECT: Secure data handling
class SecureDataManager {
    private let keychain = Keychain(service: "com.neuronexa.app")
        .accessibility(.whenUnlockedThisDeviceOnly)
    
    func storeUserToken(_ token: String) throws {
        try keychain.set(token, key: "user_token")
    }
    
    func retrieveUserToken() throws -> String? {
        return try keychain.get("user_token")
    }
    
    // ✅ Proper encryption for sensitive health data
    func storeHealthData<T: Codable>(_ data: T) throws {
        let jsonData = try JSONEncoder().encode(data)
        let encryptedData = try CryptoKit.AES.GCM.seal(jsonData, using: getEncryptionKey())
        try keychain.set(encryptedData.combined, key: "health_data")
    }
}

// ❌ INCORRECT: Insecure storage
class InsecureDataManager {
    func storeUserToken(_ token: String) {
        UserDefaults.standard.set(token, forKey: "user_token") // ❌ Insecure
    }
    
    func storeHealthData(_ data: String) {
        // ❌ No encryption for sensitive data
        try? data.write(to: getDocumentsDirectory().appendingPathComponent("health.txt"), 
                       atomically: true, encoding: .utf8)
    }
}
```

### **Network Security**
```swift
// ✅ CORRECT: Secure networking with certificate pinning
class SecureNetworkManager {
    private let session: URLSession
    
    init() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        
        self.session = URLSession(
            configuration: configuration,
            delegate: CertificatePinningDelegate(),
            delegateQueue: nil
        )
    }
    
    func makeSecureRequest<T: Codable>(
        to endpoint: APIEndpoint,
        responseType: T.Type
    ) async throws -> T {
        var request = URLRequest(url: endpoint.url)
        request.httpMethod = endpoint.method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add authentication header
        if let token = try? SecureDataManager().retrieveUserToken() {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw NetworkError.invalidResponse
        }
        
        return try JSONDecoder().decode(T.self, from: data)
    }
}
```

---

## 🧪 **Testing Rules**

### **Unit Testing Standards**
```swift
// ✅ CORRECT: Comprehensive unit test
class TaskViewModelTests: XCTestCase {
    var viewModel: TaskViewModel!
    var mockRepository: MockTaskRepository!
    var mockCoordinator: MockTaskCoordinator!
    
    override func setUp() {
        super.setUp()
        mockRepository = MockTaskRepository()
        mockCoordinator = MockTaskCoordinator()
        viewModel = TaskViewModel(
            repository: mockRepository,
            coordinator: mockCoordinator
        )
    }
    
    override func tearDown() {
        viewModel = nil
        mockRepository = nil
        mockCoordinator = nil
        super.tearDown()
    }
    
    func testLoadTasks_Success() async {
        // Given
        let expectedTasks = [Task.mock(), Task.mock()]
        mockRepository.tasksToReturn = expectedTasks
        
        // When
        await viewModel.loadTasks()
        
        // Then
        XCTAssertEqual(viewModel.tasks, expectedTasks)
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNil(viewModel.errorMessage)
    }
    
    func testLoadTasks_Failure() async {
        // Given
        mockRepository.shouldThrowError = true
        
        // When
        await viewModel.loadTasks()
        
        // Then
        XCTAssertTrue(viewModel.tasks.isEmpty)
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNotNil(viewModel.errorMessage)
    }
}
```

### **UI Testing Standards**
- **ALWAYS** test critical user flows
- **ALWAYS** test accessibility features
- **NEVER** test implementation details
- **ALWAYS** use Page Object pattern for complex flows

---

## 📱 **Apple Watch Development Rules**

### **Watch App Guidelines**
- **ALWAYS** keep interactions under 10 seconds
- **ALWAYS** provide haptic feedback for important actions
- **NEVER** perform heavy computations on watch
- **ALWAYS** sync data efficiently with iPhone app
- **ALWAYS** design for glanceable information

```swift
// ✅ CORRECT: Efficient watch view
struct WatchTaskListView: View {
    @StateObject private var viewModel = WatchTaskViewModel()
    
    var body: some View {
        NavigationView {
            List(viewModel.urgentTasks.prefix(5)) { task in // Limit items
                TaskRowView(task: task)
                    .onTapGesture {
                        WKInterfaceDevice.current().play(.click)
                        viewModel.toggleTask(task)
                    }
            }
            .navigationTitle("Tasks")
            .onAppear {
                viewModel.loadUrgentTasks() // Only load what's needed
            }
        }
    }
}
```

---

## 🚀 **Performance Rules**

### **Memory Management**
- **ALWAYS** use weak references for delegates
- **ALWAYS** cancel network requests when views disappear
- **NEVER** create retain cycles with closures
- **ALWAYS** dispose of Combine subscriptions

### **Battery Optimization**
- **ALWAYS** minimize background processing
- **ALWAYS** batch network requests
- **NEVER** poll servers continuously
- **ALWAYS** use efficient Core Data queries

---

## 📊 **Analytics & Monitoring Rules**

### **Privacy-Compliant Analytics**
- **NEVER** collect personally identifiable information
- **ALWAYS** anonymize user data
- **ALWAYS** provide opt-out mechanisms
- **NEVER** track without explicit consent

```swift
// ✅ CORRECT: Privacy-compliant analytics
struct AnalyticsEvent {
    let name: String
    let parameters: [String: Any]
    let timestamp: Date
    
    // ✅ No PII, anonymized data only
    static func taskCompleted(category: TaskCategory, duration: TimeInterval) -> AnalyticsEvent {
        return AnalyticsEvent(
            name: "task_completed",
            parameters: [
                "category": category.rawValue,
                "duration_seconds": Int(duration),
                "session_id": UUID().uuidString // Anonymous session ID
            ],
            timestamp: Date()
        )
    }
}
```
