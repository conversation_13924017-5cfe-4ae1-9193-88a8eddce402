// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 63;
	objects = {

/* Begin PBXBuildFile section */
		085E440920E573795E9FAA6C /* ViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39768C0ED5380415D42F627E /* ViewModel.swift */; };
		1BF07E92E1BBE6B8EE932D89 /* CognitiveButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 93ED4217F8175C868792F515 /* CognitiveButton.swift */; };
		26E105883F36E7D36007D968 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F368792AC77D4E443C2DA4DA /* Foundation.framework */; };
		28188994E020F9A24BFCC41D /* NeuroNexaTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 41403A20BFB77358D594626A /* NeuroNexaTests.swift */; };
		2B319B6D2D12D900D1FDF3AA /* BreathingExerciseService.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7F22FC0E4D0125E3DD51394 /* BreathingExerciseService.swift */; };
		2DE507A9509A9B4F5CBA7A0C /* TaskCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7530BF107654273C7F991859 /* TaskCard.swift */; };
		2F86B93EFE49872BB00D1EC5 /* ExecutiveFunctionSupportEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6B9C63C62F5B9EA965A07893 /* ExecutiveFunctionSupportEngine.swift */; };
		302DA3A16D550A36E0D79045 /* NeuroNexaApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAAD317E6EC82B8B3055CB35 /* NeuroNexaApp.swift */; };
		356E5FCF3FECEF207C216477 /* Combine.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 45AA77E14F5D361BA13A49CF /* Combine.framework */; };
		3CB3B45C0873CA6565C996DC /* NeuroNexaDesignSystem.swift in Sources */ = {isa = PBXBuildFile; fileRef = C760F555B5FFA89720702173 /* NeuroNexaDesignSystem.swift */; };
		44E4D386588ACDB6B0841DBD /* User.swift in Sources */ = {isa = PBXBuildFile; fileRef = 14B98E4904332312D61D738E /* User.swift */; };
		4B1A4A34294EF248305D67CF /* AITaskCoachViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32452AEEF878737B92D46018 /* AITaskCoachViewModel.swift */; };
		4B77CDDB0FC5FB9A1ECEDEAA /* AuthenticationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = E6810C03FEF8919836779DA1 /* AuthenticationService.swift */; };
		558750542402A4A9096496EA /* NeuroNexaModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = F96C9B7EE12B1712FF113B8B /* NeuroNexaModels.swift */; };
		5F6DC4CD1DB542BB80508274 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 849A698C04A1323A1819EE35 /* SettingsView.swift */; };
		602C74B9D0127BC5F37F1628 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = C278BD525BA76293FA2D08F8 /* AppDelegate.swift */; };
		6AEE7F9781C1BDF1F64BF091 /* DashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD1F382F511F88499E269997 /* DashboardView.swift */; };
		73AA23546358A104F2861505 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BB21457120F5BB8F255BAAC2 /* ContentView.swift */; };
		76E58D3791F5FBA0FEC27A0D /* BreathingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B3252C47F43F5F440C259A2A /* BreathingView.swift */; };
		95E658D408DE767D7118016D /* BreathingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BB39F3ED27423FE41E77E8B0 /* BreathingViewModel.swift */; };
		AC9EF12932C6AED564C6A6B8 /* HealthKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EA27AD106D6239CB88A50D86 /* HealthKit.framework */; };
		AFC71FED2EA857B24D2C6551 /* DependencyContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06B10CFDA09008AD815834B8 /* DependencyContainer.swift */; };
		BCE3A1F0786699DD81128B1E /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DDCE21D34A4D8BE903E7C970 /* SceneDelegate.swift */; };
		C073D56123BA822DBBAA0010 /* (null) in Sources */ = {isa = PBXBuildFile; };
		C6BE214FA04D2F53C831463D /* NeurodiversityEnums.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658245 /* NeurodiversityEnums.swift */; };
		C6BE214FA04D2F53C831463E /* SensoryEnums.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658246 /* SensoryEnums.swift */; };
		C6BE214FA04D2F53C831463F /* TaskEnums.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658247 /* TaskEnums.swift */; };
		C6BE214FA04D2F53C8314640 /* BreathingEnums.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658248 /* BreathingEnums.swift */; };
		C6BE214FA04D2F53C8314641 /* WellnessEnums.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658249 /* WellnessEnums.swift */; };
		C6BE214FA04D2F53C8314642 /* TaskCardConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD665824A /* TaskCardConfiguration.swift */; };
		C6BE214FA04D2F53C8314643 /* AccessibilitySettings.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD665824B /* AccessibilitySettings.swift */; };
		C6BE214FA04D2F53C8314644 /* NeuroNexaTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD665824C /* NeuroNexaTheme.swift */; };
		C6BE214FA04D2F53C8314645 /* ViewPlaceholders.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD665824D /* ViewPlaceholders.swift */; };
		C6BE214FA04D2F53C8314646 /* TaskModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD665824E /* TaskModels.swift */; };
		C6BE214FA04D2F53C8314647 /* TaskRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD665824F /* TaskRepository.swift */; };
		C6BE214FA04D2F53C8314648 /* RoutineRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658250 /* RoutineRepository.swift */; };
		C6BE214FA04D2F53C8314649 /* UserRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658251 /* UserRepository.swift */; };
		C6BE214FA04D2F53C831464A /* BreathingModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658252 /* BreathingModels.swift */; };
		C6BE214FA04D2F53C831464B /* BreathingService.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658253 /* BreathingService.swift */; };
		C6BE214FA04D2F53C831464C /* UserProfileModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DD6E6E70FA56BDD6658254 /* UserProfileModels.swift */; };
		C84B60BFF111546E27AB8319 /* Coordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CECD631B17BE8BDE82F03A /* Coordinator.swift */; };
		D84EE71EC640E0B8600099EA /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 046F4B37AD7B05E9E9EB6D9B /* UIKit.framework */; };
		DD0B054F45CAAFC5CC295544 /* NeuroNexaUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = EB2E9BB04E5AD5ED1D5FA5FD /* NeuroNexaUITests.swift */; };
		DD4C216F019CEE7F70DAEB97 /* AppConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 712986AD618F9CD7C667091D /* AppConfiguration.swift */; };
		E685EABDE4C67552E2B06746 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7E49B82876CB7F7D913C3127 /* SwiftUI.framework */; };
		E7C1A8D87871E3B041A1602F /* CognitivePatternAnalyzer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3AD5CA9610719A84572B3D59 /* CognitivePatternAnalyzer.swift */; };
		FBAFFFA15748CBBE74B8B005 /* iOS26Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF299F80434548CD683A2DBB /* iOS26Extensions.swift */; };
		FF6D02F7A7D8356B5BECB30D /* AITaskCoachService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DB6A021AFB553D06551B168A /* AITaskCoachService.swift */; };
	A1B2C3D4E5F6789012345678 /* NeuroNexaServices.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1D2E3F4A5B6789012345678 /* NeuroNexaServices.swift */; };
	B2C3D4E5F6789012345678A1 /* AITaskCoachView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4FD0B13AF9EB2FE1F45EDC3 /* AITaskCoachView.swift */; };

/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		606E3634AF5BC44BEF83D107 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2F637B368CA66BE52905F41C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE2AB19DEE7740FCE3A55A92;
			remoteInfo = NeuroNexa;
		};
		9BAC86405D8D8E3B143060AD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2F637B368CA66BE52905F41C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE2AB19DEE7740FCE3A55A92;
			remoteInfo = NeuroNexa;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		046F4B37AD7B05E9E9EB6D9B /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UIKit.framework; sourceTree = "<group>"; };
		06B10CFDA09008AD815834B8 /* DependencyContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DependencyContainer.swift; sourceTree = "<group>"; };
		14B98E4904332312D61D738E /* User.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = User.swift; sourceTree = "<group>"; };
		32452AEEF878737B92D46018 /* AITaskCoachViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AITaskCoachViewModel.swift; sourceTree = "<group>"; };
		39768C0ED5380415D42F627E /* ViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewModel.swift; sourceTree = "<group>"; };
		3AD5CA9610719A84572B3D59 /* CognitivePatternAnalyzer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CognitivePatternAnalyzer.swift; sourceTree = "<group>"; };
		41403A20BFB77358D594626A /* NeuroNexaTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeuroNexaTests.swift; sourceTree = "<group>"; };
		45AA77E14F5D361BA13A49CF /* Combine.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = Combine.framework; sourceTree = "<group>"; };
		6B9C63C62F5B9EA965A07893 /* ExecutiveFunctionSupportEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExecutiveFunctionSupportEngine.swift; sourceTree = "<group>"; };
		712986AD618F9CD7C667091D /* AppConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppConfiguration.swift; sourceTree = "<group>"; };
		7530BF107654273C7F991859 /* TaskCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskCard.swift; sourceTree = "<group>"; };
		7E49B82876CB7F7D913C3127 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = SwiftUI.framework; sourceTree = "<group>"; };
		849A698C04A1323A1819EE35 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		93ED4217F8175C868792F515 /* CognitiveButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CognitiveButton.swift; sourceTree = "<group>"; };
		A4F9C8968D19143561968474 /* NeuroNexa.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NeuroNexa.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A5CECD631B17BE8BDE82F03A /* Coordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Coordinator.swift; sourceTree = "<group>"; };
		A6390593219EFED0A480668C /* NeuroNexaTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NeuroNexaTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		AF299F80434548CD683A2DBB /* iOS26Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = iOS26Extensions.swift; sourceTree = "<group>"; };
		B0D5DC53E1AA2804B32A42E7 /* NeuroNexaUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NeuroNexaUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B3252C47F43F5F440C259A2A /* BreathingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BreathingView.swift; sourceTree = "<group>"; };
		BB21457120F5BB8F255BAAC2 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		BB39F3ED27423FE41E77E8B0 /* BreathingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BreathingViewModel.swift; sourceTree = "<group>"; };
		C278BD525BA76293FA2D08F8 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		C760F555B5FFA89720702173 /* NeuroNexaDesignSystem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeuroNexaDesignSystem.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD6658245 /* NeurodiversityEnums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeurodiversityEnums.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD6658246 /* SensoryEnums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SensoryEnums.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD6658247 /* TaskEnums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskEnums.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD6658248 /* BreathingEnums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BreathingEnums.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD6658249 /* WellnessEnums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WellnessEnums.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD665824A /* TaskCardConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskCardConfiguration.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD665824B /* AccessibilitySettings.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccessibilitySettings.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD665824C /* NeuroNexaTheme.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeuroNexaTheme.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD665824D /* ViewPlaceholders.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewPlaceholders.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD665824E /* TaskModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskModels.swift; sourceTree = "<group>"; };
		D0DD6E6E70FA56BDD665824F /* TaskRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Core/Repositories/TaskRepository.swift"; sourceTree = SOURCE_ROOT; };
		D0DD6E6E70FA56BDD6658250 /* RoutineRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Core/Repositories/RoutineRepository.swift"; sourceTree = SOURCE_ROOT; };
		D0DD6E6E70FA56BDD6658251 /* UserRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Core/Repositories/UserRepository.swift"; sourceTree = SOURCE_ROOT; };
		D0DD6E6E70FA56BDD6658252 /* BreathingModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Core/Models/BreathingModels.swift"; sourceTree = SOURCE_ROOT; };
		D0DD6E6E70FA56BDD6658253 /* BreathingService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Core/Services/BreathingService.swift"; sourceTree = SOURCE_ROOT; };
		D0DD6E6E70FA56BDD6658254 /* UserProfileModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Core/Models/UserProfileModels.swift"; sourceTree = SOURCE_ROOT; };
	C1D2E3F4A5B6789012345678 /* NeuroNexaServices.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Core/Models/NeuroNexaServices.swift"; sourceTree = SOURCE_ROOT; };
		D4FD0B13AF9EB2FE1F45EDC3 /* AITaskCoachView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Views/AITaskCoach/AITaskCoachView.swift"; sourceTree = SOURCE_ROOT; };

		DB6A021AFB553D06551B168A /* AITaskCoachService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AITaskCoachService.swift; sourceTree = "<group>"; };
		DDCE21D34A4D8BE903E7C970 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		E6810C03FEF8919836779DA1 /* AuthenticationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationService.swift; sourceTree = "<group>"; };
		EA27AD106D6239CB88A50D86 /* HealthKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = HealthKit.framework; sourceTree = "<group>"; };
		EB2E9BB04E5AD5ED1D5FA5FD /* NeuroNexaUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeuroNexaUITests.swift; sourceTree = "<group>"; };
		F368792AC77D4E443C2DA4DA /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = Foundation.framework; sourceTree = "<group>"; };
		F7F22FC0E4D0125E3DD51394 /* BreathingExerciseService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BreathingExerciseService.swift; sourceTree = "<group>"; };
		F96C9B7EE12B1712FF113B8B /* NeuroNexaModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeuroNexaModels.swift; sourceTree = "<group>"; };
		FAAD317E6EC82B8B3055CB35 /* NeuroNexaApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeuroNexaApp.swift; sourceTree = "<group>"; };
		FD1F382F511F88499E269997 /* DashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		E7F357634C29CEA429DEDD58 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E685EABDE4C67552E2B06746 /* SwiftUI.framework in Frameworks */,
				AC9EF12932C6AED564C6A6B8 /* HealthKit.framework in Frameworks */,
				356E5FCF3FECEF207C216477 /* Combine.framework in Frameworks */,
				26E105883F36E7D36007D968 /* Foundation.framework in Frameworks */,
				D84EE71EC640E0B8600099EA /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		043D17344552519A069E7C58 /* Styles */ = {
			isa = PBXGroup;
			children = (
				C760F555B5FFA89720702173 /* NeuroNexaDesignSystem.swift */,
			);
			path = Styles;
			sourceTree = "<group>";
		};
		0CC4886A80B74CA8614D94E4 /* Models */ = {
			isa = PBXGroup;
			children = (
				D0DD6E6E70FA56BDD665824B /* AccessibilitySettings.swift */,
				D0DD6E6E70FA56BDD665824C /* NeuroNexaTheme.swift */,
				D0DD6E6E70FA56BDD665824D /* ViewPlaceholders.swift */,
				D0DD6E6E70FA56BDD665824E /* TaskModels.swift */,
				D0DD6E6E70FA56BDD665824F /* TaskRepository.swift */,
				D0DD6E6E70FA56BDD6658250 /* RoutineRepository.swift */,
				D0DD6E6E70FA56BDD6658251 /* UserRepository.swift */,
				D0DD6E6E70FA56BDD6658252 /* BreathingModels.swift */,
				D0DD6E6E70FA56BDD6658253 /* BreathingService.swift */,
				D0DD6E6E70FA56BDD6658254 /* UserProfileModels.swift */,
				D0DD6E6E70FA56BDD6658245 /* NeurodiversityEnums.swift */,
				D0DD6E6E70FA56BDD6658246 /* SensoryEnums.swift */,
				D0DD6E6E70FA56BDD6658247 /* TaskEnums.swift */,
				D0DD6E6E70FA56BDD6658248 /* BreathingEnums.swift */,
				D0DD6E6E70FA56BDD6658249 /* WellnessEnums.swift */,
				F96C9B7EE12B1712FF113B8B /* NeuroNexaModels.swift */,
				1E64AD3F61D23B1F3C5427DF /* User */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		1C464E15856A41AE2186BD4B /* UI */ = {
			isa = PBXGroup;
			children = (
				FDE9A625212375069F1805BE /* Components */,
				654BEC57DB8B572F033CB6A7 /* Modifiers */,
				043D17344552519A069E7C58 /* Styles */,
				CC32C6FDFFAD81050B7F7570 /* Views */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		1E64AD3F61D23B1F3C5427DF /* User */ = {
			isa = PBXGroup;
			children = (
				14B98E4904332312D61D738E /* User.swift */,
			);
			path = User;
			sourceTree = "<group>";
		};
		5D2FDB8996692F53D2A406FE /* Dashboard */ = {
			isa = PBXGroup;
			children = (
				FD1F382F511F88499E269997 /* DashboardView.swift */,
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
		654BEC57DB8B572F033CB6A7 /* Modifiers */ = {
			isa = PBXGroup;
			children = (
				AF299F80434548CD683A2DBB /* iOS26Extensions.swift */,
			);
			path = Modifiers;
			sourceTree = "<group>";
		};
		7EE27A43A173334A8FAAD67C /* AI */ = {
			isa = PBXGroup;
			children = (
				DB6A021AFB553D06551B168A /* AITaskCoachService.swift */,
				3AD5CA9610719A84572B3D59 /* CognitivePatternAnalyzer.swift */,
				6B9C63C62F5B9EA965A07893 /* ExecutiveFunctionSupportEngine.swift */,
			);
			path = AI;
			sourceTree = "<group>";
		};
		896609057AAC561328873BD5 /* Architecture */ = {
			isa = PBXGroup;
			children = (
				A5CECD631B17BE8BDE82F03A /* Coordinator.swift */,
				06B10CFDA09008AD815834B8 /* DependencyContainer.swift */,
				39768C0ED5380415D42F627E /* ViewModel.swift */,
			);
			path = Architecture;
			sourceTree = "<group>";
		};
		8E1E9DCB79098A3E6CEFCA12 /* Settings */ = {
			isa = PBXGroup;
			children = (
				849A698C04A1323A1819EE35 /* SettingsView.swift */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		97343725311576BBC726AAFB = {
			isa = PBXGroup;
			children = (
				B74004FD2F99FF2887771682 /* App */,
				E026A5A16A9C1FF9FF128BA0 /* Core */,
				D3C80092457F6CF3F1843732 /* NeuroNexaTests */,
				CFD7756A2D271CE8DD26D718 /* NeuroNexaUITests */,
				CFDD335A49F6F9F4318D85EF /* Services */,
				1C464E15856A41AE2186BD4B /* UI */,
				A272B4C354CB221576E3741B /* ViewModels */,
				C759133F61386F1E926E36E2 /* Frameworks */,
				A3286BEE3AE6C688B56DC860 /* Products */,
			);
			sourceTree = "<group>";
		};
		A272B4C354CB221576E3741B /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				32452AEEF878737B92D46018 /* AITaskCoachViewModel.swift */,
				BB39F3ED27423FE41E77E8B0 /* BreathingViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		A3286BEE3AE6C688B56DC860 /* Products */ = {
			isa = PBXGroup;
			children = (
				A4F9C8968D19143561968474 /* NeuroNexa.app */,
				A6390593219EFED0A480668C /* NeuroNexaTests.xctest */,
				B0D5DC53E1AA2804B32A42E7 /* NeuroNexaUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AECB2205720B994AF9D14669 /* Services */ = {
			isa = PBXGroup;
			children = (
				C09F1982BEF8373FD49DCA8C /* Authentication */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		B74004FD2F99FF2887771682 /* App */ = {
			isa = PBXGroup;
			children = (
				712986AD618F9CD7C667091D /* AppConfiguration.swift */,
				C278BD525BA76293FA2D08F8 /* AppDelegate.swift */,
				FAAD317E6EC82B8B3055CB35 /* NeuroNexaApp.swift */,
				DDCE21D34A4D8BE903E7C970 /* SceneDelegate.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		C09F1982BEF8373FD49DCA8C /* Authentication */ = {
			isa = PBXGroup;
			children = (
				E6810C03FEF8919836779DA1 /* AuthenticationService.swift */,
			);
			path = Authentication;
			sourceTree = "<group>";
		};
		C759133F61386F1E926E36E2 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				45AA77E14F5D361BA13A49CF /* Combine.framework */,
				F368792AC77D4E443C2DA4DA /* Foundation.framework */,
				EA27AD106D6239CB88A50D86 /* HealthKit.framework */,
				7E49B82876CB7F7D913C3127 /* SwiftUI.framework */,
				046F4B37AD7B05E9E9EB6D9B /* UIKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CABDB0573A3D8CED9A6B46B3 /* AITaskCoach */ = {
			isa = PBXGroup;
			children = (
				D4FD0B13AF9EB2FE1F45EDC3 /* AITaskCoachView.swift */,
			);
			path = AITaskCoach;
			sourceTree = "<group>";
		};
		CB247396207638ABDCD3EC4C /* Breathing */ = {
			isa = PBXGroup;
			children = (
				F7F22FC0E4D0125E3DD51394 /* BreathingExerciseService.swift */,
			);
			path = Breathing;
			sourceTree = "<group>";
		};
		CC32C6FDFFAD81050B7F7570 /* Views */ = {
			isa = PBXGroup;
			children = (
				BB21457120F5BB8F255BAAC2 /* ContentView.swift */,
				CABDB0573A3D8CED9A6B46B3 /* AITaskCoach */,
				E0A1EB08324C394B7414AD0D /* Breathing */,
				5D2FDB8996692F53D2A406FE /* Dashboard */,
				8E1E9DCB79098A3E6CEFCA12 /* Settings */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		CFD7756A2D271CE8DD26D718 /* NeuroNexaUITests */ = {
			isa = PBXGroup;
			children = (
				EB2E9BB04E5AD5ED1D5FA5FD /* NeuroNexaUITests.swift */,
			);
			name = NeuroNexaUITests;
			path = Tests/NeuroNexaUITests;
			sourceTree = "<group>";
		};
		CFDD335A49F6F9F4318D85EF /* Services */ = {
			isa = PBXGroup;
			children = (
				7EE27A43A173334A8FAAD67C /* AI */,
				CB247396207638ABDCD3EC4C /* Breathing */,
				F7A8B9C0D1E2F3456789ABCD /* Core */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		F7A8B9C0D1E2F3456789ABCD /* Core */ = {
			isa = PBXGroup;
			children = (

			);
			path = Core;
			sourceTree = "<group>";
		};
		D3C80092457F6CF3F1843732 /* NeuroNexaTests */ = {
			isa = PBXGroup;
			children = (
				41403A20BFB77358D594626A /* NeuroNexaTests.swift */,
			);
			name = NeuroNexaTests;
			path = Tests/NeuroNexaTests;
			sourceTree = "<group>";
		};
		E026A5A16A9C1FF9FF128BA0 /* Core */ = {
			isa = PBXGroup;
			children = (
				896609057AAC561328873BD5 /* Architecture */,
				0CC4886A80B74CA8614D94E4 /* Models */,
				AECB2205720B994AF9D14669 /* Services */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		E0A1EB08324C394B7414AD0D /* Breathing */ = {
			isa = PBXGroup;
			children = (
				B3252C47F43F5F440C259A2A /* BreathingView.swift */,
			);
			path = Breathing;
			sourceTree = "<group>";
		};
		FDE9A625212375069F1805BE /* Components */ = {
			isa = PBXGroup;
			children = (
				93ED4217F8175C868792F515 /* CognitiveButton.swift */,
				7530BF107654273C7F991859 /* TaskCard.swift */,
				D0DD6E6E70FA56BDD665824A /* TaskCardConfiguration.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1CD20621A792F6AC1DB2F01E /* NeuroNexaUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3D4045D99E709CE8F4D862D1 /* Build configuration list for PBXNativeTarget "NeuroNexaUITests" */;
			buildPhases = (
				919235A6770AAB52F83C3B63 /* Sources */,
			);
			buildRules = (
			);
			dependencies = (
				1119A81A199A310B6D921762 /* PBXTargetDependency */,
			);
			name = NeuroNexaUITests;
			packageProductDependencies = (
			);
			productName = NeuroNexaUITests;
			productReference = B0D5DC53E1AA2804B32A42E7 /* NeuroNexaUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		DCCC9BA9B63311FADD35D601 /* NeuroNexaTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 73C095394140B0522805FEE7 /* Build configuration list for PBXNativeTarget "NeuroNexaTests" */;
			buildPhases = (
				BF410C455EFBB8870E280134 /* Sources */,
			);
			buildRules = (
			);
			dependencies = (
				155EED35D7AF7D436A683AA3 /* PBXTargetDependency */,
			);
			name = NeuroNexaTests;
			packageProductDependencies = (
			);
			productName = NeuroNexaTests;
			productReference = A6390593219EFED0A480668C /* NeuroNexaTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		EE2AB19DEE7740FCE3A55A92 /* NeuroNexa */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8335BD54DCA44867F3F30F14 /* Build configuration list for PBXNativeTarget "NeuroNexa" */;
			buildPhases = (
				E2D7C588AF7C057759BFDDC3 /* SwiftLint */,
				F64D3F6D812B8DB52B17FA03 /* Sources */,
				E7F357634C29CEA429DEDD58 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NeuroNexa;
			packageProductDependencies = (
			);
			productName = NeuroNexa;
			productReference = A4F9C8968D19143561968474 /* NeuroNexa.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2F637B368CA66BE52905F41C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					1CD20621A792F6AC1DB2F01E = {
						TestTargetID = EE2AB19DEE7740FCE3A55A92;
					};
					EE2AB19DEE7740FCE3A55A92 = {
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 4D35352B24CB1D644BB63180 /* Build configuration list for PBXProject "NeuroNexa" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = 97343725311576BBC726AAFB;
			minimizedProjectReferenceProxies = 1;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				EE2AB19DEE7740FCE3A55A92 /* NeuroNexa */,
				DCCC9BA9B63311FADD35D601 /* NeuroNexaTests */,
				1CD20621A792F6AC1DB2F01E /* NeuroNexaUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		E2D7C588AF7C057759BFDDC3 /* SwiftLint */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = SwiftLint;
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if which swiftlint >/dev/null; then\n  swiftlint\nelse\n  echo \"warning: SwiftLint not installed\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		919235A6770AAB52F83C3B63 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				DD0B054F45CAAFC5CC295544 /* NeuroNexaUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BF410C455EFBB8870E280134 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				28188994E020F9A24BFCC41D /* NeuroNexaTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F64D3F6D812B8DB52B17FA03 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C6BE214FA04D2F53C8314643 /* AccessibilitySettings.swift in Sources */,
				C6BE214FA04D2F53C8314644 /* NeuroNexaTheme.swift in Sources */,
				C6BE214FA04D2F53C8314645 /* ViewPlaceholders.swift in Sources */,
				C6BE214FA04D2F53C8314646 /* TaskModels.swift in Sources */,
				C6BE214FA04D2F53C8314647 /* TaskRepository.swift in Sources */,
				C6BE214FA04D2F53C8314648 /* RoutineRepository.swift in Sources */,
				C6BE214FA04D2F53C8314649 /* UserRepository.swift in Sources */,
		C6BE214FA04D2F53C831464A /* BreathingModels.swift in Sources */,
		C6BE214FA04D2F53C831464B /* BreathingService.swift in Sources */,
		C6BE214FA04D2F53C831464C /* UserProfileModels.swift in Sources */,
				FF6D02F7A7D8356B5BECB30D /* AITaskCoachService.swift in Sources */,
		A1B2C3D4E5F6789012345678 /* NeuroNexaServices.swift in Sources */,
		B2C3D4E5F6789012345678A1 /* AITaskCoachView.swift in Sources */,
				C073D56123BA822DBBAA0010 /* (null) in Sources */,
				4B1A4A34294EF248305D67CF /* AITaskCoachViewModel.swift in Sources */,
				DD4C216F019CEE7F70DAEB97 /* AppConfiguration.swift in Sources */,
				602C74B9D0127BC5F37F1628 /* AppDelegate.swift in Sources */,
				4B77CDDB0FC5FB9A1ECEDEAA /* AuthenticationService.swift in Sources */,
				2B319B6D2D12D900D1FDF3AA /* BreathingExerciseService.swift in Sources */,
				76E58D3791F5FBA0FEC27A0D /* BreathingView.swift in Sources */,
				95E658D408DE767D7118016D /* BreathingViewModel.swift in Sources */,
				1BF07E92E1BBE6B8EE932D89 /* CognitiveButton.swift in Sources */,
				E7C1A8D87871E3B041A1602F /* CognitivePatternAnalyzer.swift in Sources */,
				73AA23546358A104F2861505 /* ContentView.swift in Sources */,
				C84B60BFF111546E27AB8319 /* Coordinator.swift in Sources */,
				6AEE7F9781C1BDF1F64BF091 /* DashboardView.swift in Sources */,
				AFC71FED2EA857B24D2C6551 /* DependencyContainer.swift in Sources */,
				2F86B93EFE49872BB00D1EC5 /* ExecutiveFunctionSupportEngine.swift in Sources */,
				302DA3A16D550A36E0D79045 /* NeuroNexaApp.swift in Sources */,
				3CB3B45C0873CA6565C996DC /* NeuroNexaDesignSystem.swift in Sources */,
				C6BE214FA04D2F53C831463D /* NeurodiversityEnums.swift in Sources */,
				C6BE214FA04D2F53C831463E /* SensoryEnums.swift in Sources */,
				C6BE214FA04D2F53C831463F /* TaskEnums.swift in Sources */,
				C6BE214FA04D2F53C8314640 /* BreathingEnums.swift in Sources */,
				C6BE214FA04D2F53C8314641 /* WellnessEnums.swift in Sources */,
				558750542402A4A9096496EA /* NeuroNexaModels.swift in Sources */,
				BCE3A1F0786699DD81128B1E /* SceneDelegate.swift in Sources */,
				5F6DC4CD1DB542BB80508274 /* SettingsView.swift in Sources */,
				2DE507A9509A9B4F5CBA7A0C /* TaskCard.swift in Sources */,
				C6BE214FA04D2F53C8314642 /* TaskCardConfiguration.swift in Sources */,
				44E4D386588ACDB6B0841DBD /* User.swift in Sources */,
				085E440920E573795E9FAA6C /* ViewModel.swift in Sources */,
				FBAFFFA15748CBBE74B8B005 /* iOS26Extensions.swift in Sources */,

			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1119A81A199A310B6D921762 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE2AB19DEE7740FCE3A55A92 /* NeuroNexa */;
			targetProxy = 606E3634AF5BC44BEF83D107 /* PBXContainerItemProxy */;
		};
		155EED35D7AF7D436A683AA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE2AB19DEE7740FCE3A55A92 /* NeuroNexa */;
			targetProxy = 9BAC86405D8D8E3B143060AD /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0418F3F6297E17C20B99889E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = YNSMLADB62;
				ENABLE_PREVIEWS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.neuronexa.ios;
				PRODUCT_NAME = NeuroNexa;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1BC01CA1E79500F9B665E9FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		31DF2BB2272EEFEAA13FD3B2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = YNSMLADB62;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.neuronexa.NeuroNexaTests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NeuroNexa.app/NeuroNexa";
			};
			name = Release;
		};
		A9C16AED88F504C246B9F4FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = YNSMLADB62;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.neuronexa.NeuroNexaUITests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NeuroNexa;
			};
			name = Debug;
		};
		AA90A66870C35A645DE1945C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		AFCEB9E93FD381FA71AA73DC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = YNSMLADB62;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.neuronexa.NeuroNexaTests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NeuroNexa.app/NeuroNexa";
			};
			name = Debug;
		};
		B01D1C4180CDD4B6FF032B3B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = YNSMLADB62;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.neuronexa.NeuroNexaUITests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NeuroNexa;
			};
			name = Release;
		};
		D5B01BE9D2EDE8773D6DE1E5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = YNSMLADB62;
				ENABLE_PREVIEWS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.neuronexa.ios;
				PRODUCT_NAME = NeuroNexa;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3D4045D99E709CE8F4D862D1 /* Build configuration list for PBXNativeTarget "NeuroNexaUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A9C16AED88F504C246B9F4FC /* Debug */,
				B01D1C4180CDD4B6FF032B3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		4D35352B24CB1D644BB63180 /* Build configuration list for PBXProject "NeuroNexa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AA90A66870C35A645DE1945C /* Debug */,
				1BC01CA1E79500F9B665E9FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		73C095394140B0522805FEE7 /* Build configuration list for PBXNativeTarget "NeuroNexaTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AFCEB9E93FD381FA71AA73DC /* Debug */,
				31DF2BB2272EEFEAA13FD3B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		8335BD54DCA44867F3F30F14 /* Build configuration list for PBXNativeTarget "NeuroNexa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0418F3F6297E17C20B99889E /* Debug */,
				D5B01BE9D2EDE8773D6DE1E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2F637B368CA66BE52905F41C /* Project object */;
}
