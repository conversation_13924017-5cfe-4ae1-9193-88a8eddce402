<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1600"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      runPostActionsOnFailure = "NO">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "EE2AB19DEE7740FCE3A55A92"
               BuildableName = "NeuroNexa.app"
               BlueprintName = "NeuroNexa"
               ReferencedContainer = "container:NeuroNexa.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "DCCC9BA9B63311FADD35D601"
               BuildableName = "NeuroNexaTests.xctest"
               BlueprintName = "NeuroNexaTests"
               ReferencedContainer = "container:NeuroNexa.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "1CD20621A792F6AC1DB2F01E"
               BuildableName = "NeuroNexaUITests.xctest"
               BlueprintName = "NeuroNexaUITests"
               ReferencedContainer = "container:NeuroNexa.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      onlyGenerateCoverageForSpecifiedTargets = "NO">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "EE2AB19DEE7740FCE3A55A92"
            BuildableName = "NeuroNexa.app"
            BlueprintName = "NeuroNexa"
            ReferencedContainer = "container:NeuroNexa.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "DCCC9BA9B63311FADD35D601"
               BuildableName = "NeuroNexaTests.xctest"
               BlueprintName = "NeuroNexaTests"
               ReferencedContainer = "container:NeuroNexa.xcodeproj">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "1CD20621A792F6AC1DB2F01E"
               BuildableName = "NeuroNexaUITests.xctest"
               BlueprintName = "NeuroNexaUITests"
               ReferencedContainer = "container:NeuroNexa.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
      <CommandLineArguments>
      </CommandLineArguments>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "EE2AB19DEE7740FCE3A55A92"
            BuildableName = "NeuroNexa.app"
            BlueprintName = "NeuroNexa"
            ReferencedContainer = "container:NeuroNexa.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
      </CommandLineArguments>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "EE2AB19DEE7740FCE3A55A92"
            BuildableName = "NeuroNexa.app"
            BlueprintName = "NeuroNexa"
            ReferencedContainer = "container:NeuroNexa.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
