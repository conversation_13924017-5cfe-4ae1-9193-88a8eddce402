warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/36)
Linting 'NeuroNexaApp.swift' (2/36)
Linting 'AppDelegate.swift' (3/36)
Linting 'SceneDelegate.swift' (4/36)
Linting 'iOS26Extensions.swift' (6/36)
Linting 'NeuroNexaDesignSystem.swift' (7/36)
Linting 'CognitiveProgressViewStyle.swift' (8/36)
Linting 'TaskCardConfiguration.swift' (9/36)
Linting 'NeurodiversityModifiers.swift' (5/36)
Linting 'CognitiveButton.swift' (10/36)
Linting 'TaskCard.swift' (11/36)
Linting 'SettingsView.swift' (12/36)
Linting 'NeurodiversityEnums.swift' (14/36)
Linting 'DashboardView.swift' (15/36)
Linting 'ContentView.swift' (13/36)
Linting 'AnxietyDetectionSheet.swift' (16/36)
Linting 'BreathingSupportingViews.swift' (18/36)
Linting 'BreathingView.swift' (17/36)
Linting 'NeuroNexaModels.swift' (19/36)
Linting 'TaskEnums.swift' (20/36)
Linting 'User.swift' (21/36)
Linting 'WellnessEnums.swift' (22/36)
Linting 'DependencyContainer.swift' (23/36)
Linting 'BreathingEnums.swift' (24/36)
Linting 'ViewModel.swift' (25/36)
Linting 'Coordinator.swift' (26/36)
Linting 'OpenAITaskCoach.swift' (27/36)
Linting 'AuthenticationService.swift' (28/36)
Linting 'NeuroNexaTests.swift' (29/36)
Linting 'NeurodiversityAccessibilityTests.swift' (30/36)
Linting 'AccessibilityAuditTests.swift' (31/36)
Linting 'NeuroNexaUITests.swift' (32/36)
Linting 'BreathingExerciseServiceTests.swift' (33/36)
Linting 'BreathingSessionTests.swift' (34/36)
Linting 'AITaskCoachServiceTests.swift' (35/36)
Linting 'SensoryEnums.swift' (36/36)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Architecture/DependencyContainer.swift",
    "line" : 1,
    "reason" : "Files should not contain leading whitespace",
    "rule_id" : "leading_whitespace",
    "severity" : "Warning",
    "type" : "Leading Whitespace"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 289,
    "reason" : "Images that provide context should have an accessibility label or should be explicitly hidden from accessibility",
    "rule_id" : "accessibility_label_for_image",
    "severity" : "Warning",
    "type" : "Accessibility Label for Image"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 373,
    "reason" : "All views with tap gestures added should include the .isButton or the .isLink accessibility traits",
    "rule_id" : "accessibility_trait_for_button",
    "severity" : "Warning",
    "type" : "Accessibility Trait for Button"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 611,
    "reason" : "File should contain 500 lines or less: currently contains 611",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 8,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 476 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  }
]
Done linting! Found 5 violations, 0 serious in 36 files.
