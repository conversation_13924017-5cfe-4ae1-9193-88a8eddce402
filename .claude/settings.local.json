{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(swiftlint:*)", "Bash(ls:*)", "Bash(xcodebuild:*)", "Bash(swift build:*)", "Bash(timeout 60 xcodebuild:*)", "Bash(-exec swift -frontend -typecheck {})", "Bash(/usr/bin/timeout 30 xcodebuild -scheme NeuroNexa -configuration Debug -destination 'platform=iOS Simulator,id=5E1C72BF-5386-4E1F-84B5-821434F94920' build)", "Ba<PERSON>(codium:*)", "mcp__ide__getDiagnostics", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "WebFetch(domain:apidog.com)", "Bash(pip3 install:*)"], "deny": []}}