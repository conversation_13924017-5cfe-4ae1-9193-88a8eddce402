warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/123)
Linting 'NeuroNexaApp.swift' (2/123)
Linting 'SceneDelegate.swift' (3/123)
Linting 'SettingsViewModel.swift' (4/123)
Linting 'AppDelegate.swift' (5/123)
Linting 'AITaskCoachViewModel.swift' (7/123)
Linting 'DashboardViewModel.swift' (8/123)
Linting 'BreathingViewModel.swift' (6/123)
Linting 'iOS26Extensions.swift' (9/123)
Linting 'NeuroNexaDesignSystem.swift' (10/123)
Linting 'CognitiveButton+Extensions.swift' (11/123)
Linting 'TaskCard+Extensions.swift' (12/123)
Linting 'TaskCardConfiguration.swift' (13/123)
Linting 'TaskCard+ViewSections.swift' (14/123)
Linting 'CognitiveProgressViewStyle.swift' (15/123)
Linting 'CognitiveButton.swift' (16/123)
Linting 'TaskCard.swift' (17/123)
Linting 'UI/Views/BreathingView.swift' (18/123)
Linting 'UI/Views/DashboardView.swift' (20/123)
Linting 'UI/Views/Settings/SettingsView.swift' (19/123)
Linting 'UI/Views/SettingsView.swift' (21/123)
Linting 'UI/Views/Breathing/BreathingView.swift' (22/123)
Linting 'BreathingSupportingViews.swift' (23/123)
Linting 'BreathingHelpers.swift' (24/123)
Linting 'BreathingOverlayViews.swift' (25/123)
Linting 'AnxietyDetectionSheet.swift' (26/123)
Linting 'BreathingContentViews.swift' (27/123)
Linting 'ContentView.swift' (29/123)
Linting 'AITaskCoachView.swift' (28/123)
Linting 'UserRepository.swift' (30/123)
Linting 'UI/Views/Dashboard/DashboardView.swift' (31/123)
Linting 'UserProfileRepository.swift' (32/123)
Linting 'RoutineRepository.swift' (33/123)
Linting 'TaskRepository.swift' (34/123)
Linting 'NeurodiversityEnums.swift' (35/123)
Linting 'BreathingSessionRepository.swift' (36/123)
Linting 'BehaviorModels.swift' (37/123)
Linting 'CognitivePatternModels.swift' (38/123)
Linting 'OpenAIModels.swift' (39/123)
Linting 'SettingsModels.swift' (40/123)
Linting 'NeuroNexaModels.swift' (41/123)
Linting 'BehaviorPredictionModels.swift' (42/123)
Linting 'TaskTimingModels.swift' (43/123)
Linting 'TaskEnums.swift' (44/123)
Linting 'UserPreferences.swift' (45/123)
Linting 'BreathingModels.swift' (46/123)
Linting 'OpenAIUserContextModels.swift' (47/123)
Linting 'NeurodiversityServices.swift' (48/123)
Linting 'AccessibilitySettings.swift' (49/123)
Linting 'OpenAITaskModels.swift' (50/123)
Linting 'ViewPlaceholders.swift' (51/123)
Linting 'ExecutiveFunctionModels.swift' (52/123)
Linting 'OpenAITypes.swift' (53/123)
Linting 'OpenAIBreakModels.swift' (54/123)
Linting 'SensoryOptimizationModels.swift' (55/123)
Linting 'OpenAICoachModels.swift' (56/123)
Linting 'CognitiveOptimizationModels.swift' (57/123)
Linting 'User.swift' (58/123)
Linting 'OpenAICognitiveAdaptationModels.swift' (59/123)
Linting 'SensoryEnums.swift' (60/123)
Linting 'CognitivePreferencesModels.swift' (61/123)
Linting 'PersonalizedContentTypes.swift' (62/123)
Linting 'NeuroNexaServices.swift' (63/123)
Linting 'NeuroNexaTheme.swift' (64/123)
Linting 'SharedTypes.swift' (65/123)
Linting 'SensoryModels.swift' (66/123)
Linting 'NeurodiversityTypes.swift' (67/123)
Linting 'CognitiveSupportingTypes.swift' (68/123)
Linting 'CognitiveModels.swift' (69/123)
Linting 'OpenAIContentModels.swift' (70/123)
Linting 'UserProfileModels.swift' (71/123)
Linting 'BreakSuggestionTypes.swift' (72/123)
Linting 'OpenAITaskCoachModels.swift' (73/123)
Linting 'CognitiveAnalysisModels.swift' (74/123)
Linting 'SensoryCognitiveModels.swift' (75/123)
Linting 'CognitiveAdaptationTypes.swift' (76/123)
Linting 'BreathingEnums.swift' (77/123)
Linting 'TaskModels.swift' (78/123)
Linting 'SensoryAdaptationModels.swift' (79/123)
Linting 'WellnessEnums.swift' (80/123)
Linting 'BehaviorInsightsModels.swift' (81/123)
Linting 'BehaviorAnalysisModels.swift' (82/123)
Linting 'ViewModel.swift' (83/123)
Linting 'Coordinator.swift' (84/123)
Linting 'DependencyContainer.swift' (85/123)
Linting 'ServiceProtocols.swift' (86/123)
Linting 'CognitiveAnalysisServiceHelpers.swift' (87/123)
Linting 'CognitiveLoadService.swift' (88/123)
Linting 'PrivacySettings.swift' (89/123)
Linting 'CognitiveAnalysisServiceExtensions.swift' (90/123)
Linting 'CoreDataService.swift' (91/123)
Linting 'ExecutiveFunctionService.swift' (92/123)
Linting 'UserService.swift' (93/123)
Linting 'BreathingServiceHelpers.swift' (94/123)
Linting 'SensoryAdaptationService.swift' (95/123)
Linting 'PersonalizedTaskServiceExtensions.swift' (96/123)
Linting 'HealthKitService.swift' (98/123)
Linting 'WatchConnectivityService.swift' (97/123)
Linting 'OpenAIErrors.swift' (99/123)
Linting 'BasicServiceImplementations.swift' (100/123)
Linting 'OpenAIService.swift' (101/123)
Linting 'OpenAITaskCoachParsing.swift' (102/123)
Linting 'OpenAITaskCoach.swift' (103/123)
Linting 'OpenAITaskCoachExtensions.swift' (104/123)
Linting 'OpenAITaskCoachHelpers.swift' (105/123)
Linting 'OpenAITaskCoachPrompts.swift' (106/123)
Linting 'CognitiveAnalysisService.swift' (107/123)
Linting 'PersonalizedTaskHelpers.swift' (108/123)
Linting 'CognitiveAnalysisHelpers.swift' (109/123)
Linting 'PersonalizedTaskService.swift' (110/123)
Linting 'SettingsService.swift' (111/123)
Linting 'CloudKitService.swift' (112/123)
Linting 'PersonalizedTaskServiceGeneration.swift' (113/123)
Linting 'BreathingService.swift' (114/123)
Linting 'AuthenticationService.swift' (115/123)
Linting 'NeuroNexaUITests.swift' (116/123)
Linting 'NeuroNexaTests.swift' (117/123)
Linting 'NeurodiversityAccessibilityTests.swift' (118/123)
Linting 'AccessibilityAuditTests.swift' (119/123)
Linting 'BreathingExerciseServiceTests.swift' (120/123)
Linting 'AITaskCoachServiceTests.swift' (121/123)
Linting 'AITaskCoachServiceTestsExtensions.swift' (122/123)
Linting 'BreathingSessionTests.swift' (123/123)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+Extensions.swift",
    "line" : 211,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+ViewSections.swift",
    "line" : 177,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+ViewSections.swift",
    "line" : 7,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/AITaskCoachView.swift",
    "line" : 604,
    "reason" : "File should contain 500 lines or less: currently contains 604",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 38,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/ContentView.swift",
    "line" : 125,
    "reason" : "Unused parameter in a closure should be replaced with _",
    "rule_id" : "unused_closure_parameter",
    "severity" : "Warning",
    "type" : "Unused Closure Parameter"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Dashboard/DashboardView.swift",
    "line" : 547,
    "reason" : "File should contain 500 lines or less: currently contains 547",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 216,
    "reason" : "Variable name 'n' should be between 2 and 50 characters long",
    "rule_id" : "identifier_name",
    "severity" : "Warning",
    "type" : "Identifier Name"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 227,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 8,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 423,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "File should contain 500 lines or less: currently contains 501",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton+Extensions.swift",
    "line" : 206,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton+Extensions.swift",
    "line" : 8,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton+Extensions.swift",
    "line" : 66,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  }
]
Done linting! Found 15 violations, 0 serious in 123 files.
