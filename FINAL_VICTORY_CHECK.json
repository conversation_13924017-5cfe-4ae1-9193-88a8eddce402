warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/125)
Linting 'NeuroNexaApp.swift' (2/125)
Linting 'AppDelegate.swift' (3/125)
Linting 'SettingsViewModel.swift' (5/125)
Linting 'SceneDelegate.swift' (4/125)
Linting 'BreathingViewModel.swift' (6/125)
Linting 'AITaskCoachViewModel.swift' (7/125)
Linting 'DashboardViewModel.swift' (8/125)
Linting 'iOS26Extensions.swift' (9/125)
Linting 'NeuroNexaDesignSystem.swift' (10/125)
Linting 'CognitiveButton+Extensions.swift' (11/125)
Linting 'TaskCard+Extensions.swift' (12/125)
Linting 'TaskCard+ViewSections.swift' (14/125)
Linting 'CognitiveButton.swift' (13/125)
Linting 'CognitiveProgressViewStyle.swift' (16/125)
Linting 'TaskCard.swift' (17/125)
Linting 'TaskCardConfiguration.swift' (15/125)
Linting 'BreathingSupportingViews.swift' (18/125)
Linting 'BreathingOverlayViews.swift' (19/125)
Linting 'BreathingHelpers.swift' (20/125)
Linting 'AnxietyDetectionSheet.swift' (21/125)
Linting 'BreathingContentViews.swift' (22/125)
Linting 'AITaskCoachView+Extensions.swift' (23/125)
Linting 'DashboardView+Extensions.swift' (24/125)
Linting 'UI/Views/Settings/SettingsView.swift' (25/125)
Linting 'ContentView.swift' (26/125)
Linting 'UserRepository.swift' (27/125)
Linting 'UI/Views/SettingsView.swift' (28/125)
Linting 'AITaskCoachView.swift' (29/125)
Linting 'UI/Views/Breathing/BreathingView.swift' (30/125)
Linting 'UI/Views/BreathingView.swift' (31/125)
Linting 'TaskRepository.swift' (32/125)
Linting 'RoutineRepository.swift' (33/125)
Linting 'BreathingSessionRepository.swift' (34/125)
Linting 'UserProfileRepository.swift' (35/125)
Linting 'BehaviorModels.swift' (36/125)
Linting 'NeurodiversityEnums.swift' (37/125)
Linting 'SettingsModels.swift' (38/125)
Linting 'CognitivePatternModels.swift' (39/125)
Linting 'NeuroNexaModels.swift' (40/125)
Linting 'OpenAIModels.swift' (41/125)
Linting 'TaskEnums.swift' (42/125)
Linting 'UserPreferences.swift' (43/125)
Linting 'BreathingModels.swift' (44/125)
Linting 'BehaviorPredictionModels.swift' (45/125)
Linting 'TaskTimingModels.swift' (46/125)
Linting 'ViewPlaceholders.swift' (47/125)
Linting 'NeurodiversityServices.swift' (48/125)
Linting 'OpenAIUserContextModels.swift' (49/125)
Linting 'OpenAITaskModels.swift' (50/125)
Linting 'UI/Views/Dashboard/DashboardView.swift' (51/125)
Linting 'AccessibilitySettings.swift' (52/125)
Linting 'ExecutiveFunctionModels.swift' (53/125)
Linting 'OpenAITypes.swift' (54/125)
Linting 'SensoryOptimizationModels.swift' (55/125)
Linting 'User.swift' (56/125)
Linting 'OpenAIBreakModels.swift' (57/125)
Linting 'OpenAICoachModels.swift' (58/125)
Linting 'CognitiveOptimizationModels.swift' (59/125)
Linting 'SensoryEnums.swift' (60/125)
Linting 'NeuroNexaTheme.swift' (61/125)
Linting 'NeuroNexaServices.swift' (62/125)
Linting 'OpenAICognitiveAdaptationModels.swift' (63/125)
Linting 'NeurodiversityTypes.swift' (64/125)
Linting 'SensoryModels.swift' (65/125)
Linting 'CognitivePreferencesModels.swift' (66/125)
Linting 'PersonalizedContentTypes.swift' (67/125)
Linting 'CognitiveSupportingTypes.swift' (68/125)
Linting 'SharedTypes.swift' (69/125)
Linting 'UserProfileModels.swift' (70/125)
Linting 'OpenAIContentModels.swift' (72/125)
Linting 'CognitiveModels.swift' (71/125)
Linting 'BreakSuggestionTypes.swift' (73/125)
Linting 'CognitiveAnalysisModels.swift' (74/125)
Linting 'OpenAITaskCoachModels.swift' (75/125)
Linting 'SensoryAdaptationModels.swift' (76/125)
Linting 'BreathingEnums.swift' (77/125)
Linting 'CognitiveAdaptationTypes.swift' (78/125)
Linting 'SensoryCognitiveModels.swift' (79/125)
Linting 'TaskModels.swift' (80/125)
Linting 'PrivacySettings.swift' (81/125)
Linting 'WellnessEnums.swift' (82/125)
Linting 'Coordinator.swift' (83/125)
Linting 'ViewModel.swift' (84/125)
Linting 'ServiceProtocols.swift' (85/125)
Linting 'CognitiveLoadService.swift' (86/125)
Linting 'BehaviorAnalysisModels.swift' (87/125)
Linting 'DependencyContainer.swift' (88/125)
Linting 'BehaviorInsightsModels.swift' (89/125)
Linting 'CognitiveAnalysisServiceHelpers.swift' (90/125)
Linting 'ExecutiveFunctionService.swift' (91/125)
Linting 'CognitiveAnalysisServiceExtensions.swift' (92/125)
Linting 'CoreDataService.swift' (93/125)
Linting 'UserService.swift' (94/125)
Linting 'SensoryAdaptationService.swift' (95/125)
Linting 'WatchConnectivityService.swift' (96/125)
Linting 'HealthKitService.swift' (97/125)
Linting 'OpenAIErrors.swift' (98/125)
Linting 'PersonalizedTaskServiceExtensions.swift' (99/125)
Linting 'BasicServiceImplementations.swift' (100/125)
Linting 'BreathingServiceHelpers.swift' (101/125)
Linting 'OpenAIService.swift' (102/125)
Linting 'OpenAITaskCoach.swift' (103/125)
Linting 'OpenAITaskCoachParsing.swift' (104/125)
Linting 'OpenAITaskCoachPrompts.swift' (105/125)
Linting 'OpenAITaskCoachHelpers.swift' (106/125)
Linting 'CognitiveAnalysisHelpers.swift' (107/125)
Linting 'PersonalizedTaskService.swift' (108/125)
Linting 'OpenAITaskCoachExtensions.swift' (109/125)
Linting 'SettingsService.swift' (110/125)
Linting 'PersonalizedTaskHelpers.swift' (111/125)
Linting 'CognitiveAnalysisService.swift' (112/125)
Linting 'BreathingService.swift' (113/125)
Linting 'AuthenticationService.swift' (114/125)
Linting 'PersonalizedTaskServiceGeneration.swift' (115/125)
Linting 'CloudKitService.swift' (116/125)
Linting 'NeuroNexaUITests.swift' (117/125)
Linting 'AccessibilityAuditTests.swift' (118/125)
Linting 'NeurodiversityAccessibilityTests.swift' (119/125)
Linting 'NeuroNexaTests.swift' (120/125)
Linting 'BreathingSessionTests.swift' (121/125)
Linting 'BreathingExerciseServiceTests.swift' (122/125)
Linting 'AITaskCoachServiceTestsExtensions.swift' (123/125)
Linting 'AITaskCoachServiceTests.swift' (124/125)
Linting 'UI/Views/DashboardView.swift' (125/125)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton+Extensions.swift",
    "line" : 206,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton+Extensions.swift",
    "line" : 8,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/CognitiveButton+Extensions.swift",
    "line" : 66,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+Extensions.swift",
    "line" : 211,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+ViewSections.swift",
    "line" : 177,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Components/TaskCard+ViewSections.swift",
    "line" : 7,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/AITaskCoachView+Extensions.swift",
    "line" : 318,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Dashboard/DashboardView+Extensions.swift",
    "line" : 324,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Dashboard/DashboardView+Extensions.swift",
    "line" : 7,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 216,
    "reason" : "Variable name 'n' should be between 2 and 50 characters long",
    "rule_id" : "identifier_name",
    "severity" : "Warning",
    "type" : "Identifier Name"
  },
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 227,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisServiceHelpers.swift",
    "line" : 8,
    "reason" : "Don't include vertical whitespace (empty line) after opening braces",
    "rule_id" : "vertical_whitespace_opening_braces",
    "severity" : "Warning",
    "type" : "Vertical Whitespace after Opening Braces"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 423,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  }
]
Done linting! Found 13 violations, 0 serious in 125 files.
