warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/119)
Linting 'NeuroNexaApp.swift' (2/119)
Linting 'AppDelegate.swift' (3/119)
Linting 'SceneDelegate.swift' (4/119)
Linting 'SettingsViewModel.swift' (5/119)
Linting 'BreathingViewModel.swift' (6/119)
Linting 'AITaskCoachViewModel.swift' (7/119)
Linting 'DashboardViewModel.swift' (8/119)
Linting 'iOS26Extensions.swift' (9/119)
Linting 'NeuroNexaDesignSystem.swift' (10/119)
Linting 'CognitiveProgressViewStyle.swift' (11/119)
Linting 'TaskCardConfiguration.swift' (12/119)
Linting 'CognitiveButton.swift' (13/119)
Linting 'TaskCard.swift' (14/119)
Linting 'UI/Views/BreathingView.swift' (15/119)
Linting 'UI/Views/SettingsView.swift' (16/119)
Linting 'UI/Views/DashboardView.swift' (17/119)
Linting 'UI/Views/Settings/SettingsView.swift' (18/119)
Linting 'BreathingHelpers.swift' (19/119)
Linting 'BreathingOverlayViews.swift' (20/119)
Linting 'BreathingSupportingViews.swift' (21/119)
Linting 'AnxietyDetectionSheet.swift' (22/119)
Linting 'BreathingContentViews.swift' (23/119)
Linting 'UI/Views/Breathing/BreathingView.swift' (24/119)
Linting 'AITaskCoachView.swift' (25/119)
Linting 'UserRepository.swift' (26/119)
Linting 'TaskRepository.swift' (27/119)
Linting 'UserProfileRepository.swift' (28/119)
Linting 'RoutineRepository.swift' (29/119)
Linting 'ContentView.swift' (30/119)
Linting 'BreathingSessionRepository.swift' (31/119)
Linting 'NeurodiversityEnums.swift' (32/119)
Linting 'SettingsModels.swift' (33/119)
Linting 'OpenAIModels.swift' (34/119)
Linting 'UI/Views/Dashboard/DashboardView.swift' (35/119)
Linting 'BehaviorModels.swift' (36/119)
Linting 'CognitivePatternModels.swift' (37/119)
Linting 'TaskEnums.swift' (38/119)
Linting 'NeuroNexaModels.swift' (39/119)
Linting 'TaskTimingModels.swift' (40/119)
Linting 'BreathingModels.swift' (41/119)
Linting 'UserPreferences.swift' (42/119)
Linting 'OpenAIUserContextModels.swift' (43/119)
Linting 'BehaviorPredictionModels.swift' (44/119)
Linting 'ViewPlaceholders.swift' (45/119)
Linting 'NeurodiversityServices.swift' (46/119)
Linting 'AccessibilitySettings.swift' (47/119)
Linting 'SensoryOptimizationModels.swift' (48/119)
Linting 'OpenAITaskModels.swift' (49/119)
Linting 'OpenAIBreakModels.swift' (50/119)
Linting 'ExecutiveFunctionModels.swift' (51/119)
Linting 'OpenAITypes.swift' (52/119)
Linting 'OpenAICoachModels.swift' (53/119)
Linting 'User.swift' (54/119)
Linting 'SensoryEnums.swift' (55/119)
Linting 'PersonalizedContentTypes.swift' (56/119)
Linting 'CognitiveOptimizationModels.swift' (57/119)
Linting 'CognitivePreferencesModels.swift' (58/119)
Linting 'OpenAICognitiveAdaptationModels.swift' (59/119)
Linting 'NeuroNexaServices.swift' (60/119)
Linting 'NeuroNexaTheme.swift' (61/119)
Linting 'CognitiveSupportingTypes.swift' (62/119)
Linting 'SharedTypes.swift' (63/119)
Linting 'NeurodiversityTypes.swift' (64/119)
Linting 'UserProfileModels.swift' (65/119)
Linting 'SensoryModels.swift' (66/119)
Linting 'CognitiveModels.swift' (67/119)
Linting 'OpenAIContentModels.swift' (68/119)
Linting 'OpenAITaskCoachModels.swift' (69/119)
Linting 'CognitiveAnalysisModels.swift' (70/119)
Linting 'BreakSuggestionTypes.swift' (72/119)
Linting 'CognitiveAdaptationTypes.swift' (71/119)
Linting 'SensoryCognitiveModels.swift' (73/119)
Linting 'SensoryAdaptationModels.swift' (74/119)
Linting 'BreathingEnums.swift' (75/119)
Linting 'TaskModels.swift' (76/119)
Linting 'WellnessEnums.swift' (77/119)
Linting 'PrivacySettings.swift' (78/119)
Linting 'BehaviorInsightsModels.swift' (79/119)
Linting 'DependencyContainer.swift' (80/119)
Linting 'BehaviorAnalysisModels.swift' (81/119)
Linting 'ViewModel.swift' (82/119)
Linting 'Coordinator.swift' (83/119)
Linting 'ServiceProtocols.swift' (84/119)
Linting 'CognitiveLoadService.swift' (85/119)
Linting 'UserService.swift' (86/119)
Linting 'BreathingServiceHelpers.swift' (87/119)
Linting 'CognitiveAnalysisServiceExtensions.swift' (88/119)
Linting 'ExecutiveFunctionService.swift' (89/119)
Linting 'CoreDataService.swift' (90/119)
Linting 'PersonalizedTaskServiceExtensions.swift' (91/119)
Linting 'SensoryAdaptationService.swift' (92/119)
Linting 'OpenAIErrors.swift' (93/119)
Linting 'OpenAIService.swift' (94/119)
Linting 'WatchConnectivityService.swift' (95/119)
Linting 'HealthKitService.swift' (96/119)
Linting 'BasicServiceImplementations.swift' (97/119)
Linting 'OpenAITaskCoach.swift' (98/119)
Linting 'OpenAITaskCoachParsing.swift' (99/119)
Linting 'OpenAITaskCoachHelpers.swift' (100/119)
Linting 'OpenAITaskCoachExtensions.swift' (101/119)
Linting 'OpenAITaskCoachPrompts.swift' (102/119)
Linting 'CognitiveAnalysisHelpers.swift' (103/119)
Linting 'CognitiveAnalysisService.swift' (104/119)
Linting 'PersonalizedTaskHelpers.swift' (105/119)
Linting 'PersonalizedTaskService.swift' (106/119)
Linting 'PersonalizedTaskServiceGeneration.swift' (107/119)
Linting 'SettingsService.swift' (108/119)
Linting 'BreathingService.swift' (109/119)
Linting 'AuthenticationService.swift' (110/119)
Linting 'NeuroNexaTests.swift' (111/119)
Linting 'NeuroNexaUITests.swift' (112/119)
Linting 'BreathingExerciseServiceTests.swift' (113/119)
Linting 'BreathingSessionTests.swift' (114/119)
Linting 'AITaskCoachServiceTestsExtensions.swift' (116/119)
Linting 'AccessibilityAuditTests.swift' (115/119)
Linting 'AITaskCoachServiceTests.swift' (118/119)
Linting 'CloudKitService.swift' (119/119)
Linting 'NeurodiversityAccessibilityTests.swift' (117/119)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 423,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisService.swift",
    "line" : 20,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 347 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "File should contain 500 lines or less: currently contains 501",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 501,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 13,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 320,
    "reason" : "`where` clauses are preferred over a single `if` inside a `for`",
    "rule_id" : "for_where",
    "severity" : "Warning",
    "type" : "Prefer For-Where"
  },
  {
    "character" : 67,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 202,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 37,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 262,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  },
  {
    "character" : 41,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskServiceGeneration.swift",
    "line" : 264,
    "reason" : "Underscores should be used as thousand separators",
    "rule_id" : "number_separator",
    "severity" : "Warning",
    "type" : "Number Separator"
  }
]
Done linting! Found 8 violations, 0 serious in 119 files.
