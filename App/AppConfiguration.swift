import Foundation

@available(iOS 26.0, *)
struct AppConfiguration {
    static let shared = AppConfiguration()

    // App constants and configuration
    let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"

    // API Configuration
    static var openAIAPIKey: String {
        // First try to get from environment variable
        if let envKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] {
            return envKey
        }

        // Fallback to Info.plist
        if let plistKey = Bundle.main.infoDictionary?["OPENAI_API_KEY"] as? String {
            return plistKey
        }

        // Development fallback - should be set in environment or Info.plist
        fatalError("OpenAI API Key not found. Please set OPENAI_API_KEY environment variable or add it to Info.plist")
    }

    private init() {}
}
