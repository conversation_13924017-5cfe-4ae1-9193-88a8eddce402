import SwiftUI
import Combine

@available(iOS 18.0, *)
@MainActor
class DashboardViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var shouldSuggestBreak = false
    @Published var currentCognitiveLoad: CognitiveLoadLevel = .medium
    @Published var todaysTasks: [AITask] = []
    @Published var recentActivities: [String] = []
    @Published var currentStreak = 5
    
    // MARK: - Dependencies
    private let userRepository: UserRepositoryProtocol
    private let taskRepository: TaskRepositoryProtocol
    private let cognitiveLoadService: CognitiveLoadServiceProtocol
    
    // MARK: - Initialization
    init(
        userRepository: UserRepositoryProtocol = DependencyContainer.shared.userRepository,
        taskRepository: TaskRepositoryProtocol = DependencyContainer.shared.taskRepository,
        cognitiveLoadService: CognitiveLoadServiceProtocol = DependencyContainer.shared.cognitiveLoadService
    ) {
        self.userRepository = userRepository
        self.taskRepository = taskRepository
        self.cognitiveLoadService = cognitiveLoadService
        
        setupInitialData()
    }
    
    // MARK: - Public Methods
    func loadDashboardData() async {
        isLoading = true
        
        do {
            // Load cognitive load
            currentCognitiveLoad = try await cognitiveLoadService.getCurrentCognitiveLoad()
            
            // Load today's tasks
            let allTasks = try await taskRepository.getAllTasks()
            todaysTasks = allTasks.filter { task in
                Calendar.current.isDateInToday(task.createdAt)
            }
            
            // Load recent activities
            await loadRecentActivities()
            
            // Check if break is needed
            shouldSuggestBreak = await shouldSuggestBreakNow()
            
        } catch {
            print("Error loading dashboard data: \(error)")
        }
        
        isLoading = false
    }
    
    // MARK: - Private Methods
    private func setupInitialData() {
        // Set up mock data for development
        todaysTasks = [
            AITask(
                id: UUID(),
                title: "Review morning emails",
                description: "Check and respond to important emails",
                priority: .medium,
                estimatedDuration: 30,
                cognitiveLoad: .low,
                isCompleted: true,
                createdAt: Date(),
                updatedAt: Date()
            ),
            AITask(
                id: UUID(),
                title: "Prepare presentation",
                description: "Finalize slides for team meeting",
                priority: .high,
                estimatedDuration: 90,
                cognitiveLoad: .high,
                isCompleted: false,
                createdAt: Date(),
                updatedAt: Date()
            ),
            AITask(
                id: UUID(),
                title: "Take a short walk",
                description: "Get some fresh air and movement",
                priority: .low,
                estimatedDuration: 15,
                cognitiveLoad: .low,
                isCompleted: false,
                createdAt: Date(),
                updatedAt: Date()
            )
        ]
        
        recentActivities = [
            "Completed breathing exercise",
            "Started focus session",
            "Updated task priorities",
            "Reviewed daily goals"
        ]
    }
    
    private func loadRecentActivities() async {
        // Mock implementation - in real app, load from analytics service
        recentActivities = [
            "Completed breathing exercise - 5 min ago",
            "Started focus session - 15 min ago",
            "Updated task priorities - 1 hour ago",
            "Reviewed daily goals - 2 hours ago"
        ]
    }
    
    private func shouldSuggestBreakNow() async -> Bool {
        // Mock logic - in real app, check work duration, cognitive load, etc.
        let workDuration = TimeInterval.random(in: 3600...7200) // 1-2 hours
        let lastBreak = Date().addingTimeInterval(-workDuration)
        
        return Date().timeIntervalSince(lastBreak) > 3600 && currentCognitiveLoad == .high
    }
}
