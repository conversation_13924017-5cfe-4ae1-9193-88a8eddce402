import Foundation
import SwiftUI
import Combine

// MARK: - AI Task Coach ViewModel

/// ViewModel for the AI Task Coach feature with neurodiversity-first design
/// Manages task generation, coaching, and adaptive UI behavior
@available(iOS 18.0, *)
@MainActor
class AITaskCoachViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var tasks: [AITask] = []
    @Published var isGeneratingTasks = false
    @Published var isLoadingTasks = false
    @Published var generationProgress: Double = 0.0
    @Published var selectedTask: AITask?
    @Published var showingTaskDetails = false
    @Published var showingTaskBreakdown = false
    @Published var currentCognitiveLoad: CognitiveLoadLevel = .medium
    @Published var lastError: Error?
    @Published var showingError = false
    
    // Task filtering and sorting
    @Published var filterPriority: TaskPriority?
    @Published var filterCognitiveLoad: CognitiveLoadLevel?
    @Published var sortOption: TaskSortOption = .priority
    @Published var showCompletedTasks = false
    
    // Coaching state
    @Published var activeCoachingSession: CoachingSession?
    @Published var coachingInsights: [CoachingInsight] = []
    @Published var adaptiveRecommendations: [String] = []
    
    // MARK: - Dependencies
    private nonisolated(unsafe) let aiTaskCoachService: AITaskCoachServiceProtocol
    private nonisolated(unsafe) let cognitiveLoadService: CognitiveLoadServiceProtocol
    private nonisolated(unsafe) let userRepository: UserRepositoryProtocol
    private nonisolated(unsafe) let taskRepository: TaskRepositoryProtocol
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var currentUser: UserProfile?
    private nonisolated(unsafe) var taskGenerationTimer: Timer?
    private let adaptiveUIUpdateInterval: TimeInterval = 30.0 // 30 seconds
    
    // MARK: - Initialization
    init(
        aiTaskCoachService: AITaskCoachServiceProtocol,
        cognitiveLoadService: CognitiveLoadServiceProtocol,
        userRepository: UserRepositoryProtocol,
        taskRepository: TaskRepositoryProtocol
    ) {
        self.aiTaskCoachService = aiTaskCoachService
        self.cognitiveLoadService = cognitiveLoadService
        self.userRepository = userRepository
        self.taskRepository = taskRepository
        
        setupBindings()
        startAdaptiveUIUpdates()
    }
    
    // MARK: - Public Methods
    
    func loadInitialData() async {
        isLoadingTasks = true
        
        do {
            // Load current user profile
            currentUser = try await userRepository.getCurrentUser()
            
            // Load existing tasks
            let existingTasks = try await taskRepository.getAllTasks()
            tasks = existingTasks
            
            // Update cognitive load
            await updateCognitiveLoad()
            
            // Generate new tasks if needed
            if tasks.isEmpty || shouldGenerateNewTasks() {
                await generatePersonalizedTasks()
            }
            
        } catch {
            handleError(error)
        }
        
        isLoadingTasks = false
    }
    
    func generatePersonalizedTasks() async {
        guard let user = currentUser else {
            handleError(AITaskCoachViewModelError.userNotLoaded)
            return
        }
        
        isGeneratingTasks = true
        generationProgress = 0.0
        
        do {
            let newTasks = try await aiTaskCoachService.generatePersonalizedTasks(for: user)
            
            // Adapt tasks for current cognitive load
            let adaptedTasks = await aiTaskCoachService.adaptTasksForCognitiveLoad(newTasks, cognitiveLoad: currentCognitiveLoad)
            
            // Save tasks to repository
            for task in adaptedTasks {
                try await taskRepository.saveTask(task)
            }
            
            // Update UI
            tasks = adaptedTasks
            
            // Generate coaching insights
            await generateCoachingInsights()
            
        } catch {
            handleError(error)
        }
        
        isGeneratingTasks = false
        generationProgress = 1.0
    }
    
    func completeTask(_ task: AITask) async {
        do {
            // Mark task as completed
            var completedTask = task
            completedTask.isCompleted = true
            
            // Update in repository
            try await taskRepository.updateTask(completedTask)
            
            // Update local state
            if let index = tasks.firstIndex(where: { $0.id == task.id }) {
                tasks[index] = completedTask
            }
            
            // Create completion record for analysis
            let completion = TaskCompletion(
                taskId: task.id,
                userId: currentUser?.id ?? UUID(),
                actualDuration: task.estimatedDuration, // Would be actual tracked time
                estimatedDuration: task.estimatedDuration,
                cognitiveLoadExperienced: currentCognitiveLoad,
                difficultyRating: 3, // Would come from user input
                satisfactionRating: 4 // Would come from user input
            )
            
            // Analyze completion for learning
            await aiTaskCoachService.analyzeTaskCompletion(task, completion: completion)
            
            // Update coaching insights
            await generateCoachingInsights()
            
            // Provide haptic feedback
            provideCompletionFeedback()
            
        } catch {
            handleError(error)
        }
    }
    
    func breakdownTask(_ task: AITask) async {
        selectedTask = task
        
        do {
            let subtasks = await aiTaskCoachService.suggestTaskBreakdown(task)
            
            // Create a coaching session for the breakdown
            activeCoachingSession = CoachingSession(
                taskId: task.id,
                sessionType: .taskBreakdown,
                subtasks: subtasks,
                startedAt: Date()
            )
            
            showingTaskBreakdown = true
            
        } catch {
            handleError(error)
        }
    }
    
    func startCoachingSession(for task: AITask) {
        selectedTask = task
        
        activeCoachingSession = CoachingSession(
            taskId: task.id,
            sessionType: .activeCoaching,
            subtasks: [],
            startedAt: Date()
        )
        
        showingTaskDetails = true
    }
    
    func updateTaskPriority(_ task: AITask, priority: TaskPriority) async {
        do {
            var updatedTask = task
            updatedTask.priority = priority
            
            try await taskRepository.updateTask(updatedTask)
            
            if let index = tasks.firstIndex(where: { $0.id == task.id }) {
                tasks[index] = updatedTask
            }
            
        } catch {
            handleError(error)
        }
    }
    
    func refreshTasks() async {
        await loadInitialData()
    }
    
    // MARK: - Computed Properties
    
    var filteredAndSortedTasks: [AITask] {
        var filtered = tasks
        
        // Apply filters
        if let priorityFilter = filterPriority {
            filtered = filtered.filter { $0.priority == priorityFilter }
        }
        
        if let cognitiveLoadFilter = filterCognitiveLoad {
            filtered = filtered.filter { $0.cognitiveLoad == cognitiveLoadFilter }
        }
        
        if !showCompletedTasks {
            filtered = filtered.filter { !$0.isCompleted }
        }
        
        // Apply sorting
        switch sortOption {
        case .priority:
            filtered.sort(using: KeyPathComparator(\.priority.rawValue, order: .reverse))
        case .cognitiveLoad:
            filtered.sort(using: KeyPathComparator(\.cognitiveLoad.rawValue))
        case .estimatedDuration:
            filtered.sort(using: KeyPathComparator(\.estimatedDuration))
        case .dateCreated:
            filtered.sort { (task1: AITask, task2: AITask) in
                return task1.id.uuidString > task2.id.uuidString
            }
        }
        
        return filtered
    }
    
    var taskStats: TaskStats {
        let total = tasks.count
        let completed = tasks.filter { $0.isCompleted }.count
        let pending = total - completed
        let averageCognitiveLoad = calculateAverageCognitiveLoad()
        
        return TaskStats(
            total: total,
            completed: completed,
            pending: pending,
            averageCognitiveLoad: averageCognitiveLoad
        )
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Note: Protocol-based services don't expose publishers
        // Cognitive load updates will be handled manually
        
        // React to cognitive load changes
        $currentCognitiveLoad
            .dropFirst()
            .debounce(for: .seconds(2), scheduler: DispatchQueue.main)
            .sink { [weak self] newLoad in
                Task {
                    await self?.adaptTasksForCognitiveLoad(newLoad)
                }
            }
            .store(in: &cancellables)
    }
    
    private func startAdaptiveUIUpdates() {
        taskGenerationTimer = Timer.scheduledTimer(withTimeInterval: adaptiveUIUpdateInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.updateCognitiveLoad()
            }
        }
    }
    
    private func updateCognitiveLoad() async {
        let newLoad = await cognitiveLoadService.getCurrentCognitiveLoad()
        if newLoad != currentCognitiveLoad {
            currentCognitiveLoad = newLoad
        }
    }
    
    private func adaptTasksForCognitiveLoad(_ cognitiveLoad: CognitiveLoadLevel) async {
        let adaptedTasks = await aiTaskCoachService.adaptTasksForCognitiveLoad(tasks, cognitiveLoad: cognitiveLoad)
        tasks = adaptedTasks
        
        // Update adaptive recommendations
        await generateAdaptiveRecommendations()
    }
    
    private func shouldGenerateNewTasks() -> Bool {
        let incompleteTasks = tasks.filter { !$0.isCompleted }
        return incompleteTasks.count < 3 // Generate new tasks if fewer than 3 incomplete
    }
    
    private func generateCoachingInsights() async {
        guard let user = currentUser else { return }
        
        // Generate insights based on task completion patterns
        let completedTasks = tasks.filter { $0.isCompleted }
        
        var insights: [CoachingInsight] = []
        
        // Analyze completion patterns
        if completedTasks.count >= 3 {
            let avgDuration = completedTasks.map { $0.estimatedDuration }.reduce(0, +) / Double(completedTasks.count)
            
            insights.append(CoachingInsight(
                type: .performance,
                title: "Task Completion Pattern",
                description: "You typically complete tasks in \(Int(avgDuration / 60)) minutes",
                recommendation: "Consider breaking longer tasks into \(Int(avgDuration / 60))-minute chunks",
                confidence: 0.8
            ))
        }
        
        // Analyze cognitive load patterns
        let highLoadTasks = tasks.filter { $0.cognitiveLoad == .high || $0.cognitiveLoad == .overload }
        if !highLoadTasks.isEmpty {
            insights.append(CoachingInsight(
                type: .cognitiveLoad,
                title: "Cognitive Load Management",
                description: "You have \(highLoadTasks.count) high cognitive load tasks",
                recommendation: "Schedule these during your peak energy hours",
                confidence: 0.9
            ))
        }
        
        coachingInsights = insights
    }
    
    private func generateAdaptiveRecommendations() async {
        var recommendations: [String] = []
        
        switch currentCognitiveLoad {
        case .low:
            recommendations.append("Great time for challenging tasks!")
            recommendations.append("Consider tackling high-priority items")
            
        case .medium:
            recommendations.append("Good balance - mix easy and moderate tasks")
            recommendations.append("Take breaks between complex tasks")
            
        case .high:
            recommendations.append("Focus on simpler tasks right now")
            recommendations.append("Consider taking a short break")
            
        case .overload:
            recommendations.append("Time for a break - step away from tasks")
            recommendations.append("Try a breathing exercise or calming activity")
        }
        
        adaptiveRecommendations = recommendations
    }
    
    private func calculateAverageCognitiveLoad() -> CognitiveLoadLevel {
        guard !tasks.isEmpty else { return .medium }
        
        let loadValues = tasks.map { task in
            switch task.cognitiveLoad {
            case .low: return 1
            case .medium: return 2
            case .high: return 3
            case .overload: return 4
            }
        }
        
        let average = Double(loadValues.reduce(0, +)) / Double(loadValues.count)
        
        switch average {
        case 0..<1.5: return .low
        case 1.5..<2.5: return .medium
        case 2.5..<3.5: return .high
        default: return .overload
        }
    }
    
    private func provideCompletionFeedback() {
        // Provide haptic feedback for task completion
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // Could also trigger visual celebration or sound based on user preferences
    }
    
    private func handleError(_ error: Error) {
        lastError = error
        showingError = true
        print("AITaskCoachViewModel error: \(error)")
    }
    
    deinit {
        // Timer cleanup handled automatically
    }
}

// MARK: - Supporting Types

enum AITaskCoachViewModelError: Error, LocalizedError {
    case userNotLoaded
    case taskGenerationFailed
    case taskUpdateFailed
    
    var errorDescription: String? {
        switch self {
        case .userNotLoaded:
            return "User profile not loaded"
        case .taskGenerationFailed:
            return "Failed to generate personalized tasks"
        case .taskUpdateFailed:
            return "Failed to update task"
        }
    }
}

enum TaskSortOption: String, CaseIterable {
    case priority = "Priority"
    case cognitiveLoad = "Cognitive Load"
    case estimatedDuration = "Duration"
    case dateCreated = "Date Created"
}

struct TaskStats {
    let total: Int
    let completed: Int
    let pending: Int
    let averageCognitiveLoad: CognitiveLoadLevel
}

struct CoachingSession {
    let taskId: UUID
    let sessionType: CoachingSessionType
    let subtasks: [AITask]
    let startedAt: Date
    var endedAt: Date?
}

enum CoachingSessionType {
    case taskBreakdown
    case activeCoaching
    case review
}

struct CoachingInsight {
    let type: CoachingInsightType
    let title: String
    let description: String
    let recommendation: String
    let confidence: Double
}

enum CoachingInsightType {
    case performance
    case cognitiveLoad
    case pattern
    case recommendation
}
