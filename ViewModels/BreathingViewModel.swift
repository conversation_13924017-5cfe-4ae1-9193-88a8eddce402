import Foundation
import SwiftUI
import Combine

// MARK: - Breathing ViewModel

/// ViewModel for breathing and calming features with neurodiversity support
/// Manages breathing sessions, anxiety detection, and biometric monitoring
@available(iOS 18.0, *)
@MainActor
class BreathingViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isSessionActive = false
    @Published var isPaused = false
    @Published var currentExercise: BreathingExercise?
    @Published var currentPhase: BreathingPhase = .preparation
    @Published var sessionProgress: Double = 0.0
    @Published var elapsedTime: TimeInterval = 0
    @Published var remainingTime: TimeInterval = 0
    
    // Exercise management
    @Published var personalizedExercises: [BreathingExercise] = []
    @Published var quickStartExercises: [BreathingExercise] = []
    @Published var isLoadingExercises = false
    
    // Biometric data
    @Published var currentHeartRate: Double = 0
    @Published var currentHRV: Double = 0
    @Published var heartRateTrend: BiometricTrend = .stable
    @Published var hrvTrend: BiometricTrend = .stable
    @Published var isWatchConnected = false
    
    // Anxiety detection
    @Published var anxietyDetection: AnxietyOverwhelmDetection?
    @Published var isMonitoringAnxiety = false
    
    // Error handling
    @Published var lastError: Error?
    @Published var showingError = false
    
    // MARK: - Dependencies
    nonisolated(unsafe) private let breathingService: BreathingExerciseServiceProtocol
    nonisolated(unsafe) private let userRepository: UserRepositoryProtocol
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var currentUser: UserProfile?
    private var sessionStartTime: Date?
    nonisolated(unsafe) private var anxietyMonitoringTimer: Timer?
    
    // MARK: - Initialization
    init(
        breathingService: BreathingExerciseServiceProtocol,
        userRepository: UserRepositoryProtocol
    ) {
        self.breathingService = breathingService
        self.userRepository = userRepository
        
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    func loadPersonalizedExercises() async {
        isLoadingExercises = true
        
        do {
            // Load current user
            currentUser = try await userRepository.getCurrentUser()
            
            guard let user = currentUser else {
                throw BreathingViewModelError.userNotLoaded
                return
            }
            
            // Get personalized exercises
            let exercises = await breathingService.getPersonalizedExercises(for: user)
            personalizedExercises = exercises
            
            // Set quick start exercises (first 4 beginner exercises)
            quickStartExercises = Array(exercises.filter { $0.difficulty == .beginner }.prefix(4))
            
        } catch {
            handleError(error)
        }
        
        isLoadingExercises = false
    }
    
    func startBreathingSession(_ exercise: BreathingExercise) async {
        guard let user = currentUser else {
            handleError(BreathingViewModelError.userNotLoaded)
            return
        }
        
        do {
            sessionStartTime = Date()
            try await breathingService.startBreathingSession(exercise)
            
        } catch {
            handleError(error)
        }
    }
    
    func pauseSession() async {
        await breathingService.pauseSession()
    }
    
    func resumeSession() async {
        await breathingService.resumeSession()
    }
    
    func endSession() async {
        do {
            let result = try await breathingService.endBreathingSession()

            // Handle session completion
            await handleSessionCompletion(result)
        } catch {
            handleError(error)
        }
    }
    
    func startAnxietyMonitoring() async {
        guard let user = currentUser else { return }
        
        isMonitoringAnxiety = true
        
        // Start periodic anxiety detection
        anxietyMonitoringTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task {
                await self?.performAnxietyDetection()
            }
        }
    }
    
    func stopAnxietyMonitoring() {
        isMonitoringAnxiety = false
        anxietyMonitoringTimer?.invalidate()
        anxietyMonitoringTimer = nil
    }
    
    func dismissAnxietyDetection() {
        anxietyDetection = nil
    }
    
    func refreshExercises() async {
        await loadPersonalizedExercises()
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Note: Protocol doesn't expose published properties
        // State management handled locally in ViewModel
    }
    
    private func updateHeartRateMetrics(_ heartRateData: [HeartRateReading]) {
        guard !heartRateData.isEmpty else { return }
        
        // Update current heart rate
        currentHeartRate = heartRateData.last?.value ?? 0

        // Calculate trend
        if heartRateData.count >= 3 {
            let recent = Array(heartRateData.suffix(3))
            let trend = calculateTrend(recent.map { $0.value })
            heartRateTrend = trend
        }
    }
    
    private func updateHRVMetrics(_ hrvData: [HRVReading]) {
        guard !hrvData.isEmpty else { return }
        
        // Update current HRV
        currentHRV = hrvData.last?.value ?? 0
        
        // Calculate trend
        if hrvData.count >= 3 {
            let recent = Array(hrvData.suffix(3))
            let trend = calculateTrend(recent.map { $0.value })
            hrvTrend = trend
        }
    }
    
    private func calculateTrend(_ values: [Double]) -> BiometricTrend {
        guard values.count >= 2 else { return .stable }

        let first = values.first!
        let last = values.last!
        let change = (last - first) / first

        if change > 0.05 { // 5% increase
            return .improving
        } else if change < -0.05 { // 5% decrease
            return .declining
        } else {
            return .stable
        }
    }
    
    private func updateSessionTiming() {
        guard isSessionActive, let startTime = sessionStartTime, let exercise = currentExercise else {
            elapsedTime = 0
            remainingTime = 0
            return
        }
        
        let elapsed = Date().timeIntervalSince(startTime)
        elapsedTime = elapsed
        remainingTime = max(0, exercise.duration - elapsed)
    }
    
    private func performAnxietyDetection() async {
        guard let user = currentUser else { return }

        // Local anxiety detection based on heart rate and session data
        let triggers: [AnxietyTrigger] = currentHeartRate > 100 ? [
            AnxietyTrigger(type: .physical, description: "Elevated heart rate detected", intensity: 0.7)
        ] : []

        let recommendations: [AnxietyRecommendation] = currentHeartRate > 100 ? [
            AnxietyRecommendation(type: .breathing, title: "Deep Breathing", description: "Try a 4-7-8 breathing exercise")
        ] : []

        let detection = AnxietyOverwhelmDetection(
            overwhelmLevel: currentHeartRate > 100 ? .moderate : .none,
            anxietyScore: currentHeartRate > 100 ? 0.7 : 0.2,
            triggers: triggers,
            recommendations: recommendations,
            confidence: 0.7
        )

        // Only update if there's a significant change or new detection
        if shouldUpdateAnxietyDetection(detection) {
            anxietyDetection = detection
        }
            
    }

    private func shouldUpdateAnxietyDetection(_ newDetection: AnxietyOverwhelmDetection) -> Bool {
        guard let currentDetection = anxietyDetection else {
            // No current detection, update if not calm
            return newDetection.overwhelmLevel != .none
        }
        
        // Update if overwhelm level changed significantly
        let levelChanged = newDetection.overwhelmLevel != currentDetection.overwhelmLevel
        
        // Update if anxiety score changed by more than 0.2
        let scoreChanged = abs(newDetection.anxietyScore - currentDetection.anxietyScore) > 0.2
        
        // Update if it's been more than 5 minutes since last detection
        let timeElapsed = Date().timeIntervalSince(currentDetection.timestamp) > 300
        
        return levelChanged || scoreChanged || timeElapsed
    }
    
    private func handleSessionCompletion(_ result: BreathingSessionResult) async {
        // Reset session state
        sessionStartTime = nil
        
        // Show completion feedback based on effectiveness
        if result.stressReduction > 0.7 {
            // Excellent session
            await showCompletionFeedback("Excellent session! Your stress levels decreased significantly.")
        } else if result.stressReduction > 0.4 {
            // Good session
            await showCompletionFeedback("Good session! You completed \(Int(result.completionRate * 100))% of the exercise.")
        } else {
            // Encourage continued practice
            await showCompletionFeedback("Great effort! Regular practice will help improve your breathing technique.")
        }
        
        // Provide haptic feedback
        provideCompletionFeedback(result.stressReduction)
    }
    
    private func showCompletionFeedback(_ message: String) async {
        // This would typically show a toast or alert
        print("Session completed: \(message)")
    }
    
    private func provideCompletionFeedback(_ effectiveness: Double) {
        let impactStyle: UIImpactFeedbackGenerator.FeedbackStyle = effectiveness > 0.7 ? .heavy : .medium
        let impactFeedback = UIImpactFeedbackGenerator(style: impactStyle)
        impactFeedback.impactOccurred()
    }
    
    private func handleError(_ error: Error) {
        lastError = error
        showingError = true
        print("BreathingViewModel error: \(error)")
    }
    
    deinit {
        anxietyMonitoringTimer?.invalidate()
    }
}

// MARK: - Supporting Types

enum BreathingViewModelError: Error, LocalizedError {
    case userNotLoaded
    case sessionStartFailed
    case exerciseLoadFailed
    
    var errorDescription: String? {
        switch self {
        case .userNotLoaded:
            return "User profile not loaded"
        case .sessionStartFailed:
            return "Failed to start breathing session"
        case .exerciseLoadFailed:
            return "Failed to load breathing exercises"
        }
    }
}

// MARK: - Extensions

extension BreathingExercise {
    static let defaultExercise = BreathingExercise(
        name: "Basic Breathing",
        description: "Simple 4-4-4 breathing pattern",
        type: .boxBreathing,
        duration: 300, // 5 minutes
        inhalePattern: BreathingPattern.boxBreathing,
        exhalePattern: BreathingPattern.boxBreathing,
        difficulty: .beginner,
        benefits: ["Reduces anxiety", "Improves focus"],
        instructions: ["Breathe in for 4 counts", "Hold for 4 counts", "Breathe out for 4 counts"],
        neurodiversitySupport: [.adhd, .anxiety]
    )
}

// BreathingPattern definitions are in Core/Models/BreathingModels.swift

// MARK: - Mock Data for Development

#if DEBUG
extension BreathingExercise {
    static let mockExercises: [BreathingExercise] = [
        BreathingExercise(
            name: "Box Breathing",
            description: "4-4-4-4 pattern for focus and calm",
            type: .boxBreathing,
            duration: 300,
            inhalePattern: BreathingPattern.boxBreathing,
            exhalePattern: BreathingPattern.boxBreathing,
            difficulty: .beginner,
            benefits: ["Reduces stress", "Improves focus"],
            neurodiversitySupport: [.adhd, .anxiety]
        ),
        BreathingExercise(
            name: "Calming Breath",
            description: "Gentle breathing for overwhelm",
            type: .deepBreathing,
            duration: 180,
            inhalePattern: BreathingPattern.deepBreathing,
            exhalePattern: BreathingPattern.deepBreathing,
            difficulty: .beginner,
            benefits: ["Quick calming", "Easy to follow"],
            neurodiversitySupport: [.autism, .sensoryProcessingDifferences]
        )
    ]
}
#endif
