warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/116)
Linting 'NeuroNexaApp.swift' (2/116)
Linting 'AppDelegate.swift' (3/116)
Linting 'SettingsViewModel.swift' (4/116)
Linting 'SceneDelegate.swift' (5/116)
Linting 'BreathingViewModel.swift' (6/116)
Linting 'AITaskCoachViewModel.swift' (7/116)
Linting 'DashboardViewModel.swift' (8/116)
Linting 'iOS26Extensions.swift' (9/116)
Linting 'NeuroNexaDesignSystem.swift' (10/116)
Linting 'CognitiveProgressViewStyle.swift' (11/116)
Linting 'TaskCardConfiguration.swift' (12/116)
Linting 'CognitiveButton.swift' (13/116)
Linting 'TaskCard.swift' (14/116)
Linting 'UI/Views/BreathingView.swift' (15/116)
Linting 'UI/Views/DashboardView.swift' (16/116)
Linting 'UI/Views/Settings/SettingsView.swift' (18/116)
Linting 'UI/Views/SettingsView.swift' (17/116)
Linting 'BreathingSupportingViews.swift' (20/116)
Linting 'BreathingOverlayViews.swift' (21/116)
Linting 'UI/Views/Breathing/BreathingView.swift' (19/116)
Linting 'AnxietyDetectionSheet.swift' (23/116)
Linting 'BreathingContentViews.swift' (24/116)
Linting 'AITaskCoachView.swift' (25/116)
Linting 'ContentView.swift' (26/116)
Linting 'UserRepository.swift' (27/116)
Linting 'TaskRepository.swift' (28/116)
Linting 'RoutineRepository.swift' (29/116)
Linting 'UserProfileRepository.swift' (30/116)
Linting 'BreathingSessionRepository.swift' (31/116)
Linting 'NeurodiversityEnums.swift' (32/116)
Linting 'BreathingHelpers.swift' (22/116)
Linting 'CognitivePatternModels.swift' (33/116)
Linting 'UI/Views/Dashboard/DashboardView.swift' (34/116)
Linting 'OpenAIModels.swift' (35/116)
Linting 'BehaviorModels.swift' (36/116)
Linting 'SettingsModels.swift' (37/116)
Linting 'NeuroNexaModels.swift' (38/116)
Linting 'BehaviorPredictionModels.swift' (39/116)
Linting 'TaskEnums.swift' (40/116)
Linting 'UserPreferences.swift' (41/116)
Linting 'TaskTimingModels.swift' (42/116)
Linting 'ViewPlaceholders.swift' (43/116)
Linting 'OpenAIUserContextModels.swift' (44/116)
Linting 'BreathingModels.swift' (45/116)
Linting 'OpenAITaskModels.swift' (46/116)
Linting 'NeurodiversityServices.swift' (47/116)
Linting 'OpenAIBreakModels.swift' (48/116)
Linting 'OpenAITypes.swift' (49/116)
Linting 'SensoryOptimizationModels.swift' (50/116)
Linting 'AccessibilitySettings.swift' (51/116)
Linting 'ExecutiveFunctionModels.swift' (52/116)
Linting 'User.swift' (53/116)
Linting 'OpenAICoachModels.swift' (54/116)
Linting 'SensoryEnums.swift' (55/116)
Linting 'CognitiveOptimizationModels.swift' (56/116)
Linting 'OpenAICognitiveAdaptationModels.swift' (57/116)
Linting 'CognitivePreferencesModels.swift' (58/116)
Linting 'NeuroNexaServices.swift' (59/116)
Linting 'PersonalizedContentTypes.swift' (60/116)
Linting 'NeuroNexaTheme.swift' (61/116)
Linting 'NeurodiversityTypes.swift' (62/116)
Linting 'SensoryModels.swift' (63/116)
Linting 'SharedTypes.swift' (64/116)
Linting 'CognitiveSupportingTypes.swift' (65/116)
Linting 'UserProfileModels.swift' (66/116)
Linting 'OpenAIContentModels.swift' (67/116)
Linting 'OpenAITaskCoachModels.swift' (68/116)
Linting 'CognitiveModels.swift' (69/116)
Linting 'SensoryCognitiveModels.swift' (70/116)
Linting 'BreakSuggestionTypes.swift' (71/116)
Linting 'CognitiveAnalysisModels.swift' (72/116)
Linting 'CognitiveAdaptationTypes.swift' (73/116)
Linting 'TaskModels.swift' (74/116)
Linting 'BreathingEnums.swift' (75/116)
Linting 'SensoryAdaptationModels.swift' (76/116)
Linting 'PrivacySettings.swift' (77/116)
Linting 'WellnessEnums.swift' (78/116)
Linting 'BehaviorInsightsModels.swift' (79/116)
Linting 'ViewModel.swift' (80/116)
Linting 'BehaviorAnalysisModels.swift' (81/116)
Linting 'DependencyContainer.swift' (82/116)
Linting 'Coordinator.swift' (83/116)
Linting 'ServiceProtocols.swift' (84/116)
Linting 'CognitiveLoadService.swift' (85/116)
Linting 'CoreDataService.swift' (86/116)
Linting 'ExecutiveFunctionService.swift' (87/116)
Linting 'UserService.swift' (88/116)
Linting 'CognitiveAnalysisServiceExtensions.swift' (89/116)
Linting 'SensoryAdaptationService.swift' (90/116)
Linting 'PersonalizedTaskServiceExtensions.swift' (91/116)
Linting 'HealthKitService.swift' (93/116)
Linting 'BasicServiceImplementations.swift' (94/116)
Linting 'WatchConnectivityService.swift' (92/116)
Linting 'OpenAIService.swift' (95/116)
Linting 'OpenAIErrors.swift' (96/116)
Linting 'OpenAITaskCoach.swift' (97/116)
Linting 'OpenAITaskCoachParsing.swift' (98/116)
Linting 'OpenAITaskCoachExtensions.swift' (99/116)
Linting 'OpenAITaskCoachHelpers.swift' (100/116)
Linting 'PersonalizedTaskHelpers.swift' (101/116)
Linting 'CognitiveAnalysisService.swift' (102/116)
Linting 'CognitiveAnalysisHelpers.swift' (103/116)
Linting 'SettingsService.swift' (104/116)
Linting 'PersonalizedTaskService.swift' (105/116)
Linting 'BreathingService.swift' (106/116)
Linting 'AuthenticationService.swift' (107/116)
Linting 'NeuroNexaUITests.swift' (108/116)
Linting 'NeuroNexaTests.swift' (109/116)
Linting 'CloudKitService.swift' (111/116)
Linting 'AccessibilityAuditTests.swift' (112/116)
Linting 'BreathingSessionTests.swift' (113/116)
Linting 'NeurodiversityAccessibilityTests.swift' (110/116)
Linting 'AITaskCoachServiceTests.swift' (114/116)
Linting 'AITaskCoachServiceTestsExtensions.swift' (115/116)
Linting 'BreathingExerciseServiceTests.swift' (116/116)
[
  {
    "character" : 9,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/HealthKitService.swift",
    "line" : 204,
    "reason" : "Prefer implicit returns in closures, functions and getters",
    "rule_id" : "implicit_return",
    "severity" : "Warning",
    "type" : "Implicit Return"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 488,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/AI/OpenAITaskCoach.swift",
    "line" : 22,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 339 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/PersonalizedTaskService.swift",
    "line" : 20,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 359 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 522,
    "reason" : "File should contain 500 lines or less: currently contains 522",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/BreathingService.swift",
    "line" : 522,
    "reason" : "Files should have a single trailing newline",
    "rule_id" : "trailing_newline",
    "severity" : "Warning",
    "type" : "Trailing Newline"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/Core/Services/CognitiveAnalysisService.swift",
    "line" : 20,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 347 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  }
]
Done linting! Found 7 violations, 0 serious in 116 files.
