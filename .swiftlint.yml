# NeuroNexa SwiftLint Configuration for iOS 26

# Paths to include/exclude
included:
  - App
  - UI
  - Core
  - Data
  - Tests
  - WatchOS

excluded:
  - Pods
  - Build
  - .build
  - fastlane
  - Reports
  - Configuration/Generated

# Rules
disabled_rules:
  - trailing_whitespace
  - line_length

opt_in_rules:
  - accessibility_label_for_image
  - accessibility_trait_for_button
  - array_init
  - closure_spacing
  - collection_alignment
  - contains_over_filter_count
  - empty_collection_literal
  - empty_count
  - empty_string
  - enum_case_associated_values_count
  - explicit_init
  - extension_access_modifier
  - fallthrough
  - fatal_error_message
  - file_header
  - first_where
  - force_unwrapping
  - function_default_parameter_at_end
  - identical_operands
  - implicit_return
  - joined_default_parameter
  - last_where
  - legacy_random
  - literal_expression_end_indentation
  - lower_acl_than_parent
  - modifier_order
  - nimble_operator
  - nslocalizedstring_key
  - number_separator
  - object_literal
  - operator_usage_whitespace
  - overridden_super_call
  - override_in_extension
  - pattern_matching_keywords
  - prefer_self_type_over_type_of_self
  - private_action
  - private_outlet
  - prohibited_super_call
  - quick_discouraged_call
  - quick_discouraged_focused_test
  - quick_discouraged_pending_test
  - reduce_into
  - redundant_nil_coalescing
  - redundant_type_annotation
  - single_test_class
  - sorted_first_last
  - sorted_imports
  - static_operator
  - strong_iboutlet
  - toggle_bool
  - unavailable_function
  - unneeded_parentheses_in_closure_argument
  - unowned_variable_capture
  - untyped_error_in_catch
  - vertical_parameter_alignment_on_call
  - vertical_whitespace_closing_braces
  - vertical_whitespace_opening_braces
  - xct_specific_matcher
  - yoda_condition

# Custom rules for neurodiversity-focused development
custom_rules:
  no_apple_intelligence:
    name: "No Apple Intelligence"
    regex: "^(?!.*//.*)(.*)(Apple.*Intelligence|AppleIntelligence)(?!.*//)"
    message: "Apple Intelligence is prohibited. Use OpenAI implementation instead. See Documentation/APPLE_INTELLIGENCE_PROHIBITION_POLICY.md"
    severity: error

  # no_apple_intelligence_imports:
    # name: "No Apple Intelligence Imports"
    # regex: "import.*Apple.*Intelligence|import.*Intelligence(?!Service)"
    # message: "Apple Intelligence imports are prohibited. Use OpenAI services instead."
    # severity: error

  no_apple_intelligence_services:
    name: "No Apple Intelligence Services"
    regex: "^(?!.*//.*)(.*)(AppleIntelligenceService)(?!.*//)"
    message: "AppleIntelligenceService is prohibited. Use OpenAITaskCoach instead."
    severity: error
  cognitive_load_consideration:
    name: "Cognitive Load Consideration"
    regex: 'cognitiveLoad'
    message: "Good practice: considering cognitive load in UI design"
    severity: info

# Configuration
line_length:
  warning: 120
  error: 150

function_body_length:
  warning: 50
  error: 100

type_body_length:
  warning: 300
  error: 500

file_length:
  warning: 500
  error: 1000

cyclomatic_complexity:
  warning: 10
  error: 20

nesting:
  type_level:
    warning: 2
    error: 3
  function_level:
    warning: 5
    error: 10

identifier_name:
  min_length:
    warning: 2
    error: 1
  max_length:
    warning: 50
    error: 100
  excluded:
    - id
    - x
    - y
    - z

reporter: "xcode"
