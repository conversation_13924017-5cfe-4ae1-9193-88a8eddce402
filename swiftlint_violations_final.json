warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/39)
Linting 'NeuroNexaApp.swift' (2/39)
Linting 'AppDelegate.swift' (3/39)
Linting 'NeurodiversityModifiers.swift' (4/39)
Linting 'iOS26Extensions.swift' (6/39)
Linting 'SceneDelegate.swift' (5/39)
Linting 'CognitiveProgressViewStyle.swift' (8/39)
Linting 'NeuroNexaDesignSystem.swift' (7/39)
Linting 'TaskCardConfiguration.swift' (10/39)
Linting 'CognitiveButton.swift' (9/39)
Linting 'TaskCard.swift' (11/39)
Linting 'SettingsView.swift' (12/39)
Linting 'BreathingView.swift' (13/39)
Linting 'BreathingSupportingViews.swift' (14/39)
Linting 'BreathingHelpers.swift' (15/39)
Linting 'AnxietyDetectionSheet.swift' (16/39)
Linting 'BreathingOverlayViews.swift' (17/39)
Linting 'BreathingContentViews.swift' (18/39)
Linting 'DashboardView.swift' (19/39)
Linting 'ContentView.swift' (20/39)
Linting 'NeuroNexaModels.swift' (21/39)
Linting 'NeurodiversityEnums.swift' (22/39)
Linting 'TaskEnums.swift' (23/39)
Linting 'SensoryEnums.swift' (24/39)
Linting 'User.swift' (25/39)
Linting 'BreathingEnums.swift' (26/39)
Linting 'WellnessEnums.swift' (27/39)
Linting 'ViewModel.swift' (28/39)
Linting 'Coordinator.swift' (29/39)
Linting 'OpenAITaskCoach.swift' (30/39)
Linting 'AuthenticationService.swift' (31/39)
Linting 'NeuroNexaUITests.swift' (32/39)
Linting 'NeuroNexaTests.swift' (33/39)
Linting 'NeurodiversityAccessibilityTests.swift' (34/39)
Linting 'DependencyContainer.swift' (35/39)
Linting 'AccessibilityAuditTests.swift' (36/39)
Linting 'BreathingExerciseServiceTests.swift' (37/39)
Linting 'BreathingSessionTests.swift' (38/39)
Linting 'AITaskCoachServiceTests.swift' (39/39)
[

]
Done linting! Found 0 violations, 0 serious in 39 files.
