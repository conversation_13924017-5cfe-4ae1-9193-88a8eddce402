# NeuroNexa Project - Augment Code + Claude Code Integration Rules

## 🚨 MANDATORY DEVELOPMENT ENVIRONMENT (ENFORCED)
- **RULE #1**: NeuroNexa project requires EXCLUSIVE use of Xcode Beta 26 and iOS 26 ONLY
- **RULE #2**: NO development permitted on iOS 25.x, iOS 24.x, or any earlier versions
- **RULE #3**: NO development permitted using stable Xcode versions (16.x, 15.x, etc.)
- **ENFORCEMENT**: Automated validation scripts prevent non-compliant development
- **VALIDATION**: `./Scripts/validate-environment.sh` must pass before any development work

## 1. Core Project Requirements
- NeuroNexa project requires mandatory use of Xcode Beta 26 and iOS 26 only, plus watchOS 26 integration support
- NeuroNexa project requires 100% SwiftLint compliance
- NeuroNexa project requires neurodiversity-first design principles adherence
- NeuroNexa project requires systematic task management approach
- NeuroNexa project requires comprehensive testing including accessibility requirements
- NeuroNexa project goal is to achieve 100% App Store deployment readiness

## 2. Development Tools and Practices
- NeuroNexa project prefers using Context7 to build reference libraries for iOS Swift development
- NeuroNexa project prefers using MCP tools for development efficiency
- SwiftLintMCP for automatic violation fixes
- GitHub MCP for repository access and best practices
- Tool-assisted development over manual code fixes

## 3. AI Feature Implementation
- NeuroNexa project uses OpenAI for AI features, not Apple Intelligence
- Remove all Apple Intelligence integrations and replace with OpenAI implementation

## 4. Architecture Requirements
- SwiftUI with MVVM-C (Model-View-ViewModel-Coordinator) pattern
- Dependency injection using DependencyContainer
- Repository pattern for data access
- Service layer for business logic
- Neurodiversity-first design principles

## 5A. Augment Code Primary Agent Rules
- Augment Code is the PRIMARY autonomous agent for this project
- Augment Code has FULL write access to all project files
- Augment Code manages task planning, execution, and validation
- Augment Code controls all Git operations (commit, push, merge)
- Augment Code validates all architectural decisions
- Augment Code maintains project documentation and reference libraries

## 5B. Augment + Claude Code Integration Rules

### Claude Code Role Definition
- Claude Code serves as a SECONDARY agent under Augment Code supervision
- Claude Code is a CONTROLLED WRITE assistant with pre-approved modification categories
- Claude Code provides suggestions, analysis, and implements approved optimizations
- Claude Code NEVER pushes changes without Augment Code approval
- Claude Code follows all project rules defined in this document

### Claude Code Controlled Write Permissions
✅ **ALLOWED WITH APPROVAL:**
- SwiftUI view optimizations (subview extraction, performance improvements)
- Accessibility enhancements (adding accessibility labels, VoiceOver support)
- Code style fixes (SwiftLint compliance, formatting)
- Comment additions with `// Claude:` prefix for suggestions
- Documentation improvements and updates
- Test coverage enhancements
- Performance optimization implementations

✅ **ALLOWED (ANALYSIS ONLY):**
- Code analysis and review
- ViewModel logic cleanup recommendations
- AI and HealthKit service pattern analysis
- Architecture pattern compliance checks

❌ **STRICTLY PROHIBITED:**
- Git operations (commit, push, pull, merge)
- Dependency management (package.json, Package.swift modifications)
- Core architecture files (`/Core/Architecture/DependencyContainer.swift`, Coordinators)
- Design system changes (`/UI/NeuroNexaDesignSystem.swift`)
- Project configuration changes (Xcode project settings, build configurations)
- CI/CD pipeline modifications
- Service layer architecture changes (`/Services/` core structure)

### Claude Code Communication Protocol
- All Claude Code suggestions must be prefixed with `// Claude:`
- Claude Code must route all refactor suggestions back to Augment Code
- Claude Code must confirm architectural suggestions with Augment Code
- Claude Code provides diff previews for all proposed modifications
- Claude Code uses inline comments for improvement suggestions

### Controlled Modification Workflow
1. **Augment Code** initiates Claude Code review/modification sessions
2. **Claude Code** analyzes specified files/components
3. **Claude Code** generates diff previews for proposed changes
4. **Augment Code** reviews and approves/rejects modifications
5. **Claude Code** implements only approved changes
6. **Augment Code** validates final results and maintains change log
7. **Rollback capability** maintained for all Claude modifications

### Approval Process for File Modifications
- **Diff Preview Required**: Claude Code must show exact changes before implementation
- **Category Validation**: Modifications must fall within approved categories
- **Augment Approval**: Explicit approval required before any file changes
- **Change Logging**: All modifications logged with timestamp and rationale
- **Rollback Ready**: Git state preserved for easy rollback if needed

### Claude Code Focus Areas
- SwiftUI view optimizations
- ViewModel logic cleanup
- Accessibility compliance checks
- Performance bottleneck identification
- Code style and SwiftLint compliance
- Test coverage gaps
- Documentation improvements

### Escalation Protocol
- Claude Code escalates complex architectural decisions to Augment Code
- Claude Code flags potential security or performance issues
- Claude Code requests clarification on neurodiversity-first design principles
- Augment Code provides final approval for all suggested changes

## 6. Environment Compliance Enforcement
- All development sessions must begin with environment validation
- CI/CD pipelines automatically reject non-compliant builds
- Pre-commit hooks prevent commits with legacy iOS version references
- Documentation must reflect iOS 26/Xcode Beta 26 exclusive requirements

## 7. Quality Assurance
- 100% SwiftLint compliance maintained at all times
- Comprehensive test coverage for all features
- Accessibility testing for neurodiversity support
- Performance monitoring and optimization
- Code review process with Claude Code assistance

## 8. Documentation Standards
- All code changes must be documented
- Reference libraries must be kept up-to-date
- Architecture decisions must be recorded
- API documentation must be comprehensive
- User guides must reflect neurodiversity-first principles
