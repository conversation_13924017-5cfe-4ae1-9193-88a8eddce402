warning: Invalid configuration for 'cognitive_load_consideration' rule. Falling back to default.
warning: Found a configuration for 'line_length' rule, but it is disabled in 'disabled_rules'.
Linting Swift files in current working directory
Linting 'AppConfiguration.swift' (1/37)
Linting 'NeuroNexaApp.swift' (2/37)
Linting 'AppDelegate.swift' (3/37)
Linting 'SceneDelegate.swift' (4/37)
Linting 'NeurodiversityModifiers.swift' (5/37)
Linting 'iOS26Extensions.swift' (6/37)
Linting 'NeuroNexaDesignSystem.swift' (7/37)
Linting 'CognitiveProgressViewStyle.swift' (8/37)
Linting 'TaskCardConfiguration.swift' (9/37)
Linting 'CognitiveButton.swift' (10/37)
Linting 'TaskCard.swift' (11/37)
Linting 'SettingsView.swift' (12/37)
Linting 'BreathingSupportingViews.swift' (13/37)
Linting 'BreathingView.swift' (14/37)
Linting 'BreathingOverlayViews.swift' (15/37)
Linting 'ContentView.swift' (17/37)
Linting 'AnxietyDetectionSheet.swift' (16/37)
Linting 'NeuroNexaModels.swift' (18/37)
Linting 'DashboardView.swift' (20/37)
Linting 'TaskEnums.swift' (19/37)
Linting 'SensoryEnums.swift' (22/37)
Linting 'User.swift' (21/37)
Linting 'NeurodiversityEnums.swift' (23/37)
Linting 'BreathingEnums.swift' (24/37)
Linting 'DependencyContainer.swift' (25/37)
Linting 'WellnessEnums.swift' (26/37)
Linting 'ViewModel.swift' (27/37)
Linting 'OpenAITaskCoach.swift' (28/37)
Linting 'NeuroNexaUITests.swift' (29/37)
Linting 'Coordinator.swift' (30/37)
Linting 'NeurodiversityAccessibilityTests.swift' (31/37)
Linting 'AccessibilityAuditTests.swift' (33/37)
Linting 'AuthenticationService.swift' (32/37)
Linting 'BreathingExerciseServiceTests.swift' (34/37)
Linting 'BreathingSessionTests.swift' (35/37)
Linting 'NeuroNexaTests.swift' (36/37)
Linting 'AITaskCoachServiceTests.swift' (37/37)
[
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/Core/Architecture/DependencyContainer.swift",
    "line" : 1,
    "reason" : "Files should not contain leading whitespace",
    "rule_id" : "leading_whitespace",
    "severity" : "Warning",
    "type" : "Leading Whitespace"
  },
  {
    "character" : 8,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingOverlayViews.swift",
    "line" : 2,
    "reason" : "Imports should be sorted",
    "rule_id" : "sorted_imports",
    "severity" : "Warning",
    "type" : "Sorted Imports"
  },
  {
    "character" : null,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 597,
    "reason" : "File should contain 500 lines or less: currently contains 597",
    "rule_id" : "file_length",
    "severity" : "Warning",
    "type" : "File Length"
  },
  {
    "character" : 1,
    "file" : "/Users/<USER>/Neuronexa/UI/Views/Breathing/BreathingView.swift",
    "line" : 8,
    "reason" : "Type body should span 300 lines or less excluding comments and whitespace: currently spans 466 lines",
    "rule_id" : "type_body_length",
    "severity" : "Warning",
    "type" : "Type Body Length"
  }
]
Done linting! Found 4 violations, 0 serious in 37 files.
