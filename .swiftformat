# NeuroNexa SwiftFormat Configuration for iOS 26

# Version
--swiftversion 6.0

# Enabled rules for NeuroNexa
--enable isEmpty
--enable sortedImports
--enable redundantSelf
--enable redundantReturn
--enable redundantParens
--enable redundantGet
--enable redundantInit
--enable redundantVoidReturnType
--enable redundantNilInit
--enable redundantLet
--enable redundantPattern
--enable redundantRawValues
--enable redundantObjc
--enable redundantFileprivate
--enable redundantExtensionACL
--enable redundantBreak
--enable redundantClosure
--enable redundantBackticks
--enable redundantType
--enable unusedArguments
--enable duplicateImports
--enable hoistPatternLet
--enable preferKeyPath
--enable strongOutlets
--enable trailingClosures
--enable wrap
--enable wrapArguments
--enable yodaConditions
--enable leadingDelimiters
--enable numberFormatting
--enable andOperator
--enable anyObjectProtocol
--enable blankLinesAtEndOfScope
--enable blankLinesAtStartOfScope
--enable blankLinesBetweenScopes
--enable braces
--enable consecutiveBlankLines
--enable consecutiveSpaces
--enable elseOnSameLine
--enable emptyBraces
--enable fileHeader
--enable hoistTry
--enable initCoderUnavailable
--enable linebreakAtEndOfFile
--enable linebreaks
--enable modifierOrder
--enable organizeDeclarations
--enable preferForLoop
--enable spaceAroundBraces
--enable spaceAroundBrackets
--enable spaceAroundComments
--enable spaceAroundGenerics
--enable spaceAroundOperators
--enable spaceAroundParens
--enable spaceInsideBraces
--enable spaceInsideBrackets
--enable spaceInsideComments
--enable spaceInsideGenerics
--enable spaceInsideParens
--enable specifiers
--enable strongifiedSelf
--enable todos
--enable typeSugar
--enable void
--enable wrapMultilineStatementBraces

# Options
--indent 4
--tabwidth 4
--maxwidth 120
--wraparguments before-first
--wrapparameters before-first
--wrapcollections before-first
--closingparen balanced
--commas inline
--trimwhitespace always
--insertlines enabled
--removelines enabled
--allman false
--stripunusedargs closure-only
--self remove
--importgrouping testable-bottom
--ifdef no-indent
--redundanttype inferred
--nospaceoperators ...,..<
--ranges no-space
--typeattributes prev-line
--varattributes prev-line
--funcattributes prev-line
--modifierorder private,fileprivate,internal,public,open,override,convenience,required,static,class,final,lazy,weak,unowned,@objc,@nonobjc,@available,@IBAction,@IBOutlet,@IBDesignable,@IBInspectable,@GKInspectable,@NSManaged,@UIApplicationMain,@NSApplicationMain
