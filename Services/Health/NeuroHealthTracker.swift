import Foundation
import Combine

// Import shared types for cross-module dependencies

// MARK: - Neuro Health Tracker

/// Service for tracking neurodiversity-related health metrics and patterns
@available(iOS 18.0, *)
class NeuroHealthTracker: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentMoodLevel: MoodLevel = .neutral
    @Published var currentEnergyLevel: EnergyLevel = .moderate
    @Published var currentFocusLevel: FocusLevel = .moderate
    @Published var currentStressLevel: StressLevel = .low
    @Published var dailyMetrics: [DailyHealthMetric] = []
    @Published var weeklyTrends: HealthTrends?
    @Published var isLoading = false
    @Published var lastError: Error?
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let healthRepository: HealthRepositoryProtocol
    
    // MARK: - Initialization
    init(healthRepository: HealthRepositoryProtocol = HealthRepository()) {
        self.healthRepository = healthRepository
        loadHealthData()
    }
    
    // MARK: - Public Methods
    
    /// Load health data
    func loadHealthData() {
        isLoading = true
        
        Task {
            do {
                let metrics = try await healthRepository.getDailyMetrics()
                let trends = try await healthRepository.getWeeklyTrends()
                
                await MainActor.run {
                    self.dailyMetrics = metrics
                    self.weeklyTrends = trends
                    self.updateCurrentLevels()
                    self.isLoading = false
                    self.lastError = nil
                }
            } catch {
                await MainActor.run {
                    self.lastError = error
                    self.isLoading = false
                }
            }
        }
    }
    
    /// Record mood level
    func recordMood(_ mood: MoodLevel) async throws {
        let metric = DailyHealthMetric(
            date: Date(),
            mood: mood,
            energy: currentEnergyLevel,
            focus: currentFocusLevel,
            stress: currentStressLevel
        )
        
        try await saveMetric(metric)
        
        await MainActor.run {
            self.currentMoodLevel = mood
        }
    }
    
    /// Record energy level
    func recordEnergy(_ energy: EnergyLevel) async throws {
        let metric = DailyHealthMetric(
            date: Date(),
            mood: currentMoodLevel,
            energy: energy,
            focus: currentFocusLevel,
            stress: currentStressLevel
        )
        
        try await saveMetric(metric)
        
        await MainActor.run {
            self.currentEnergyLevel = energy
        }
    }
    
    /// Record focus level
    func recordFocus(_ focus: FocusLevel) async throws {
        let metric = DailyHealthMetric(
            date: Date(),
            mood: currentMoodLevel,
            energy: currentEnergyLevel,
            focus: focus,
            stress: currentStressLevel
        )
        
        try await saveMetric(metric)
        
        await MainActor.run {
            self.currentFocusLevel = focus
        }
    }
    
    /// Record stress level
    func recordStress(_ stress: StressLevel) async throws {
        let metric = DailyHealthMetric(
            date: Date(),
            mood: currentMoodLevel,
            energy: currentEnergyLevel,
            focus: currentFocusLevel,
            stress: stress
        )
        
        try await saveMetric(metric)
        
        await MainActor.run {
            self.currentStressLevel = stress
        }
    }
    
    /// Get health insights
    func getHealthInsights() async throws -> HealthInsights {
        let metrics = try await healthRepository.getDailyMetrics()
        let recentMetrics = metrics.suffix(7) // Last 7 days
        
        let avgMood = recentMetrics.map { $0.mood.rawValue }.reduce(0, +) / recentMetrics.count
        let avgEnergy = recentMetrics.map { $0.energy.rawValue }.reduce(0, +) / recentMetrics.count
        let avgFocus = recentMetrics.map { $0.focus.rawValue }.reduce(0, +) / recentMetrics.count
        let avgStress = recentMetrics.map { $0.stress.rawValue }.reduce(0, +) / recentMetrics.count
        
        return HealthInsights(
            averageMood: avgMood,
            averageEnergy: avgEnergy,
            averageFocus: avgFocus,
            averageStress: avgStress,
            recommendations: generateRecommendations(
                mood: avgMood,
                energy: avgEnergy,
                focus: avgFocus,
                stress: avgStress
            )
        )
    }
    
    // MARK: - Private Methods
    
    private func saveMetric(_ metric: DailyHealthMetric) async throws {
        try await healthRepository.saveMetric(metric)
        
        await MainActor.run {
            // Update or add to daily metrics
            if let index = self.dailyMetrics.firstIndex(where: { 
                Calendar.current.isDate($0.date, inSameDayAs: metric.date) 
            }) {
                self.dailyMetrics[index] = metric
            } else {
                self.dailyMetrics.append(metric)
            }
        }
    }
    
    private func updateCurrentLevels() {
        guard let todayMetric = dailyMetrics.first(where: { 
            Calendar.current.isDate($0.date, inSameDayAs: Date()) 
        }) else { return }
        
        currentMoodLevel = todayMetric.mood
        currentEnergyLevel = todayMetric.energy
        currentFocusLevel = todayMetric.focus
        currentStressLevel = todayMetric.stress
    }
    
    private func generateRecommendations(
        mood: Int,
        energy: Int,
        focus: Int,
        stress: Int
    ) -> [String] {
        var recommendations: [String] = []
        
        if mood < 3 {
            recommendations.append("Consider engaging in mood-boosting activities like listening to music or spending time in nature.")
        }
        
        if energy < 3 {
            recommendations.append("Try gentle movement or breathing exercises to boost your energy levels.")
        }
        
        if focus < 3 {
            recommendations.append("Break tasks into smaller chunks and use focus techniques like the Pomodoro method.")
        }
        
        if stress > 3 {
            recommendations.append("Practice stress-reduction techniques like deep breathing or mindfulness meditation.")
        }
        
        return recommendations
    }
}

// MARK: - Supporting Types

// Note: MoodReading is now defined in Core/Models/SharedTypes.swift

public enum MoodLevel: Int, CaseIterable, Codable {
    case veryLow = 1, low = 2, neutral = 3, good = 4, excellent = 5
}

public enum EnergyLevel: Int, CaseIterable, Codable {
    case veryLow = 1, low = 2, moderate = 3, high = 4, veryHigh = 5
}

public enum FocusLevel: Int, CaseIterable, Codable {
    case veryLow = 1, low = 2, moderate = 3, high = 4, veryHigh = 5
}

public enum StressLevel: Int, CaseIterable, Codable {
    case veryLow = 1, low = 2, moderate = 3, high = 4, veryHigh = 5
}

struct DailyHealthMetric: Identifiable, Codable {
    let id = UUID()
    let date: Date
    let mood: MoodLevel
    let energy: EnergyLevel
    let focus: FocusLevel
    let stress: StressLevel
}

struct HealthTrends: Codable {
    let moodTrend: TrendDirection
    let energyTrend: TrendDirection
    let focusTrend: TrendDirection
    let stressTrend: TrendDirection
}

enum TrendDirection: String, Codable {
    case improving, stable, declining
}

struct HealthInsights: Codable {
    let averageMood: Int
    let averageEnergy: Int
    let averageFocus: Int
    let averageStress: Int
    let recommendations: [String]
}

// MARK: - Health Repository

protocol HealthRepositoryProtocol {
    func getDailyMetrics() async throws -> [DailyHealthMetric]
    func saveMetric(_ metric: DailyHealthMetric) async throws
    func getWeeklyTrends() async throws -> HealthTrends
}

class HealthRepository: HealthRepositoryProtocol {
    
    private let userDefaults = UserDefaults.standard
    private let metricsKey = "health_metrics"
    
    func getDailyMetrics() async throws -> [DailyHealthMetric] {
        guard let data = userDefaults.data(forKey: metricsKey) else {
            return []
        }
        
        do {
            let metrics = try JSONDecoder().decode([DailyHealthMetric].self, from: data)
            return metrics.sorted { $0.date > $1.date }
        } catch {
            throw NeuroHealthError.invalidData
        }
    }
    
    func saveMetric(_ metric: DailyHealthMetric) async throws {
        var metrics = try await getDailyMetrics()
        
        // Remove existing metric for the same day
        metrics.removeAll { Calendar.current.isDate($0.date, inSameDayAs: metric.date) }
        metrics.append(metric)
        
        do {
            let data = try JSONEncoder().encode(metrics)
            userDefaults.set(data, forKey: metricsKey)
        } catch {
            throw NeuroHealthError.saveFailed
        }
    }
    
    func getWeeklyTrends() async throws -> HealthTrends {
        let metrics = try await getDailyMetrics()
        let recentMetrics = Array(metrics.prefix(14)) // Last 14 days
        
        guard recentMetrics.count >= 7 else {
            return HealthTrends(
                moodTrend: .stable,
                energyTrend: .stable,
                focusTrend: .stable,
                stressTrend: .stable
            )
        }
        
        let firstWeek = Array(recentMetrics.suffix(7))
        let secondWeek = Array(recentMetrics.prefix(7))
        
        return HealthTrends(
            moodTrend: calculateTrend(
                first: firstWeek.map { $0.mood.rawValue },
                second: secondWeek.map { $0.mood.rawValue }
            ),
            energyTrend: calculateTrend(
                first: firstWeek.map { $0.energy.rawValue },
                second: secondWeek.map { $0.energy.rawValue }
            ),
            focusTrend: calculateTrend(
                first: firstWeek.map { $0.focus.rawValue },
                second: secondWeek.map { $0.focus.rawValue }
            ),
            stressTrend: calculateTrend(
                first: firstWeek.map { $0.stress.rawValue },
                second: secondWeek.map { $0.stress.rawValue }
            )
        )
    }
    
    private func calculateTrend(first: [Int], second: [Int]) -> TrendDirection {
        let firstAvg = first.reduce(0, +) / first.count
        let secondAvg = second.reduce(0, +) / second.count
        
        let difference = secondAvg - firstAvg
        
        if difference > 0 {
            return .improving
        } else if difference < 0 {
            return .declining
        } else {
            return .stable
        }
    }
}

enum NeuroHealthError: Error, LocalizedError {
    case invalidData
    case saveFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidData:
            return "Invalid health data"
        case .saveFailed:
            return "Failed to save health metric"
        }
    }
}
