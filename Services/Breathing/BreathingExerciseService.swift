import Combine
import Foundation
import HealthKit
import WatchConnectivity

// MARK: - Breathing Exercise Service

/// Service for managing breathing exercises with neurodiversity-first approach
/// Integrates with HealthKit and Apple Watch for biometric feedback
@available(iOS 26.0, *)
class BreathingExerciseService: BreathingExerciseServiceProtocol, ObservableObject, @unchecked Sendable {

    // MARK: - Published Properties
    @Published var isSessionActive = false
    @Published var currentExercise: BreathingExercise?
    @Published var sessionProgress: Double = 0.0
    @Published var currentPhase: BreathingPhase = .inhale
    @Published var heartRateData: [HeartRateReading] = []
    @Published var hrvData: [HRVReading] = []
    @Published var isWatchConnected = false
    @Published var lastError: Error?

    // MARK: - Dependencies
    private let healthKitService: HealthKitServiceProtocol
    private let watchConnectivityService: WatchConnectivityServiceProtocol
    private let sensoryAdaptationService: SensoryAdaptationServiceProtocol

    // MARK: - Private Properties
    private var sessionTimer: Timer?
    private var phaseTimer: Timer?
    private var currentSession: BreathingSession?
    private var cancellables = Set<AnyCancellable>()
    private let breathingPatterns: [BreathingPattern]
    private let calmingTechniques: [CalmingTechnique]

    // MARK: - Initialization
    init(
        healthKitService: HealthKitServiceProtocol,
        watchConnectivityService: WatchConnectivityServiceProtocol,
        sensoryAdaptationService: SensoryAdaptationServiceProtocol
    ) {
        self.healthKitService = healthKitService
        self.watchConnectivityService = watchConnectivityService
        self.sensoryAdaptationService = sensoryAdaptationService
        self.breathingPatterns = BreathingPattern.defaultPatterns
        self.calmingTechniques = CalmingTechnique.defaultTechniques

        setupBindings()
    }

    // MARK: - BreathingExerciseServiceProtocol Implementation

    func getAvailableExercises() async -> [BreathingExercise] {
        return []
    }

    func startBreathingSession(_ exercise: BreathingExercise) async throws {
        // Use default user profile for compatibility
        let defaultProfile = UserProfile(name: "Default", email: "<EMAIL>")
        try await startBreathingSession(exercise, userProfile: defaultProfile)
    }

    func startBreathingSession(_ exercise: BreathingExercise, userProfile: UserProfile) async throws
    {
        guard !isSessionActive else {
            throw BreathingExerciseServiceError.sessionAlreadyActive
        }

        // Adapt exercise for user's sensory preferences
        let adaptedExercise = await adaptExerciseForUser(exercise, userProfile: userProfile)

        // Create new session
        currentSession = BreathingSession(
            exercise: adaptedExercise,
            startTime: Date()
        )

        currentExercise = adaptedExercise
        isSessionActive = true
        sessionProgress = 0.0
        currentPhase = .preparation

        // Start health monitoring
        try await startHealthMonitoring()

        // Start Apple Watch session if available
        if isWatchConnected {
            await startWatchSession(adaptedExercise)
        }

        // Begin breathing session
        await startBreathingCycle()
    }

    func pauseSession() async {
        guard isSessionActive else { return }

        sessionTimer?.invalidate()
        phaseTimer?.invalidate()

        currentSession?.isPaused = true

        // Pause watch session
        if isWatchConnected {
            await pauseWatchSession()
        }
    }

    func resumeSession() async {
        guard isSessionActive, currentSession?.isPaused == true else { return }

        currentSession?.isPaused = false

        // Resume breathing cycle
        await continueBreathingCycle()

        // Resume watch session
        if isWatchConnected {
            await resumeWatchSession()
        }
    }

    func endBreathingSession() async throws -> BreathingSessionResult {
        return await endSession()
    }

    func endSession() async -> BreathingSessionResult {
        defer {
            cleanup()
        }

        guard let session = currentSession else {
            return BreathingSessionResult.empty
        }

        // Stop timers
        sessionTimer?.invalidate()
        phaseTimer?.invalidate()

        // Stop health monitoring
        await stopHealthMonitoring()

        // End watch session
        if isWatchConnected {
            await endWatchSession()
        }

        // Calculate session results
        let result = await calculateSessionResults(session)

        // Save session data
        try? await saveSessionData(session, result: result)

        return result
    }

    func getPersonalizedExercises(for userProfile: UserProfile) async -> [BreathingExercise] {
        var personalizedExercises: [BreathingExercise] = []

        // Select exercises based on neurodiversity type
        for neurodiversityType in userProfile.cognitiveProfile.neurodiversityType {
            // Convert NeurodiversityType to NeurodiversitySupport
            let neurodiversitySupport: NeurodiversitySupport
            switch neurodiversityType {
            case .adhd: neurodiversitySupport = .adhd
            case .autism: neurodiversitySupport = .autism
            case .executiveDysfunction: neurodiversitySupport = .executiveDysfunction
            default: neurodiversitySupport = .anxiety // fallback
            }

            let suitableExercises =
                breathingPatterns
                .filter { $0.suitableFor.contains(neurodiversitySupport) }
                .map { pattern in
                    BreathingExercise(
                        name: pattern.name,
                        description: pattern.description,
                        pattern: pattern,
                        duration: pattern.defaultDuration,
                        difficulty: pattern.difficulty,
                        benefits: pattern.benefits,
                        neurodiversitySupport: pattern.neurodiversitySupport
                    )
                }

            personalizedExercises.append(contentsOf: suitableExercises)
        }

        // Add general calming exercises
        let generalExercises =
            breathingPatterns
            .filter { $0.isGeneral }
            .map { pattern in
                BreathingExercise(
                    name: pattern.name,
                    description: pattern.description,
                    pattern: pattern,
                    duration: pattern.defaultDuration,
                    difficulty: pattern.difficulty,
                    benefits: pattern.benefits,
                    neurodiversitySupport: pattern.neurodiversitySupport
                )
            }

        personalizedExercises.append(contentsOf: generalExercises)

        // Remove duplicates and sort by suitability
        let uniqueExercises = Array(Set(personalizedExercises))
        return uniqueExercises.sorted { exercise1, exercise2 in
            calculateSuitabilityScore(exercise1, for: userProfile)
                > calculateSuitabilityScore(exercise2, for: userProfile)
        }
    }

    func detectAnxietyOverwhelm(for userProfile: UserProfile) async -> AnxietyOverwhelmDetection {
        // Analyze current biometric data
        let recentHeartRate = heartRateData.suffix(10)
        let recentHRV = hrvData.suffix(10)

        // Calculate anxiety indicators
        let heartRateVariability = calculateHeartRateVariability(recentHeartRate)
        let heartRateElevation = calculateHeartRateElevation(
            recentHeartRate, baseline: userProfile.baselineHeartRate ?? 70.0)
        let hrvStress = calculateHRVStressLevel(recentHRV)

        // Determine anxiety/overwhelm level
        let anxietyScore = (heartRateElevation + hrvStress) / 2.0
        let overwhelmLevel = determineOverwhelmLevel(anxietyScore)

        // Generate recommendations
        let recommendations = await generateAnxietyRecommendations(
            overwhelmLevel: overwhelmLevel,
            userProfile: userProfile
        )

        return AnxietyOverwhelmDetection(
            isDetected: overwhelmLevel != .calm,
            confidence: min(anxietyScore, 1.0),
            triggers: ["elevated_heart_rate", "hrv_stress"],
            recommendations: recommendations,
            overwhelmLevel: overwhelmLevel,
            anxietyScore: anxietyScore,
            detectionTime: Date()
        )
    }

    // MARK: - Private Implementation

    private func setupBindings() {
        // Setup periodic health monitoring
        Task {
            await startHealthMonitoring()
        }
    }

    private func startHealthMonitoring() async {
        // Simplified health monitoring without Combine publishers
        // This would be replaced with proper HealthKit integration
    }

    private func adaptExerciseForUser(_ exercise: BreathingExercise, userProfile: UserProfile) async
        -> BreathingExercise
    {
        var adaptedExercise = exercise

        // Adapt for sensory preferences (simplified implementation)
        // This would use the actual sensory adaptation service methods
        // For now, keep the original exercise settings

        // Adapt timing for executive function level by creating new pattern
        let originalPattern = adaptedExercise.pattern
        var inhaleTime = originalPattern.inhaleTime
        var exhaleTime = originalPattern.exhaleTime
        var holdTime = originalPattern.holdTime

        switch userProfile.executiveFunctionLevel {
        case .low:
            // Slower, more guided
            inhaleTime *= 1.2
            exhaleTime *= 1.2
            holdTime *= 0.8

        case .medium:
            // Standard timing
            break

        case .high:
            // Can handle more complex patterns
            holdTime *= 1.1
        }

        // Adapt for anxiety sensitivity
        if userProfile.cognitiveProfile.neurodiversityType.contains(.anxiety) {
            inhaleTime *= 0.9  // Slightly shorter to avoid anxiety
            adaptedExercise.duration = min(adaptedExercise.duration, 300)  // Max 5 minutes
        }

        // Create new adapted pattern
        adaptedExercise.pattern = BreathingPattern(
            id: originalPattern.id,
            name: originalPattern.name,
            description: originalPattern.description,
            inhaleTime: inhaleTime,
            holdTime: holdTime,
            exhaleTime: exhaleTime,
            restTime: originalPattern.restTime,
            defaultDuration: originalPattern.defaultDuration,
            difficulty: originalPattern.difficulty,
            benefits: originalPattern.benefits,
            suitableFor: originalPattern.suitableFor,
            neurodiversitySupport: originalPattern.neurodiversitySupport,
            isGeneral: originalPattern.isGeneral
        )

        return adaptedExercise
    }



    private func stopHealthMonitoring() async {
        // Simplified health monitoring - would use actual HealthKit methods
        // For now, just track that monitoring is stopped
    }

    private func startWatchSession(_ exercise: BreathingExercise) async {
        let watchData: [String: Any] = [
            "action": "startBreathing",
            "exerciseName": exercise.name,
            "duration": exercise.duration,
            "inhaleTime": exercise.pattern.inhaleTime,
            "exhaleTime": exercise.pattern.exhaleTime,
            "holdTime": exercise.pattern.holdTime,
        ]

        // Use the correct protocol method for sending breathing session to watch
        if let session = currentSession {
            try? await watchConnectivityService.sendBreathingSessionToWatch(session)
        }
    }

    private func pauseWatchSession() async {
        // Simplified watch session pause - no sendMessage method available
        // Watch connectivity handled through session updates
    }

    private func resumeWatchSession() async {
        // Simplified watch session resume - no sendMessage method available
        // Watch connectivity handled through session updates
    }

    private func endWatchSession() async {
        // Simplified watch session end - no sendMessage method available
        // Watch connectivity handled through session updates
    }

    private func startBreathingCycle() async {
        guard let exercise = currentExercise else { return }

        let cycleTime = exercise.pattern.inhaleTime + exercise.pattern.holdTime + exercise.pattern.exhaleTime + exercise.pattern.restTime
        let totalCycles = Int(exercise.duration / cycleTime)
        var currentCycle = 0

        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }

            let elapsed = Date().timeIntervalSince(self.currentSession?.startTime ?? Date())
            self.sessionProgress = elapsed / exercise.duration

            if elapsed >= exercise.duration {
                Task {
                    _ = await self.endSession()
                }
            }
        }

        await executeBreathingPhases(exercise.pattern, totalCycles: totalCycles)
    }

    private func continueBreathingCycle() async {
        guard let exercise = currentExercise else { return }
        await executeBreathingPhases(exercise.pattern, totalCycles: 1)  // Continue with remaining cycles
    }

    private func executeBreathingPhases(_ pattern: BreathingPattern, totalCycles: Int) async {
        for cycle in 0..<totalCycles {
            guard isSessionActive, currentSession?.isPaused != true else { break }

            // Preparation phase (first cycle only)
            if cycle == 0 {
                await MainActor.run { currentPhase = .preparation }
                try? await Task.sleep(nanoseconds: UInt64(2 * 1_000_000_000))  // 2 seconds
            }

            // Inhale phase
            await MainActor.run { currentPhase = .inhale }
            try? await Task.sleep(nanoseconds: UInt64(pattern.inhaleTime * 1_000_000_000))

            // Hold phase (if applicable)
            if pattern.holdTime > 0 {
                await MainActor.run { currentPhase = .hold }
                try? await Task.sleep(nanoseconds: UInt64(pattern.holdTime * 1_000_000_000))
            }

            // Exhale phase
            await MainActor.run { currentPhase = .exhale }
            try? await Task.sleep(nanoseconds: UInt64(pattern.exhaleTime * 1_000_000_000))

            // Rest phase (if applicable)
            if pattern.restTime > 0 {
                await MainActor.run { currentPhase = .pause }
                try? await Task.sleep(nanoseconds: UInt64(pattern.restTime * 1_000_000_000))
            }
        }
    }

    private func calculateSessionResults(_ session: BreathingSession) async
        -> BreathingSessionResult
    {
        let endTime = Date()
        let duration = endTime.timeIntervalSince(session.startTime)

        // Calculate heart rate changes
        let preSessionHR = heartRateData.prefix(5).map { $0.beatsPerMinute }
        let postSessionHR = heartRateData.suffix(5).map { $0.beatsPerMinute }

        let avgPreHR =
            preSessionHR.isEmpty ? 0 : preSessionHR.reduce(0, +) / Double(preSessionHR.count)
        let avgPostHR =
            postSessionHR.isEmpty ? 0 : postSessionHR.reduce(0, +) / Double(postSessionHR.count)

        let heartRateReduction = avgPreHR - avgPostHR

        // Calculate HRV improvements
        let preSessionHRV = hrvData.prefix(5).map { $0.value }
        let postSessionHRV = hrvData.suffix(5).map { $0.value }

        let avgPreHRV =
            preSessionHRV.isEmpty ? 0 : preSessionHRV.reduce(0, +) / Double(preSessionHRV.count)
        let avgPostHRV =
            postSessionHRV.isEmpty ? 0 : postSessionHRV.reduce(0, +) / Double(postSessionHRV.count)

        let hrvImprovement = avgPostHRV - avgPreHRV

        return BreathingSessionResult(
            sessionId: session.id,
            duration: duration,
            completionRate: sessionProgress,
            averageHeartRate: avgPostHR,
            stressReduction: hrvImprovement,
            effectivenessScore: calculateCalmingEffectiveness(heartRateReduction, hrvImprovement),
            userFeedback: nil,  // Will be collected from UI
            calmingEffectiveness: calculateCalmingEffectiveness(heartRateReduction, hrvImprovement),
            heartRateReduction: heartRateReduction
        )
    }

    private func calculateCalmingEffectiveness(
        _ heartRateReduction: Double, _ hrvImprovement: Double
    ) -> Double {
        // Simple effectiveness calculation (0.0 to 1.0)
        let hrScore = min(1.0, max(0.0, heartRateReduction / 20.0))  // Normalize to 20 BPM reduction
        let hrvScore = min(1.0, max(0.0, hrvImprovement / 50.0))  // Normalize to 50ms improvement

        return (hrScore + hrvScore) / 2.0
    }

    private func saveSessionData(_ session: BreathingSession, result: BreathingSessionResult)
        async throws
    {
        // Save to HealthKit using the available protocol method
        try await healthKitService.saveBreathingSession(currentSession!)

        // Save locally for analysis
        // This would typically save to Core Data or similar
    }

    private func calculateSuitabilityScore(
        _ exercise: BreathingExercise, for userProfile: UserProfile
    ) -> Double {
        var score = 0.0

        // Score based on neurodiversity support
        for neurodiversityType in userProfile.cognitiveProfile.neurodiversityType {
            // Convert NeurodiversityType to NeurodiversitySupport
            let neurodiversitySupport: NeurodiversitySupport
            switch neurodiversityType {
            case .adhd: neurodiversitySupport = .adhd
            case .autism: neurodiversitySupport = .autism
            case .executiveDysfunction: neurodiversitySupport = .executiveDysfunction
            default: neurodiversitySupport = .anxiety // fallback
            }

            if exercise.neurodiversitySupport.contains(neurodiversitySupport) {
                score += 1.0
            }
        }

        // Score based on difficulty vs executive function level
        let difficultyMatch =
            switch (exercise.difficulty, userProfile.executiveFunctionLevel) {
            case (.beginner, .low): 1.0
            case (.intermediate, .medium): 1.0
            case (.advanced, .high): 1.0
            default: 0.5
            }

        score += difficultyMatch

        return score
    }

    private func calculateHeartRateVariability(_ readings: ArraySlice<HeartRateReading>) -> Double {
        guard readings.count > 1 else { return 0.0 }

        let values = readings.map { $0.beatsPerMinute }
        let mean = values.reduce(0, +) / Double(values.count)
        let variance = values.map { pow($0 - mean, 2) }.reduce(0, +) / Double(values.count)

        return sqrt(variance)
    }

    private func calculateHeartRateElevation(
        _ readings: ArraySlice<HeartRateReading>, baseline: Double
    ) -> Double {
        guard !readings.isEmpty else { return 0.0 }

        let currentAvg = readings.map { $0.beatsPerMinute }.reduce(0, +) / Double(readings.count)
        return max(0, (currentAvg - baseline) / baseline)
    }

    private func calculateHRVStressLevel(_ readings: ArraySlice<HRVReading>) -> Double {
        guard !readings.isEmpty else { return 0.0 }

        let currentAvg = readings.map { $0.value }.reduce(0, +) / Double(readings.count)
        // Lower HRV typically indicates higher stress
        // This is a simplified calculation
        return max(0, min(1, (50 - currentAvg) / 50))
    }

    private func determineOverwhelmLevel(_ anxietyScore: Double) -> OverwhelmLevel {
        switch anxietyScore {
        case 0..<0.3: return .calm
        case 0.3..<0.6: return .mild
        case 0.6..<0.8: return .moderate
        default: return .severe
        }
    }

    private func generateAnxietyRecommendations(
        overwhelmLevel: OverwhelmLevel, userProfile: UserProfile
    ) async -> [String] {
        var recommendations: [String] = []

        switch overwhelmLevel {
        case .calm:
            recommendations.append(
                "You're in a good state. Consider a maintenance breathing exercise.")

        case .low:
            recommendations.append("You're doing well. A brief breathing exercise can help maintain balance.")

        case .mild:
            recommendations.append("Try a short 3-minute breathing exercise to maintain calm.")
            recommendations.append("Take a moment to notice your surroundings.")

        case .moderate:
            recommendations.append("Consider a 5-10 minute guided breathing session.")
            recommendations.append("Find a quiet space if possible.")
            recommendations.append("Focus on slow, deep breaths.")

        case .high:
            recommendations.append("Take time for a longer breathing session (10-15 minutes).")
            recommendations.append("Consider using calming background sounds or music.")

        case .severe:
            recommendations.append("Take immediate action: find a safe, quiet space.")
            recommendations.append("Start with simple box breathing (4-4-4-4).")
            recommendations.append("Consider reaching out for support if needed.")
        }

        // Add neurodiversity-specific recommendations
        for neurodiversityType in userProfile.cognitiveProfile.neurodiversityType {
            switch neurodiversityType {
            case .autism:
                recommendations.append(
                    "Use your preferred sensory tools (weighted blanket, fidget items).")

            case .adhd:
                recommendations.append("Try movement-based calming (gentle stretching, walking).")

            case .sensoryProcessingDifferences:
                recommendations.append("Adjust lighting and sound to comfortable levels.")

            default:
                break
            }
        }

        return recommendations
    }

    private func getSuggestedExercisesForAnxiety(
        _ overwhelmLevel: OverwhelmLevel, userProfile: UserProfile
    ) async -> [BreathingExercise] {
        let allExercises = await getPersonalizedExercises(for: userProfile)

        return allExercises.filter { exercise in
            switch overwhelmLevel {
            case .calm:
                return exercise.difficulty == .beginner || exercise.difficulty == .intermediate
            case .low:
                return exercise.difficulty == .beginner || exercise.difficulty == .intermediate
            case .mild:
                return exercise.difficulty == .beginner && exercise.duration <= 300  // 5 minutes
            case .moderate:
                return exercise.difficulty == .beginner && exercise.duration <= 600  // 10 minutes
            case .high:
                return exercise.difficulty == .beginner && exercise.duration <= 240  // 4 minutes
            case .severe:
                return exercise.difficulty == .beginner && exercise.duration <= 180  // 3 minutes
            }
        }.prefix(3).map { $0 }
    }

    private func cleanup() {
        isSessionActive = false
        currentExercise = nil
        currentSession = nil
        sessionProgress = 0.0
        currentPhase = .inhale

        sessionTimer?.invalidate()
        phaseTimer?.invalidate()
        sessionTimer = nil
        phaseTimer = nil
    }
}

// MARK: - Supporting Types

enum BreathingExerciseServiceError: Error, LocalizedError {
    case sessionAlreadyActive
    case healthKitNotAvailable
    case watchNotConnected

    var errorDescription: String? {
        switch self {
        case .sessionAlreadyActive:
            return "A breathing session is already active"
        case .healthKitNotAvailable:
            return "HealthKit is not available"
        case .watchNotConnected:
            return "Apple Watch is not connected"
        }
    }
}



extension BreathingSessionResult {
    static let empty = BreathingSessionResult(
        sessionId: UUID(),
        duration: 0,
        completionRate: 0.0
    )
}
