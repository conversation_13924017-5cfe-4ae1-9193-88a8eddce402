import Foundation
import Combine

// MARK: - Breathing Service

/// Service for managing breathing exercises and sessions
@available(iOS 18.0, *)
class BreathingService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var sessions: [BreathingSession] = []
    @Published var currentSession: BreathingSession?
    @Published var isSessionActive = false
    @Published var isLoading = false
    @Published var lastError: Error?
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let breathingRepository: BreathingRepositoryProtocol
    private var sessionTimer: Timer?
    
    // MARK: - Initialization
    init(breathingRepository: BreathingRepositoryProtocol = BreathingRepository()) {
        self.breathingRepository = breathingRepository
        loadSessions()
    }
    
    // MARK: - Public Methods
    
    /// Load all breathing sessions
    func loadSessions() {
        isLoading = true
        
        Task {
            do {
                let allSessions = try await breathingRepository.getAllSessions()
                await MainActor.run {
                    self.sessions = allSessions
                    self.isLoading = false
                    self.lastError = nil
                }
            } catch {
                await MainActor.run {
                    self.lastError = error
                    self.isLoading = false
                }
            }
        }
    }
    
    /// Start a new breathing session
    func startSession(type: BreathingExerciseType, duration: TimeInterval) async throws {
        let session = BreathingSession(
            type: type,
            duration: duration,
            startTime: Date()
        )
        
        await MainActor.run {
            self.currentSession = session
            self.isSessionActive = true
        }
        
        // Start session timer
        startSessionTimer(duration: duration)
    }
    
    /// End the current breathing session
    func endSession() async throws {
        guard var session = currentSession else {
            throw BreathingServiceError.noActiveSession
        }
        
        session.endTime = Date()
        session.isCompleted = true
        
        do {
            try await breathingRepository.saveSession(session)
            await MainActor.run {
                self.sessions.append(session)
                self.currentSession = nil
                self.isSessionActive = false
            }
            
            stopSessionTimer()
        } catch {
            await MainActor.run {
                self.lastError = error
            }
            throw error
        }
    }
    
    /// Pause the current session
    func pauseSession() {
        guard currentSession != nil else { return }
        
        stopSessionTimer()
        isSessionActive = false
    }
    
    /// Resume the current session
    func resumeSession() {
        guard let session = currentSession else { return }
        
        let remainingTime = session.duration - Date().timeIntervalSince(session.startTime)
        if remainingTime > 0 {
            startSessionTimer(duration: remainingTime)
            isSessionActive = true
        }
    }
    
    /// Get session statistics
    func getSessionStats() async throws -> BreathingStats {
        let allSessions = try await breathingRepository.getAllSessions()
        
        let totalSessions = allSessions.count
        let totalDuration = allSessions.reduce(0) { $0 + $1.duration }
        let averageDuration = totalSessions > 0 ? totalDuration / Double(totalSessions) : 0
        
        let thisWeekSessions = allSessions.filter { session in
            Calendar.current.isDate(session.startTime, equalTo: Date(), toGranularity: .weekOfYear)
        }
        
        return BreathingStats(
            totalSessions: totalSessions,
            totalDuration: totalDuration,
            averageDuration: averageDuration,
            sessionsThisWeek: thisWeekSessions.count
        )
    }
    
    // MARK: - Private Methods
    
    private func startSessionTimer(duration: TimeInterval) {
        stopSessionTimer()
        
        sessionTimer = Timer.scheduledTimer(withTimeInterval: duration, repeats: false) { [weak self] _ in
            Task {
                try? await self?.endSession()
            }
        }
    }
    
    private func stopSessionTimer() {
        sessionTimer?.invalidate()
        sessionTimer = nil
    }
}

// MARK: - Breathing Service Errors

enum BreathingServiceError: Error, LocalizedError {
    case noActiveSession
    case sessionAlreadyActive
    case invalidSessionData
    case saveFailed
    
    var errorDescription: String? {
        switch self {
        case .noActiveSession:
            return "No active breathing session"
        case .sessionAlreadyActive:
            return "A breathing session is already active"
        case .invalidSessionData:
            return "Invalid session data provided"
        case .saveFailed:
            return "Failed to save breathing session"
        }
    }
}

// MARK: - Breathing Repository Implementation

class BreathingRepository: BreathingRepositoryProtocol {
    
    private let userDefaults = UserDefaults.standard
    private let sessionsKey = "breathing_sessions"
    
    func getAllSessions() async throws -> [BreathingSession] {
        guard let data = userDefaults.data(forKey: sessionsKey) else {
            return []
        }
        
        do {
            let sessions = try JSONDecoder().decode([BreathingSession].self, from: data)
            return sessions
        } catch {
            throw BreathingServiceError.invalidSessionData
        }
    }
    
    func saveSession(_ session: BreathingSession) async throws {
        var sessions = try await getAllSessions()
        sessions.append(session)
        try await saveSessions(sessions)
    }
    
    func updateSession(_ session: BreathingSession) async throws {
        var sessions = try await getAllSessions()
        if let index = sessions.firstIndex(where: { $0.id == session.id }) {
            sessions[index] = session
            try await saveSessions(sessions)
        }
    }
    
    private func saveSessions(_ sessions: [BreathingSession]) async throws {
        do {
            let data = try JSONEncoder().encode(sessions)
            userDefaults.set(data, forKey: sessionsKey)
        } catch {
            throw BreathingServiceError.saveFailed
        }
    }
}

// MARK: - Supporting Types

enum BreathingExerciseType: String, CaseIterable, Codable {
    case box = "Box Breathing"
    case fourSevenEight = "4-7-8 Breathing"
    case deepBreathing = "Deep Breathing"
    case coherentBreathing = "Coherent Breathing"
    case triangleBreathing = "Triangle Breathing"
}

struct BreathingStats: Codable {
    let totalSessions: Int
    let totalDuration: TimeInterval
    let averageDuration: TimeInterval
    let sessionsThisWeek: Int
}

// MARK: - Breathing Repository Protocol

protocol BreathingRepositoryProtocol {
    func getAllSessions() async throws -> [BreathingSession]
    func saveSession(_ session: BreathingSession) async throws
    func updateSession(_ session: BreathingSession) async throws
}
