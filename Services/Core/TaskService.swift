import Foundation
import Combine

// MARK: - Task Service

/// Service for managing AI tasks and task-related operations
@available(iOS 18.0, *)
class TaskService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var tasks: [AITask] = []
    @Published var activeTasks: [AITask] = []
    @Published var completedTasks: [AITask] = []
    @Published var isLoading = false
    @Published var lastError: Error?
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let taskRepository: TaskRepositoryProtocol
    private let aiTaskCoach: AITaskCoachServiceProtocol
    
    // MARK: - Initialization
    init(
        taskRepository: TaskRepositoryProtocol = TaskRepository(),
        aiTaskCoach: AITaskCoachServiceProtocol = AITaskCoachService()
    ) {
        self.taskRepository = taskRepository
        self.aiTaskCoach = aiTaskCoach
        loadTasks()
    }
    
    // MARK: - Public Methods
    
    /// Load all tasks
    func loadTasks() {
        isLoading = true
        
        Task {
            do {
                let allTasks = try await taskRepository.getAllTasks()
                await MainActor.run {
                    self.tasks = allTasks
                    self.updateTaskCategories()
                    self.isLoading = false
                    self.lastError = nil
                }
            } catch {
                await MainActor.run {
                    self.lastError = error
                    self.isLoading = false
                }
            }
        }
    }
    
    /// Create a new task
    func createTask(_ task: AITask) async throws {
        await MainActor.run {
            self.isLoading = true
        }
        
        do {
            try await taskRepository.saveTask(task)
            await MainActor.run {
                self.tasks.append(task)
                self.updateTaskCategories()
                self.isLoading = false
                self.lastError = nil
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.isLoading = false
            }
            throw error
        }
    }
    
    /// Update an existing task
    func updateTask(_ task: AITask) async throws {
        await MainActor.run {
            self.isLoading = true
        }
        
        do {
            try await taskRepository.updateTask(task)
            await MainActor.run {
                if let index = self.tasks.firstIndex(where: { $0.id == task.id }) {
                    self.tasks[index] = task
                }
                self.updateTaskCategories()
                self.isLoading = false
                self.lastError = nil
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.isLoading = false
            }
            throw error
        }
    }
    
    /// Complete a task
    func completeTask(_ taskId: UUID) async throws {
        guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else {
            throw TaskServiceError.taskNotFound
        }
        
        var task = tasks[taskIndex]
        task.status = .completed
        task.completedAt = Date()
        
        try await updateTask(task)
    }
    
    /// Delete a task
    func deleteTask(_ taskId: UUID) async throws {
        await MainActor.run {
            self.isLoading = true
        }
        
        do {
            try await taskRepository.deleteTask(taskId)
            await MainActor.run {
                self.tasks.removeAll { $0.id == taskId }
                self.updateTaskCategories()
                self.isLoading = false
                self.lastError = nil
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.isLoading = false
            }
            throw error
        }
    }
    
    /// Generate AI-powered tasks
    func generateAITasks(for userContext: UserContext, count: Int = 5) async throws -> [AITask] {
        do {
            let personalizedContent = try await aiTaskCoach.generatePersonalizedTasks(
                for: userContext,
                count: count
            )
            return personalizedContent.tasks
        } catch {
            await MainActor.run {
                self.lastError = error
            }
            throw error
        }
    }
    
    // MARK: - Private Methods
    
    private func updateTaskCategories() {
        activeTasks = tasks.filter { $0.status == .active || $0.status == .inProgress }
        completedTasks = tasks.filter { $0.status == .completed }
    }
}

// MARK: - Task Service Errors

enum TaskServiceError: Error, LocalizedError {
    case taskNotFound
    case invalidTaskData
    case saveFailed
    case deleteFailed
    
    var errorDescription: String? {
        switch self {
        case .taskNotFound:
            return "Task not found"
        case .invalidTaskData:
            return "Invalid task data provided"
        case .saveFailed:
            return "Failed to save task"
        case .deleteFailed:
            return "Failed to delete task"
        }
    }
}

// MARK: - Task Repository Implementation

class TaskRepository: TaskRepositoryProtocol {
    
    private let userDefaults = UserDefaults.standard
    private let tasksKey = "ai_tasks"
    
    func getAllTasks() async throws -> [AITask] {
        guard let data = userDefaults.data(forKey: tasksKey) else {
            return []
        }
        
        do {
            let tasks = try JSONDecoder().decode([AITask].self, from: data)
            return tasks
        } catch {
            throw TaskServiceError.invalidTaskData
        }
    }
    
    func saveTask(_ task: AITask) async throws {
        var tasks = try await getAllTasks()
        tasks.append(task)
        try await saveTasks(tasks)
    }
    
    func updateTask(_ task: AITask) async throws {
        var tasks = try await getAllTasks()
        if let index = tasks.firstIndex(where: { $0.id == task.id }) {
            tasks[index] = task
            try await saveTasks(tasks)
        } else {
            throw TaskServiceError.taskNotFound
        }
    }
    
    func deleteTask(_ taskId: UUID) async throws {
        var tasks = try await getAllTasks()
        tasks.removeAll { $0.id == taskId }
        try await saveTasks(tasks)
    }
    
    private func saveTasks(_ tasks: [AITask]) async throws {
        do {
            let data = try JSONEncoder().encode(tasks)
            userDefaults.set(data, forKey: tasksKey)
        } catch {
            throw TaskServiceError.saveFailed
        }
    }
}
