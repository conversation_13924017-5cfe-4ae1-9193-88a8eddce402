import Foundation
import Combine

// MARK: - Routine Service

/// Service for managing daily routines and routine-related operations
@available(iOS 18.0, *)
class RoutineService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var routines: [Routine] = []
    @Published var activeRoutines: [Routine] = []
    @Published var isLoading = false
    @Published var lastError: Error?
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let routineRepository: RoutineRepositoryProtocol
    
    // MARK: - Initialization
    init(routineRepository: RoutineRepositoryProtocol = RoutineRepository()) {
        self.routineRepository = routineRepository
        loadRoutines()
    }
    
    // MARK: - Public Methods
    
    /// Load all routines
    func loadRoutines() {
        isLoading = true
        
        Task {
            do {
                let allRoutines = try await routineRepository.getAllRoutines()
                await MainActor.run {
                    self.routines = allRoutines
                    self.activeRoutines = allRoutines.filter { $0.isActive }
                    self.isLoading = false
                    self.lastError = nil
                }
            } catch {
                await MainActor.run {
                    self.lastError = error
                    self.isLoading = false
                }
            }
        }
    }
    
    /// Create a new routine
    func createRoutine(_ routine: Routine) async throws {
        await MainActor.run {
            self.isLoading = true
        }
        
        do {
            try await routineRepository.saveRoutine(routine)
            await MainActor.run {
                self.routines.append(routine)
                if routine.isActive {
                    self.activeRoutines.append(routine)
                }
                self.isLoading = false
                self.lastError = nil
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.isLoading = false
            }
            throw error
        }
    }
    
    /// Update an existing routine
    func updateRoutine(_ routine: Routine) async throws {
        await MainActor.run {
            self.isLoading = true
        }
        
        do {
            try await routineRepository.updateRoutine(routine)
            await MainActor.run {
                if let index = self.routines.firstIndex(where: { $0.id == routine.id }) {
                    self.routines[index] = routine
                }
                self.activeRoutines = self.routines.filter { $0.isActive }
                self.isLoading = false
                self.lastError = nil
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.isLoading = false
            }
            throw error
        }
    }
    
    /// Delete a routine
    func deleteRoutine(_ routineId: UUID) async throws {
        await MainActor.run {
            self.isLoading = true
        }
        
        do {
            try await routineRepository.deleteRoutine(routineId)
            await MainActor.run {
                self.routines.removeAll { $0.id == routineId }
                self.activeRoutines.removeAll { $0.id == routineId }
                self.isLoading = false
                self.lastError = nil
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.isLoading = false
            }
            throw error
        }
    }
    
    /// Toggle routine active status
    func toggleRoutineStatus(_ routineId: UUID) async throws {
        guard let routineIndex = routines.firstIndex(where: { $0.id == routineId }) else {
            throw RoutineServiceError.routineNotFound
        }
        
        var routine = routines[routineIndex]
        routine.isActive.toggle()
        
        try await updateRoutine(routine)
    }
}

// MARK: - Routine Service Errors

enum RoutineServiceError: Error, LocalizedError {
    case routineNotFound
    case invalidRoutineData
    case saveFailed
    case deleteFailed
    
    var errorDescription: String? {
        switch self {
        case .routineNotFound:
            return "Routine not found"
        case .invalidRoutineData:
            return "Invalid routine data provided"
        case .saveFailed:
            return "Failed to save routine"
        case .deleteFailed:
            return "Failed to delete routine"
        }
    }
}

// MARK: - Note
// RoutineRepository and Routine types are defined in Core/Repositories/RoutineRepository.swift
