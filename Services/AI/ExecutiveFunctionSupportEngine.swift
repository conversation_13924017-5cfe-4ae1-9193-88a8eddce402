import Foundation
import Combine

// MARK: - Executive Function Support Engine

/// Provides executive function support for task breakdown, sequencing, and cognitive scaffolding
/// Specifically designed for ADHD, autism, and executive dysfunction support
@available(iOS 18.0, *)
@MainActor
class ExecutiveFunctionSupportEngine: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isProcessing = false
    @Published var processingProgress: Double = 0.0
    
    // MARK: - Private Properties
    private let taskBreakdownStrategies: [TaskBreakdownStrategy]
    private let sequencingRules: [SequencingRule]
    private let cognitiveScaffolds: [CognitiveScaffold]
    
    // MARK: - Initialization
    init() {
        self.taskBreakdownStrategies = TaskBreakdownStrategy.defaultStrategies
        self.sequencingRules = SequencingRule.defaultRules
        self.cognitiveScaffolds = CognitiveScaffold.defaultScaffolds
    }
    
    // MARK: - Task Breakdown Methods
    
    func breakdownTask(_ task: AITask) async -> [AITask] {
        await MainActor.run {
            isProcessing = true
            processingProgress = 0.0
        }
        
        defer {
            Task { @MainActor in
                isProcessing = false
                processingProgress = 1.0
            }
        }
        
        // Select appropriate breakdown strategy based on task characteristics
        let strategy = selectBreakdownStrategy(for: task)
        await updateProgress(0.2)
        
        // Apply the breakdown strategy
        let subtasks = await applyBreakdownStrategy(strategy, to: task)
        await updateProgress(0.6)
        
        // Apply sequencing rules
        let sequencedTasks = await applySequencing(to: subtasks, originalTask: task)
        await updateProgress(0.8)
        
        // Add cognitive scaffolds
        let scaffoldedTasks = await addCognitiveScaffolds(to: sequencedTasks, originalTask: task)
        await updateProgress(1.0)
        
        return scaffoldedTasks
    }
    
    func generateDetailedSteps(for task: AITask) async -> [TaskStep] {
        let subtasks = await breakdownTask(task)
        
        return subtasks.enumerated().map { index, subtask in
            TaskStep(
                title: subtask.title,
                description: subtask.description,
                estimatedDuration: subtask.estimatedDuration,
                order: index + 1
            )
        }
    }
    
    func generateBasicSteps(for task: AITask) async -> [TaskStep] {
        // For higher executive function levels, provide fewer, broader steps
        let basicSteps = await generateBasicTaskSteps(for: task)
        
        return basicSteps.enumerated().map { index, step in
            TaskStep(
                title: step.title,
                description: step.description,
                estimatedDuration: step.estimatedDuration,
                order: index + 1
            )
        }
    }
    
    // MARK: - Private Implementation Methods
    
    private func selectBreakdownStrategy(for task: AITask) -> TaskBreakdownStrategy {
        // Select strategy based on task characteristics
        let taskComplexity = assessTaskComplexity(task)
        let cognitiveLoad = task.cognitiveLoad
        
        return taskBreakdownStrategies.first { strategy in
            strategy.suitableFor.contains(taskComplexity) &&
            strategy.cognitiveLoadRange.contains(cognitiveLoad)
        } ?? taskBreakdownStrategies.first!
    }
    
    private func applyBreakdownStrategy(_ strategy: TaskBreakdownStrategy, to task: AITask) async -> [AITask] {
        switch strategy.type {
        case .timeBoxed:
            return await createTimeBoxedSubtasks(task, duration: strategy.parameters["duration"] as? TimeInterval ?? 1800)
            
        case .sequential:
            return await createSequentialSubtasks(task, maxSteps: strategy.parameters["maxSteps"] as? Int ?? 5)
            
        case .parallel:
            return await createParallelSubtasks(task, maxBranches: strategy.parameters["maxBranches"] as? Int ?? 3)
            
        case .hierarchical:
            return await createHierarchicalSubtasks(task, maxDepth: strategy.parameters["maxDepth"] as? Int ?? 2)
            
        case .contextual:
            return await createContextualSubtasks(task)
        }
    }
    
    private func createTimeBoxedSubtasks(_ task: AITask, duration: TimeInterval) async -> [AITask] {
        let numberOfBoxes = max(1, Int(ceil(task.estimatedDuration / duration)))
        var subtasks: [AITask] = []
        
        for i in 0..<numberOfBoxes {
            let subtask = AITask(
                title: "\(task.title) - Session \(i + 1)",
                description: "Work on \(task.title) for \(Int(duration / 60)) minutes",
                priority: task.priority,
                estimatedDuration: min(duration, task.estimatedDuration - TimeInterval(i) * duration),
                cognitiveLoad: task.cognitiveLoad
            )
            
            subtasks.append(subtask)
        }
        
        return subtasks
    }
    
    private func createSequentialSubtasks(_ task: AITask, maxSteps: Int) async -> [AITask] {
        let steps = await generateSequentialSteps(for: task, maxSteps: maxSteps)
        
        return steps.enumerated().map { index, step in
            AITask(
                title: "Step \(index + 1): \(step.action)",
                description: step.description,
                priority: task.priority,
                estimatedDuration: task.estimatedDuration / Double(steps.count),
                cognitiveLoad: step.cognitiveLoad
            )
        }
    }
    
    private func createParallelSubtasks(_ task: AITask, maxBranches: Int) async -> [AITask] {
        let branches = await identifyParallelComponents(of: task, maxBranches: maxBranches)
        
        return branches.map { branch in
            AITask(
                title: branch.title,
                description: branch.description,
                priority: task.priority,
                estimatedDuration: branch.estimatedDuration,
                cognitiveLoad: branch.cognitiveLoad
            )
        }
    }
    
    private func createHierarchicalSubtasks(_ task: AITask, maxDepth: Int) async -> [AITask] {
        return await createHierarchy(for: task, currentDepth: 0, maxDepth: maxDepth)
    }
    
    private func createContextualSubtasks(_ task: AITask) async -> [AITask] {
        // Break down based on context switches (location, tools, people, etc.)
        let contexts = await identifyRequiredContexts(for: task)
        
        return contexts.map { context in
            AITask(
                title: "\(task.title) - \(context.name)",
                description: "Complete \(task.title) in \(context.name) context",
                priority: task.priority,
                estimatedDuration: context.estimatedDuration,
                cognitiveLoad: context.cognitiveLoad
            )
        }
    }
    
    private func applySequencing(to tasks: [AITask], originalTask: AITask) async -> [AITask] {
        // Apply sequencing rules to optimize task order
        var sequencedTasks = tasks
        
        for rule in sequencingRules {
            if rule.appliesTo(originalTask) {
                sequencedTasks = rule.apply(to: sequencedTasks)
            }
        }
        
        return sequencedTasks
    }
    
    private func addCognitiveScaffolds(to tasks: [AITask], originalTask: AITask) async -> [AITask] {
        return tasks.map { task in
            var scaffoldedTask = task
            
            // Add appropriate scaffolds based on task and user needs
            for scaffold in cognitiveScaffolds {
                if scaffold.appliesTo(task, originalTask: originalTask) {
                    scaffoldedTask = scaffold.apply(to: scaffoldedTask)
                }
            }
            
            return scaffoldedTask
        }
    }
    
    // MARK: - Helper Methods
    
    private func assessTaskComplexity(_ task: AITask) -> TaskComplexity {
        let durationScore = task.estimatedDuration > 3600 ? 2 : (task.estimatedDuration > 1800 ? 1 : 0)
        let cognitiveScore = switch task.cognitiveLoad {
        case .low: 0
        case .medium: 1
        case .high: 2
        case .overload: 3
        }
        let priorityScore = switch task.priority {
        case .low: 0
        case .medium: 1
        case .high: 2
        case .urgent: 3
        }
        
        let totalScore = durationScore + cognitiveScore + priorityScore
        
        switch totalScore {
        case 0...2: return .simple
        case 3...5: return .moderate
        case 6...8: return .complex
        default: return .veryComplex
        }
    }
    
    private func generateSequentialSteps(for task: AITask, maxSteps: Int) async -> [SequentialStep] {
        // Generate logical sequential steps based on task type and content
        var steps: [SequentialStep] = []
        
        // Default sequential breakdown pattern
        steps.append(SequentialStep(
            action: "Prepare and gather resources",
            description: "Collect all necessary materials, tools, and information for \(task.title)",
            cognitiveLoad: .low
        ))
        
        if maxSteps > 2 {
            steps.append(SequentialStep(
                action: "Plan and organize approach",
                description: "Create a clear plan for completing \(task.title)",
                cognitiveLoad: .medium
            ))
        }
        
        steps.append(SequentialStep(
            action: "Execute main work",
            description: "Complete the primary work for \(task.title)",
            cognitiveLoad: task.cognitiveLoad
        ))
        
        if maxSteps > 3 {
            steps.append(SequentialStep(
                action: "Review and refine",
                description: "Check work quality and make necessary improvements",
                cognitiveLoad: .medium
            ))
        }
        
        if maxSteps > 4 {
            steps.append(SequentialStep(
                action: "Finalize and document",
                description: "Complete final steps and document outcomes",
                cognitiveLoad: .low
            ))
        }
        
        return Array(steps.prefix(maxSteps))
    }
    
    private func identifyParallelComponents(of task: AITask, maxBranches: Int) async -> [ParallelBranch] {
        // Identify components that can be worked on independently
        var branches: [ParallelBranch] = []
        
        // Default parallel breakdown
        let branchDuration = task.estimatedDuration / Double(maxBranches)
        
        for i in 0..<maxBranches {
            branches.append(ParallelBranch(
                title: "Component \(i + 1)",
                description: "Independent component of \(task.title)",
                estimatedDuration: branchDuration,
                cognitiveLoad: task.cognitiveLoad
            ))
        }
        
        return branches
    }
    
    private func createHierarchy(for task: AITask, currentDepth: Int, maxDepth: Int) async -> [AITask] {
        if currentDepth >= maxDepth {
            return [task]
        }
        
        // Create 2-3 subtasks at each level
        let subtaskCount = min(3, max(2, Int(task.estimatedDuration / 1800))) // 30-minute chunks
        var subtasks: [AITask] = []
        
        for i in 0..<subtaskCount {
            let subtask = AITask(
                title: "\(task.title) - Part \(i + 1)",
                description: "Hierarchical component \(i + 1) of \(task.title)",
                priority: task.priority,
                estimatedDuration: task.estimatedDuration / Double(subtaskCount),
                cognitiveLoad: task.cognitiveLoad
            )
            
            subtasks.append(subtask)
        }
        
        return subtasks
    }
    
    private func identifyRequiredContexts(for task: AITask) async -> [TaskContext] {
        // Identify different contexts needed for task completion
        var contexts: [TaskContext] = []
        
        // Default contexts based on task characteristics
        contexts.append(TaskContext(
            name: "Focused Work",
            estimatedDuration: task.estimatedDuration * 0.7,
            cognitiveLoad: task.cognitiveLoad
        ))
        
        contexts.append(TaskContext(
            name: "Review & Communication",
            estimatedDuration: task.estimatedDuration * 0.3,
            cognitiveLoad: .low
        ))
        
        return contexts
    }
    
    private func generateBasicTaskSteps(for task: AITask) async -> [BasicStep] {
        return [
            BasicStep(
                title: "Start \(task.title)",
                description: "Begin working on the task",
                estimatedDuration: task.estimatedDuration * 0.8
            ),
            BasicStep(
                title: "Complete \(task.title)",
                description: "Finish and review the task",
                estimatedDuration: task.estimatedDuration * 0.2
            )
        ]
    }
    
    private func updateProgress(_ progress: Double) async {
        await MainActor.run {
            processingProgress = progress
        }
    }
}

// MARK: - Supporting Types

struct TaskBreakdownStrategy {
    let type: BreakdownType
    let suitableFor: [TaskComplexity]
    let cognitiveLoadRange: [CognitiveLoadLevel]
    let parameters: [String: Any]
    
    nonisolated(unsafe) static let defaultStrategies: [TaskBreakdownStrategy] = [
        TaskBreakdownStrategy(
            type: .timeBoxed,
            suitableFor: [.simple, .moderate],
            cognitiveLoadRange: [.low, .medium],
            parameters: ["duration": 1800.0] // 30 minutes
        ),
        TaskBreakdownStrategy(
            type: .sequential,
            suitableFor: [.moderate, .complex],
            cognitiveLoadRange: [.medium, .high],
            parameters: ["maxSteps": 5]
        ),
        TaskBreakdownStrategy(
            type: .hierarchical,
            suitableFor: [.complex, .veryComplex],
            cognitiveLoadRange: [.high, .overload],
            parameters: ["maxDepth": 2]
        )
    ]
}

enum BreakdownType {
    case timeBoxed
    case sequential
    case parallel
    case hierarchical
    case contextual
}

enum TaskComplexity {
    case simple
    case moderate
    case complex
    case veryComplex
}

struct SequencingRule {
    let name: String
    let condition: (AITask) -> Bool
    let transformation: ([AITask]) -> [AITask]
    
    func appliesTo(_ task: AITask) -> Bool {
        return condition(task)
    }
    
    func apply(to tasks: [AITask]) -> [AITask] {
        return transformation(tasks)
    }
    
    nonisolated(unsafe) static let defaultRules: [SequencingRule] = [
        SequencingRule(
            name: "Priority First",
            condition: { _ in true },
            transformation: { tasks in
                tasks.sorted { $0.priority.rawValue > $1.priority.rawValue }
            }
        ),
        SequencingRule(
            name: "Cognitive Load Ascending",
            condition: { task in task.cognitiveLoad == .high || task.cognitiveLoad == .overload },
            transformation: { tasks in
                tasks.sorted { task1, task2 in
                    let load1 = task1.cognitiveLoad
                    let load2 = task2.cognitiveLoad
                    return load1.rawValue < load2.rawValue
                }
            }
        )
    ]
}

struct CognitiveScaffold {
    let name: String
    let condition: (AITask, AITask) -> Bool
    let enhancement: (AITask) -> AITask
    
    func appliesTo(_ task: AITask, originalTask: AITask) -> Bool {
        return condition(task, originalTask)
    }
    
    func apply(to task: AITask) -> AITask {
        return enhancement(task)
    }
    
    nonisolated(unsafe) static let defaultScaffolds: [CognitiveScaffold] = [
        CognitiveScaffold(
            name: "High Cognitive Load Support",
            condition: { task, _ in task.cognitiveLoad == .high || task.cognitiveLoad == .overload },
            enhancement: { task in
                var enhanced = task
                enhanced.personalizedHints.append("Take breaks every 15-20 minutes")
                enhanced.personalizedHints.append("Use a timer to track progress")
                return enhanced
            }
        )
    ]
}

struct SequentialStep {
    let action: String
    let description: String
    let cognitiveLoad: CognitiveLoadLevel
}

struct ParallelBranch {
    let title: String
    let description: String
    let estimatedDuration: TimeInterval
    let cognitiveLoad: CognitiveLoadLevel
}

struct TaskContext {
    let name: String
    let estimatedDuration: TimeInterval
    let cognitiveLoad: CognitiveLoadLevel
}

struct BasicStep {
    let title: String
    let description: String
    let estimatedDuration: TimeInterval
}
