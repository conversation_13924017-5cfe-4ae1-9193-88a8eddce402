import Foundation

// MARK: - OpenAI API Models

/// Request model for OpenAI Chat Completions API
struct OpenAIChatRequest: Codable {
    let model: String
    let messages: [OpenAIChatMessage]
    let temperature: Double
    let maxTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case model, messages, temperature
        case maxTokens = "max_tokens"
    }
}

/// Chat message for OpenAI API
struct OpenAIChatMessage: Codable {
    let role: String
    let content: String
}

/// Response model from OpenAI Chat Completions API
struct OpenAIChatResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [OpenAIChatChoice]
    let usage: OpenAIUsage?
}

/// Choice in OpenAI chat response
struct OpenAIChatChoice: Codable {
    let index: Int
    let message: OpenAIChatMessage
    let finishReason: String?
    
    enum CodingKeys: String, CodingKey {
        case index, message
        case finishReason = "finish_reason"
    }
}

/// Usage statistics from OpenAI API
struct OpenAIUsage: Codable {
    let promptTokens: Int
    let completionTokens: Int
    let totalTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
    }
}

// MARK: - NeuroNexa Specific Models

/// User context for AI task generation
struct UserContext {
    let neurodiversityProfile: String
    let energyLevel: String
    let availableTime: Int
    let preferredTaskTypes: [String]
    let environment: String
    let sensoryPreferences: String
    let attentionPreferences: String
}

/// Personalized content generated by AI
struct PersonalizedContent {
    let tasks: [AITaskData]
    let recommendations: [String]
}

/// AI-generated task data
struct AITaskData {
    let title: String
    let description: String
    let estimatedDuration: Int
    let difficulty: TaskDifficulty
    let steps: [String]
    let sensoryConsiderations: [String]
    let cognitiveSupports: [String]
}

/// Task difficulty levels
enum TaskDifficulty: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
}

/// Cognitive break suggestion
struct BreakSuggestion {
    let type: BreakType
    let title: String
    let description: String
    let duration: Int
    let instructions: [String]
}

/// Types of cognitive breaks
enum BreakType: String, CaseIterable {
    case movement = "movement"
    case breathing = "breathing"
    case sensory = "sensory"
    case mindfulness = "mindfulness"
}

/// UI adaptation suggestions for cognitive load
struct CognitiveAdaptation {
    let colorScheme: AdaptiveColorScheme
    let animationLevel: AnimationLevel
    let textSize: TextSize
    let spacing: SpacingLevel
    let focusMode: Bool
    let distractionReduction: [String]
}

/// Adaptive color schemes
enum AdaptiveColorScheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    case highContrast = "highContrast"
}

/// Animation levels for cognitive load adaptation
enum AnimationLevel: String, CaseIterable {
    case none = "none"
    case reduced = "reduced"
    case normal = "normal"
}

/// Text size options
enum TextSize: String, CaseIterable {
    case small = "small"
    case medium = "medium"
    case large = "large"
    case extraLarge = "extraLarge"
}

/// Spacing levels for UI adaptation
enum SpacingLevel: String, CaseIterable {
    case compact = "compact"
    case normal = "normal"
    case spacious = "spacious"
}

// MARK: - Missing Types from Core Models

/// Motion adaptation for sensory needs
struct MotionAdaptation {
    let reduceMotion: Bool
    let animationDuration: Double
    let transitionStyle: String
}

/// Color adaptation for visual needs
struct ColorAdaptation {
    let contrast: Double
    let saturation: Double
    let brightness: Double
    let colorBlindnessSupport: Bool
}

/// Sound adaptation for auditory needs
struct SoundAdaptation {
    let volume: Double
    let enableHaptics: Bool
    let soundProfile: String
}

/// HRV reading data
struct HRVReading {
    let timestamp: Date
    let value: Double
    let quality: String
}

/// Biometric trend indicators
enum BiometricTrend: String, CaseIterable {
    case improving = "improving"
    case stable = "stable"
    case declining = "declining"
}

/// Anxiety overwhelm detection result
struct AnxietyOverwhelmDetection {
    let level: OverwhelmLevel
    let confidence: Double
    let recommendations: [String]
    let timestamp: Date
}

/// Overwhelm levels
enum OverwhelmLevel: String, CaseIterable {
    case none = "none"
    case mild = "mild"
    case moderate = "moderate"
    case severe = "severe"
}

/// Breathing session result
struct BreathingSessionResult {
    let duration: TimeInterval
    let averageHeartRate: Double
    let hrvImprovement: Double
    let stressReduction: Double
    let completionRate: Double
}

/// Breathing pattern definition
struct BreathingPattern {
    let name: String
    let inhaleCount: Int
    let holdCount: Int
    let exhaleCount: Int
    let cycles: Int
}

/// Calming technique
struct CalmingTechnique {
    let name: String
    let description: String
    let duration: Int
    let instructions: [String]
}

/// Basic task step
struct BasicStep {
    let title: String
    let description: String
    let estimatedTime: Int
    let isOptional: Bool
}

// MARK: - Protocol Definitions

/// Protocol for breathing exercise service
protocol BreathingExerciseServiceProtocol {
    func startSession(pattern: BreathingPattern) async
    func endSession() async -> BreathingSessionResult
    func detectAnxietyOverwhelm(for userProfile: UserProfile) async -> AnxietyOverwhelmDetection
}

/// Protocol for user repository
protocol UserRepositoryProtocol {
    func getCurrentUser() async throws -> UserProfile?
    func saveUser(_ user: UserProfile) async throws
}

/// Protocol for AI task coach service
protocol AITaskCoachServiceProtocol {
    func generatePersonalizedTasks(for user: UserProfile) async throws -> [AITask]
    func analyzeTaskCompletion(_ task: AITask, completion: TaskCompletion) async
    func adaptTasksForCognitiveLoad(_ tasks: [AITask], cognitiveLoad: CognitiveLoadLevel) -> [AITask]
    func suggestTaskBreakdown(_ task: AITask) async -> [AITask]
}

// MARK: - Error Types

/// AI Task Coach specific errors
enum AITaskCoachError: Error, LocalizedError {
    case serviceNotConfigured
    case userProfileMissing
    case taskGenerationFailed
    case invalidTaskData
    
    var errorDescription: String? {
        switch self {
        case .serviceNotConfigured:
            return "AI Task Coach service is not properly configured"
        case .userProfileMissing:
            return "User profile is required for task generation"
        case .taskGenerationFailed:
            return "Failed to generate personalized tasks"
        case .invalidTaskData:
            return "Invalid task data received"
        }
    }
}
