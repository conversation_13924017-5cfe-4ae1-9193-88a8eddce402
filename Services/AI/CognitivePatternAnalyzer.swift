import Combine
import Foundation

// MARK: - Cognitive Pattern Analyzer

/// Analyzes user task completion patterns to improve AI coaching
/// Focuses on neurodiversity-specific patterns and cognitive load optimization
@available(iOS 18.0, *)
@MainActor
class CognitivePatternAnalyzer: ObservableObject {

    // MARK: - Published Properties
    @Published var analysisProgress: Double = 0.0
    @Published var lastAnalysisDate: Date?
    @Published var identifiedPatterns: [CognitivePattern] = []

    // MARK: - Private Properties
    private var completionHistory: [TaskCompletion] = []
    private var patternCache: [String: CognitivePattern] = [:]
    private let maxHistorySize = 1000
    private let minDataPointsForPattern = 5

    // MARK: - Pattern Analysis

    func analyzeCompletion(_ task: AITask, completion: TaskCompletion) async {
        // Add to completion history
        completionHistory.append(completion)

        // Maintain history size limit
        if completionHistory.count > maxHistorySize {
            completionHistory.removeFirst(completionHistory.count - maxHistorySize)
        }

        // Trigger pattern analysis if we have enough data
        if completionHistory.count >= minDataPointsForPattern {
            await performPatternAnalysis()
        }
    }

    func getPatterns() -> [CognitivePattern] {
        return identifiedPatterns
    }

    func getCognitiveInsights(for user: UserProfile) -> CognitiveInsights {
        let recentCompletions = completionHistory.suffix(50)

        return CognitiveInsights(
            averageCognitiveLoad: calculateAverageCognitiveLoad(recentCompletions),
            optimalWorkingTimes: identifyOptimalWorkingTimes(recentCompletions),
            taskDifficultyPatterns: analyzeTaskDifficultyPatterns(recentCompletions),
            completionMethodPreferences: analyzeCompletionMethods(recentCompletions),
            cognitiveLoadTrends: analyzeCognitiveLoadTrends(recentCompletions),
            recommendedBreakFrequency: calculateOptimalBreakFrequency(recentCompletions),
            strengthBasedRecommendations: generateStrengthBasedRecommendations(
                user, recentCompletions)
        )
    }

    // MARK: - Private Analysis Methods

    private func performPatternAnalysis() async {
        analysisProgress = 0.0

        var newPatterns: [CognitivePattern] = []

        // Analyze different pattern types
        newPatterns.append(contentsOf: await analyzeTimeBasedPatterns())
        await updateProgress(0.2)

        newPatterns.append(contentsOf: await analyzeCognitiveLoadPatterns())
        await updateProgress(0.4)

        newPatterns.append(contentsOf: await analyzeTaskTypePatterns())
        await updateProgress(0.6)

        newPatterns.append(contentsOf: await analyzeCompletionMethodPatterns())
        await updateProgress(0.8)

        newPatterns.append(contentsOf: await analyzeExecutiveFunctionPatterns())
        await updateProgress(1.0)

        identifiedPatterns = newPatterns
        lastAnalysisDate = Date()
    }

    private func analyzeTimeBasedPatterns() async -> [CognitivePattern] {
        var patterns: [CognitivePattern] = []

        // Group completions by hour of day
        let hourlyPerformance = Dictionary(grouping: completionHistory) { completion in
            Calendar.current.component(.hour, from: completion.completedAt)
        }

        // Find optimal performance hours
        let performanceByHour = hourlyPerformance.mapValues { completions in
            let avgDifficulty =
                completions.map { Double($0.difficultyRating) }.reduce(0, +)
                / Double(completions.count)
            let avgDuration =
                completions.map { $0.actualDuration }.reduce(0, +) / Double(completions.count)
            return (difficulty: avgDifficulty, duration: avgDuration, count: completions.count)
        }

        // Identify peak performance hours (low difficulty rating, reasonable duration)
        let peakHours =
            performanceByHour
            .filter { $0.value.count >= 3 }  // Minimum data points
            .sorted { $0.value.difficulty < $1.value.difficulty }
            .prefix(3)
            .map { $0.key }

        if !peakHours.isEmpty {
            patterns.append(
                CognitivePattern(
                    type: .timeBasedPerformance,
                    description:
                        "Peak performance hours: \(peakHours.sorted().map { "\($0):00" }.joined(separator: ", "))",
                    confidence: calculateConfidence(dataPoints: peakHours.count),
                    recommendations: [
                        "Schedule challenging tasks during peak hours",
                        "Use peak hours for high-cognitive-load activities",
                        "Plan easier tasks outside peak hours",
                    ],
                    metadata: ["peakHours": peakHours]
                ))
        }

        return patterns
    }

    private func analyzeCognitiveLoadPatterns() async -> [CognitivePattern] {
        var patterns: [CognitivePattern] = []

        // Analyze cognitive load vs. actual performance
        let loadPerformance = Dictionary(grouping: completionHistory) { completion in
            completion.cognitiveLoadExperienced
        }

        for (load, completions) in loadPerformance {
            if completions.count >= minDataPointsForPattern {
                let avgDifficulty =
                    completions.map { Double($0.difficultyRating) }.reduce(0, +)
                    / Double(completions.count)
                let successRate =
                    Double(completions.filter { $0.difficultyRating <= 3 }.count)
                    / Double(completions.count)

                if successRate > 0.8 && avgDifficulty < 3.0 {
                    patterns.append(
                        CognitivePattern(
                            type: .cognitiveLoadOptimization,
                            description:
                                "Performs well under \(load.rawValue.lowercased()) cognitive load",
                            confidence: calculateConfidence(dataPoints: completions.count),
                            recommendations: [
                                "Target \(load.rawValue.lowercased()) cognitive load for optimal performance",
                                "Use this load level for important tasks",
                                "Monitor for signs of load increase",
                            ],
                            metadata: ["optimalLoad": load.rawValue, "successRate": successRate]
                        ))
                }
            }
        }

        return patterns
    }

    private func analyzeTaskTypePatterns() async -> [CognitivePattern] {
        var patterns: [CognitivePattern] = []

        // This would analyze task categories, but we need task category data
        // For now, analyze by estimated vs actual duration patterns

        let durationAccuracy = completionHistory.compactMap { completion -> Double? in
            // We need to get the original task's estimated duration
            // For now, assume a reasonable estimate and calculate accuracy
            let estimatedDuration: TimeInterval = 1800  // 30 minutes default
            let accuracy =
                1.0 - abs(completion.actualDuration - estimatedDuration) / estimatedDuration
            return max(0, accuracy)
        }

        if !durationAccuracy.isEmpty {
            let avgAccuracy = durationAccuracy.reduce(0, +) / Double(durationAccuracy.count)

            if avgAccuracy > 0.7 {
                patterns.append(
                    CognitivePattern(
                        type: .timeEstimationAccuracy,
                        description: "Good time estimation accuracy (\(Int(avgAccuracy * 100))%)",
                        confidence: calculateConfidence(dataPoints: durationAccuracy.count),
                        recommendations: [
                            "Continue using current time estimation strategies",
                            "Can handle tasks with tighter deadlines",
                            "Good candidate for time-sensitive responsibilities",
                        ],
                        metadata: ["accuracy": avgAccuracy]
                    ))
            } else if avgAccuracy < 0.4 {
                patterns.append(
                    CognitivePattern(
                        type: .timeEstimationAccuracy,
                        description:
                            "Time estimation needs improvement (\(Int(avgAccuracy * 100))%)",
                        confidence: calculateConfidence(dataPoints: durationAccuracy.count),
                        recommendations: [
                            "Break tasks into smaller, more predictable chunks",
                            "Use timers and time-tracking tools",
                            "Build in buffer time for tasks",
                            "Practice time estimation with feedback",
                        ],
                        metadata: ["accuracy": avgAccuracy]
                    ))
            }
        }

        return patterns
    }

    private func analyzeCompletionMethodPatterns() async -> [CognitivePattern] {
        var patterns: [CognitivePattern] = []

        let methodPerformance = Dictionary(grouping: completionHistory) { completion in
            completion.completionMethod
        }

        // Find most successful completion method
        let bestMethod =
            methodPerformance
            .mapValues { completions in
                let avgDifficulty =
                    completions.map { Double($0.difficultyRating) }.reduce(0, +)
                    / Double(completions.count)
                return (count: completions.count, avgDifficulty: avgDifficulty)
            }
            .filter { $0.value.count >= 3 }
            .min { $0.value.avgDifficulty < $1.value.avgDifficulty }

        if let (method, performance) = bestMethod {
            patterns.append(
                CognitivePattern(
                    type: .completionMethodPreference,
                    description:
                        "Performs best with \(method.rawValue.lowercased()) completion method",
                    confidence: calculateConfidence(dataPoints: performance.count),
                    recommendations: [
                        "Prioritize \(method.rawValue.lowercased()) completion approach",
                        "Design tasks to support this completion style",
                        "Provide tools that enhance this method",
                    ],
                    metadata: ["preferredMethod": method.rawValue]
                ))
        }

        return patterns
    }

    private func analyzeExecutiveFunctionPatterns() async -> [CognitivePattern] {
        var patterns: [CognitivePattern] = []

        // Analyze task initiation patterns (time between task assignment and start)
        // This would require task assignment timestamps, which we don't have in TaskCompletion
        // For now, analyze completion consistency

        let recentCompletions = completionHistory.suffix(20)
        let completionDates = recentCompletions.map {
            Calendar.current.startOfDay(for: $0.completedAt)
        }
        let uniqueDates = Set(completionDates)

        // Calculate consistency over the date range
        let dateRange =
            completionDates.isEmpty
            ? 1
            : (completionDates.max() ?? Date()).timeIntervalSince(completionDates.min() ?? Date())
        let consistency = Double(uniqueDates.count) / Double(max(1, dateRange / 86400))

        if consistency > 0.7 {
            patterns.append(
                CognitivePattern(
                    type: .executiveFunctionStrength,
                    description: "Shows consistent task completion patterns",
                    confidence: calculateConfidence(dataPoints: recentCompletions.count),
                    recommendations: [
                        "Can handle regular, recurring tasks well",
                        "Good candidate for routine-based productivity",
                        "Can take on tasks requiring consistency",
                    ],
                    metadata: ["consistency": consistency]
                ))
        } else if consistency < 0.3 {
            patterns.append(
                CognitivePattern(
                    type: .executiveFunctionChallenge,
                    description: "Inconsistent task completion patterns detected",
                    confidence: calculateConfidence(dataPoints: recentCompletions.count),
                    recommendations: [
                        "Focus on building consistent routines",
                        "Use external reminders and accountability",
                        "Break large tasks into smaller, daily actions",
                        "Consider executive function coaching",
                    ],
                    metadata: ["consistency": consistency]
                ))
        }

        return patterns
    }

    // MARK: - Helper Methods

    private func calculateAverageCognitiveLoad(_ completions: ArraySlice<TaskCompletion>)
        -> CognitiveLoadLevel
    {
        let loadValues = completions.map { completion in
            switch completion.cognitiveLoadExperienced {
            case .low: return 1
            case .medium: return 2
            case .high: return 3
            case .overload: return 4
            }
        }

        let average = Double(loadValues.reduce(0, +)) / Double(loadValues.count)

        switch average {
        case 0..<1.5: return .low
        case 1.5..<2.5: return .medium
        case 2.5..<3.5: return .high
        default: return .overload
        }
    }

    private func identifyOptimalWorkingTimes(_ completions: ArraySlice<TaskCompletion>) -> [Int] {
        let hourlyPerformance = Dictionary(grouping: completions) { completion in
            Calendar.current.component(.hour, from: completion.completedAt)
        }

        return
            hourlyPerformance
            .filter { $0.value.count >= 2 }
            .mapValues { completions in
                completions.map { Double($0.difficultyRating) }.reduce(0, +)
                    / Double(completions.count)
            }
            .sorted { $0.value < $1.value }
            .prefix(3)
            .map { $0.key }
            .sorted()
    }

    private func analyzeTaskDifficultyPatterns(_ completions: ArraySlice<TaskCompletion>)
        -> [String: Double]
    {
        let difficulties = completions.map { Double($0.difficultyRating) }
        let average = difficulties.reduce(0, +) / Double(difficulties.count)
        let variance =
            difficulties.map { pow($0 - average, 2) }.reduce(0, +) / Double(difficulties.count)

        return [
            "averageDifficulty": average,
            "difficultyVariance": variance,
            "consistencyScore": max(0, 1.0 - (variance / 4.0)),  // Normalize variance to 0-1 scale
        ]
    }

    private func analyzeCompletionMethods(_ completions: ArraySlice<TaskCompletion>)
        -> [CompletionMethod: Int]
    {
        return Dictionary(grouping: completions) { $0.completionMethod }
            .mapValues { $0.count }
    }

    private func analyzeCognitiveLoadTrends(_ completions: ArraySlice<TaskCompletion>) -> [String:
        Any]
    {
        let loads = completions.map { completion in
            switch completion.cognitiveLoadExperienced {
            case .low: return 1
            case .medium: return 2
            case .high: return 3
            case .overload: return 4
            }
        }

        // Simple trend analysis (positive = increasing load, negative = decreasing)
        let trend =
            loads.count > 1 ? Double(loads.last! - loads.first!) / Double(loads.count - 1) : 0.0

        return [
            "trend": trend,
            "isIncreasing": trend > 0.1,
            "isDecreasing": trend < -0.1,
            "isStable": abs(trend) <= 0.1,
        ]
    }

    private func calculateOptimalBreakFrequency(_ completions: ArraySlice<TaskCompletion>)
        -> TimeInterval
    {
        let durations = completions.map { $0.actualDuration }
        let averageDuration = durations.reduce(0, +) / Double(durations.count)

        // Suggest breaks based on average task duration and difficulty
        let difficulties = completions.map { Double($0.difficultyRating) }
        let averageDifficulty = difficulties.reduce(0, +) / Double(difficulties.count)

        // Higher difficulty = more frequent breaks
        let breakFrequency = averageDuration / (1.0 + averageDifficulty / 2.0)

        return max(900, min(3600, breakFrequency))  // Between 15 minutes and 1 hour
    }

    private func generateStrengthBasedRecommendations(
        _ user: UserProfile, _ completions: ArraySlice<TaskCompletion>
    ) -> [String] {
        var recommendations: [String] = []

        // Analyze performance in context of user's cognitive strengths
        for strength in user.cognitiveProfile.cognitiveStrengths {
            switch strength {
            case .hyperfocus:
                let longTasks = completions.filter { $0.actualDuration > 1800 }  // > 30 minutes
                if !longTasks.isEmpty {
                    let avgDifficulty =
                        longTasks.map { Double($0.difficultyRating) }.reduce(0, +)
                        / Double(longTasks.count)
                    if avgDifficulty < 3.0 {
                        recommendations.append("Leverage hyperfocus for longer, complex tasks")
                    }
                }

            case .detailOriented:
                recommendations.append("Assign detail-heavy tasks that benefit from thoroughness")

            case .patternRecognition:
                recommendations.append("Use pattern-based learning and problem-solving approaches")

            default:
                break
            }
        }

        return recommendations
    }

    private func calculateConfidence(dataPoints: Int) -> Double {
        // Simple confidence calculation based on data points
        return min(1.0, Double(dataPoints) / Double(minDataPointsForPattern * 3))
    }

    private func updateProgress(_ progress: Double) async {
        analysisProgress = progress
    }
}

// MARK: - Supporting Types

struct CognitivePattern {
    let type: CognitivePatternType
    let description: String
    let confidence: Double  // 0.0 to 1.0
    let recommendations: [String]
    let metadata: [String: Any]
}

enum CognitivePatternType {
    case timeBasedPerformance
    case cognitiveLoadOptimization
    case timeEstimationAccuracy
    case completionMethodPreference
    case executiveFunctionStrength
    case executiveFunctionChallenge
}

struct CognitiveInsights {
    let averageCognitiveLoad: CognitiveLoadLevel
    let optimalWorkingTimes: [Int]  // Hours of day
    let taskDifficultyPatterns: [String: Double]
    let completionMethodPreferences: [CompletionMethod: Int]
    let cognitiveLoadTrends: [String: Any]
    let recommendedBreakFrequency: TimeInterval
    let strengthBasedRecommendations: [String]
}
