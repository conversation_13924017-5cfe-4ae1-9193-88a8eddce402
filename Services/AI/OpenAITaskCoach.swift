import Foundation
import Combine

// MARK: - OpenAI Task Coach Service

/// OpenAI-powered task coaching service with neurodiversity-first approach
/// Provides personalized task generation and cognitive support
@available(iOS 18.0, *)
class OpenAITaskCoachService: ObservableObject {
    
    // MARK: - Configuration
    private let apiKey: String
    private let baseURL = "https://api.openai.com/v1"
    private let model = "gpt-4"
    
    // MARK: - Publishers
    @Published var isInitialized = false
    @Published var lastError: Error?
    
    // MARK: - Private Properties
    private var urlSession: URLSession
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        // Get API key from environment or configuration
        self.apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] ?? ""
        
        // Configure URL session
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0
        self.urlSession = URLSession(configuration: config)
    }
    
    // MARK: - Public Methods
    
    /// Initialize the OpenAI service
    func initialize() async {
        guard !apiKey.isEmpty else {
            await MainActor.run {
                self.lastError = OpenAIError.missingAPIKey
                self.isInitialized = false
            }
            return
        }
        
        // Test API connection
        do {
            _ = try await testConnection()
            await MainActor.run {
                self.isInitialized = true
                self.lastError = nil
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.isInitialized = false
            }
        }
    }
    
    /// Generate personalized tasks for a user
    func generatePersonalizedTasks(
        for userContext: UserContext,
        count: Int = 5
    ) async throws -> PersonalizedContent {
        guard isInitialized else {
            throw OpenAIError.serviceNotInitialized
        }
        
        let prompt = buildTaskGenerationPrompt(for: userContext, count: count)
        let response = try await sendChatCompletion(prompt: prompt)
        
        return try parseTaskResponse(response)
    }
    
    /// Generate cognitive break suggestions
    func suggestCognitiveBreak(
        for userContext: UserContext,
        currentLoad: CognitiveLoadLevel
    ) async throws -> BreakSuggestion {
        guard isInitialized else {
            throw OpenAIError.serviceNotInitialized
        }
        
        let prompt = buildBreakSuggestionPrompt(for: userContext, load: currentLoad)
        let response = try await sendChatCompletion(prompt: prompt)
        
        return try parseBreakSuggestion(response)
    }
    
    /// Adapt UI for cognitive load
    func adaptUIForCognitiveLoad(
        _ level: CognitiveLoadLevel,
        userContext: UserContext
    ) async throws -> CognitiveAdaptation {
        guard isInitialized else {
            throw OpenAIError.serviceNotInitialized
        }
        
        let prompt = buildUIAdaptationPrompt(for: level, context: userContext)
        let response = try await sendChatCompletion(prompt: prompt)
        
        return try parseUIAdaptation(response)
    }
    
    // MARK: - Private Methods
    
    private func testConnection() async throws -> Bool {
        let url = URL(string: "\(baseURL)/models")!
        var request = URLRequest(url: url)
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (_, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw OpenAIError.connectionFailed
        }
        
        return true
    }
    
    private func sendChatCompletion(prompt: String) async throws -> String {
        let url = URL(string: "\(baseURL)/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = OpenAIChatRequest(
            model: model,
            messages: [
                OpenAIChatMessage(role: "system", content: systemPrompt),
                OpenAIChatMessage(role: "user", content: prompt)
            ],
            temperature: 0.7,
            maxTokens: 1000
        )
        
        request.httpBody = try JSONEncoder().encode(requestBody)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw OpenAIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            throw OpenAIError.apiError(httpResponse.statusCode)
        }
        
        let chatResponse = try JSONDecoder().decode(OpenAIChatResponse.self, from: data)
        
        guard let content = chatResponse.choices.first?.message.content else {
            throw OpenAIError.emptyResponse
        }
        
        return content
    }
    
    private var systemPrompt: String {
        """
        You are a neurodiversity-first AI assistant specialized in supporting individuals with ADHD, autism, 
        and other neurodivergent conditions. Your responses should:
        
        1. Be clear, concise, and well-structured
        2. Consider executive function challenges
        3. Provide sensory-friendly suggestions
        4. Break down complex tasks into manageable steps
        5. Offer multiple approaches to accommodate different cognitive styles
        6. Be encouraging and non-judgmental
        7. Focus on strengths and capabilities
        
        Always respond in valid JSON format when generating structured data.
        """
    }
    
    private func buildTaskGenerationPrompt(for context: UserContext, count: Int) -> String {
        """
        Generate \(count) personalized tasks for a neurodivergent user with the following context:
        
        Neurodiversity Profile: \(context.neurodiversityProfile)
        Current Energy Level: \(context.energyLevel)
        Available Time: \(context.availableTime) minutes
        Preferred Task Types: \(context.preferredTaskTypes.joined(separator: ", "))
        Current Environment: \(context.environment)
        
        Please provide tasks that are:
        - Appropriately sized for the available time
        - Matched to current energy level
        - Considerate of sensory preferences
        - Broken down into clear, actionable steps
        
        Return as JSON with this structure:
        {
          "tasks": [
            {
              "title": "Task title",
              "description": "Clear description",
              "estimatedDuration": 15,
              "difficulty": "low|medium|high",
              "steps": ["Step 1", "Step 2"],
              "sensoryConsiderations": ["consideration1"],
              "cognitiveSupports": ["support1"]
            }
          ]
        }
        """
    }
    
    private func buildBreakSuggestionPrompt(for context: UserContext, load: CognitiveLoadLevel) -> String {
        """
        Suggest a cognitive break for a user experiencing \(load) cognitive load.
        
        User Context:
        - Sensory Preferences: \(context.sensoryPreferences)
        - Current Environment: \(context.environment)
        - Available Time: \(context.availableTime) minutes
        
        Provide a break suggestion that helps reduce cognitive load and restore focus.
        
        Return as JSON:
        {
          "type": "movement|breathing|sensory|mindfulness",
          "title": "Break title",
          "description": "What to do",
          "duration": 5,
          "instructions": ["Step 1", "Step 2"],
          "benefits": ["benefit1"]
        }
        """
    }
    
    private func buildUIAdaptationPrompt(for level: CognitiveLoadLevel, context: UserContext) -> String {
        """
        Suggest UI adaptations for \(level) cognitive load.
        
        User needs:
        - Sensory sensitivities: \(context.sensoryPreferences)
        - Attention preferences: \(context.attentionPreferences)
        
        Return JSON:
        {
          "colorScheme": "light|dark|highContrast",
          "animationLevel": "none|reduced|normal",
          "textSize": "small|medium|large|extraLarge",
          "spacing": "compact|normal|spacious",
          "focusMode": true,
          "distractionReduction": ["suggestion1"]
        }
        """
    }
    
    private func parseTaskResponse(_ response: String) throws -> PersonalizedContent {
        // Implementation for parsing task response
        // This would parse the JSON response and create PersonalizedContent
        let data = response.data(using: .utf8) ?? Data()
        // Simplified implementation - would need proper JSON parsing
        return PersonalizedContent(tasks: [], recommendations: [])
    }
    
    private func parseBreakSuggestion(_ response: String) throws -> BreakSuggestion {
        // Implementation for parsing break suggestion
        return BreakSuggestion(
            type: .breathing,
            title: "Deep Breathing",
            description: "Take slow, deep breaths",
            duration: 5,
            instructions: ["Breathe in for 4 counts", "Hold for 4 counts", "Breathe out for 6 counts"]
        )
    }
    
    private func parseUIAdaptation(_ response: String) throws -> CognitiveAdaptation {
        // Implementation for parsing UI adaptation
        return CognitiveAdaptation(
            colorScheme: .light,
            animationLevel: .reduced,
            textSize: .medium,
            spacing: .normal,
            focusMode: true,
            distractionReduction: []
        )
    }
}

// MARK: - Supporting Types

enum OpenAIError: Error, LocalizedError {
    case missingAPIKey
    case serviceNotInitialized
    case connectionFailed
    case invalidResponse
    case apiError(Int)
    case emptyResponse
    case parsingError
    
    var errorDescription: String? {
        switch self {
        case .missingAPIKey:
            return "OpenAI API key is missing"
        case .serviceNotInitialized:
            return "OpenAI service is not initialized"
        case .connectionFailed:
            return "Failed to connect to OpenAI API"
        case .invalidResponse:
            return "Invalid response from OpenAI API"
        case .apiError(let code):
            return "OpenAI API error: \(code)"
        case .emptyResponse:
            return "Empty response from OpenAI API"
        case .parsingError:
            return "Failed to parse OpenAI response"
        }
    }
}
