2025-07-09 01:42:55,755 - INFO - Found 12 iOS 26 simulators
2025-07-09 01:42:55,756 - INFO - Initializing MCP tools integration...
2025-07-09 01:42:55,756 - INFO - MCP tools integration enabled
2025-07-09 01:43:47,125 - INFO - Found 12 iOS 26 simulators
2025-07-09 01:43:47,126 - INFO - Initializing MCP tools integration...
2025-07-09 01:43:47,126 - INFO - MCP tools integration enabled
2025-07-09 01:43:47,126 - INFO - Starting comprehensive enterprise test suite...
2025-07-09 01:43:47,126 - INFO - Setting up enterprise testing environment...
2025-07-09 01:43:47,126 - INFO - Cleaning derived data...
2025-07-09 01:43:48,241 - INFO - Cleaning project...
2025-07-09 01:43:51,088 - INFO - Resetting simulators...
2025-07-09 01:44:10,407 - INFO - Test environment setup complete
2025-07-09 01:44:10,407 - INFO - Running unit tests...
2025-07-09 01:44:10,408 - INFO - Running MCP-enhanced unit tests on iPhone 16 Pro...
2025-07-09 01:44:10,408 - ERROR - Failed to call MCP tool swift_lint_analysis: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:10,409 - ERROR - Failed to call MCP tool ios_compatibility_check: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:10,409 - ERROR - Failed to call MCP tool accessibility_audit: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:10,409 - ERROR - Failed to call MCP tool performance_analysis: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:10,409 - INFO - MCP-enhanced unit tests completed
2025-07-09 01:44:12,319 - INFO - Unit tests completed: 0 passed, 0 failed
2025-07-09 01:44:12,320 - INFO - Running UI tests...
2025-07-09 01:44:14,029 - INFO - UI tests completed: 0 passed, 0 failed
2025-07-09 01:44:14,029 - INFO - Running accessibility tests...
2025-07-09 01:44:17,343 - INFO - Accessibility tests completed: 0 passed, 0 failed
2025-07-09 01:44:17,343 - INFO - Running performance tests...
2025-07-09 01:44:19,213 - INFO - Performance tests completed: 0 passed, 0 failed
2025-07-09 01:44:19,213 - INFO - Running security tests...
2025-07-09 01:44:19,213 - INFO - Checking for hardcoded secrets...
2025-07-09 01:44:19,348 - INFO - Validating privacy manifest...
2025-07-09 01:44:19,348 - INFO - Validating network security...
2025-07-09 01:44:19,349 - INFO - Security tests completed: 2 passed, 1 failed
2025-07-09 01:44:19,349 - INFO - Running cross-device compatibility tests...
2025-07-09 01:44:19,349 - INFO - Testing on iPhone 16 Pro
2025-07-09 01:44:19,349 - INFO - Running unit tests...
2025-07-09 01:44:19,350 - INFO - Running MCP-enhanced unit tests on iPhone 16 Pro...
2025-07-09 01:44:19,350 - ERROR - Failed to call MCP tool swift_lint_analysis: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,351 - ERROR - Failed to call MCP tool ios_compatibility_check: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,351 - ERROR - Failed to call MCP tool accessibility_audit: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,351 - ERROR - Failed to call MCP tool performance_analysis: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,351 - INFO - MCP-enhanced unit tests completed
2025-07-09 01:44:19,766 - INFO - Unit tests completed: 0 passed, 0 failed
2025-07-09 01:44:19,766 - INFO - Testing on iPhone 16
2025-07-09 01:44:19,766 - INFO - Running unit tests...
2025-07-09 01:44:19,767 - INFO - Running MCP-enhanced unit tests on iPhone 16...
2025-07-09 01:44:19,767 - ERROR - Failed to call MCP tool swift_lint_analysis: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,767 - ERROR - Failed to call MCP tool ios_compatibility_check: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,767 - ERROR - Failed to call MCP tool accessibility_audit: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,767 - ERROR - Failed to call MCP tool performance_analysis: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-07-09 01:44:19,768 - INFO - MCP-enhanced unit tests completed
2025-07-09 01:44:20,170 - INFO - Unit tests completed: 0 passed, 0 failed
2025-07-09 01:44:20,171 - WARNING - Device iPad Pro (12.9-inch) (7th generation) not available
2025-07-09 01:44:20,171 - WARNING - Device iPad Air (6th generation) not available
2025-07-09 01:44:20,171 - INFO - Generating test report...
2025-07-09 01:44:20,173 - INFO - Enterprise test suite completed successfully
